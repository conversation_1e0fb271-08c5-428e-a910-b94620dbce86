@echo off
echo 🔨 编译真实微信协议版本...

REM 检查Go环境
where go >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Go未安装或不在PATH中
    echo 请先安装Go 1.19或更高版本
    pause
    exit /b 1
)

echo ✅ 检测到Go环境
go version

REM 设置环境变量
set CGO_ENABLED=0
set GOOS=linux
set GOARCH=amd64
set GOPROXY=https://goproxy.cn,direct

echo ✅ 环境变量设置完成

REM 清理旧文件
if exist myapp-linux-bot3-real del myapp-linux-bot3-real
if exist go.sum del go.sum

echo ✅ 清理完成

REM 下载依赖
echo 📦 下载依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo ❌ 依赖下载失败
    pause
    exit /b 1
)

echo ✅ 依赖下载成功

REM 编译程序
echo 🔨 编译真实微信协议版本...
go build -ldflags="-s -w" -o myapp-linux-bot3-real main_complete_api.go
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo ✅ 编译成功！

REM 检查文件
if exist myapp-linux-bot3-real (
    echo 📋 可执行文件信息：
    dir myapp-linux-bot3-real
    echo.
    echo 🎉 真实微信协议版本编译完成！
    echo.
    echo 📋 使用说明：
    echo    在Linux环境中运行: ./myapp-linux-bot3-real
    echo    测试二维码: curl http://localhost:8057/login/GetQrCodeUrl
    echo.
    echo 🔍 特性：
    echo    ✅ 使用真实的微信API端点
    echo    ✅ 生成真实可用的二维码
    echo    ✅ 真实的登录状态检查
    echo    ✅ 完全兼容Node.js客户端
    echo.
) else (
    echo ❌ 可执行文件生成失败
    pause
    exit /b 1
)

pause
