# MyApp-Linux 修复版本 Docker 构建文件
# 多阶段构建：编译阶段 + 运行阶段

# 第一阶段：编译阶段
FROM golang:1.19-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache git ca-certificates tzdata

# 复制go.mod和go.sum文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 编译应用程序
# 使用静态链接，禁用CGO，优化二进制文件大小
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o myapp-linux-fixed .

# 第二阶段：运行阶段
FROM alpine:latest

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata

# 创建非root用户
RUN addgroup -g 1001 -S myapp && \
    adduser -u 1001 -S myapp -G myapp

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件和配置
COPY --from=builder /app/myapp-linux-fixed .
COPY --from=builder /app/assets ./assets
COPY --from=builder /app/config.json .

# 设置文件权限
RUN chown -R myapp:myapp /app && \
    chmod +x myapp-linux-fixed

# 切换到非root用户
USER myapp

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# 启动应用
CMD ["./myapp-linux-fixed"]
