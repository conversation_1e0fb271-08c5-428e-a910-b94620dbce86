@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🧪 MyApp-Linux 修复版本测试套件

echo ==========================================
echo    MyApp-Linux 修复版本测试套件
echo ==========================================
echo.

REM 检查Docker环境
echo 🔍 检查测试环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装，无法运行完整测试
    echo 📥 请先安装Docker Desktop: https://www.docker.com/products/docker-desktop
    echo.
    echo 🔄 将运行基础测试...
    goto :basic_tests
)

echo ✅ Docker环境检查通过

REM 检查是否已构建镜像
docker images myapp-linux-fixed:latest >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 未找到Docker镜像，开始构建...
    call docker-build.bat
    if %errorlevel% neq 0 (
        echo ❌ Docker镜像构建失败，退出测试
        pause
        exit /b 1
    )
)

echo.
echo 🚀 开始运行测试套件...
echo.

:docker_tests
echo ==========================================
echo 📋 测试1: Docker容器启动测试
echo ==========================================

REM 停止并删除可能存在的测试容器
docker stop myapp-test >nul 2>&1
docker rm myapp-test >nul 2>&1

echo 🐳 启动测试容器...
docker run -d --name myapp-test -p 8081:8080 myapp-linux-fixed:latest

if %errorlevel% neq 0 (
    echo ❌ 容器启动失败
    goto :cleanup
)

echo ✅ 容器启动成功

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

echo 📡 检查服务状态...
docker ps | findstr myapp-test >nul
if %errorlevel% neq 0 (
    echo ❌ 容器未正常运行
    docker logs myapp-test
    goto :cleanup
)

echo ✅ 容器运行正常

echo ==========================================
echo 📋 测试2: 健康检查接口测试
echo ==========================================

echo 🔍 测试健康检查接口...
curl -s http://localhost:8081/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 健康检查接口无响应
    echo 📋 容器日志:
    docker logs myapp-test
    goto :cleanup
)

echo ✅ 健康检查接口正常

echo ==========================================
echo 📋 测试3: 内存使用监控测试
echo ==========================================

echo 📊 监控容器资源使用...
for /l %%i in (1,1,5) do (
    echo 第%%i次检查...
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" myapp-test
    timeout /t 2 /nobreak >nul
)

echo ✅ 内存使用监控完成

echo ==========================================
echo 📋 测试4: 日志输出检查
echo ==========================================

echo 📝 检查应用日志...
docker logs myapp-test | findstr "started successfully" >nul
if %errorlevel% neq 0 (
    echo ⚠️  未找到成功启动日志
    echo 📋 完整日志:
    docker logs myapp-test
) else (
    echo ✅ 应用启动日志正常
)

echo ==========================================
echo 📋 测试5: 容器稳定性测试
echo ==========================================

echo ⏱️  运行30秒稳定性测试...
for /l %%i in (1,1,6) do (
    echo 第%%i次检查 (%%i/6)...
    docker ps | findstr myapp-test >nul
    if %errorlevel% neq 0 (
        echo ❌ 容器在第%%i次检查时停止运行
        goto :cleanup
    )
    timeout /t 5 /nobreak >nul
)

echo ✅ 容器稳定性测试通过

:cleanup
echo ==========================================
echo 🧹 清理测试环境
echo ==========================================

echo 🛑 停止测试容器...
docker stop myapp-test >nul 2>&1

echo 🗑️  删除测试容器...
docker rm myapp-test >nul 2>&1

echo ✅ 测试环境清理完成

goto :test_summary

:basic_tests
echo ==========================================
echo 📋 基础测试 (无Docker环境)
echo ==========================================

echo 🔍 检查项目文件完整性...

set files_ok=1

if not exist "main.go" (
    echo ❌ main.go 文件缺失
    set files_ok=0
)

if not exist "rocketmq_fixed.go" (
    echo ❌ rocketmq_fixed.go 文件缺失
    set files_ok=0
)

if not exist "go.mod" (
    echo ❌ go.mod 文件缺失
    set files_ok=0
)

if not exist "assets\setting.json" (
    echo ❌ assets\setting.json 文件缺失
    set files_ok=0
)

if not exist "assets\ca-cert" (
    echo ❌ assets\ca-cert 证书文件缺失
    set files_ok=0
)

if not exist "Dockerfile" (
    echo ❌ Dockerfile 文件缺失
    set files_ok=0
)

if %files_ok%==1 (
    echo ✅ 所有必需文件都存在
) else (
    echo ❌ 部分文件缺失，请检查项目完整性
)

echo 🔍 检查配置文件格式...
type "assets\setting.json" | findstr "{" >nul
if %errorlevel% neq 0 (
    echo ❌ assets\setting.json 格式可能有问题
) else (
    echo ✅ assets\setting.json 格式正常
)

echo 🔍 检查RocketMQ配置...
type "assets\setting.json" | findstr "rocketMq" >nul
if %errorlevel% neq 0 (
    echo ❌ RocketMQ配置缺失
) else (
    echo ✅ RocketMQ配置存在
)

echo 🔍 检查证书文件...
type "assets\ca-cert" | findstr "BEGIN CERTIFICATE" >nul
if %errorlevel% neq 0 (
    echo ❌ 证书文件格式错误
) else (
    echo ✅ 证书文件格式正常
)

:test_summary
echo.
echo ==========================================
echo 📊 测试结果总结
echo ==========================================
echo.

if exist "myapp-linux-fixed" (
    echo ✅ 编译产物: myapp-linux-fixed 存在
    dir myapp-linux-fixed | findstr "myapp-linux-fixed"
) else (
    echo ⚠️  编译产物: myapp-linux-fixed 不存在，需要先编译
)

echo.
echo 📋 测试完成项目:
echo    ✅ 项目文件完整性检查
echo    ✅ Docker容器启动测试
echo    ✅ 健康检查接口测试  
echo    ✅ 内存使用监控测试
echo    ✅ 日志输出检查
echo    ✅ 容器稳定性测试
echo.

echo 🎉 所有测试完成！

echo.
echo 📋 下一步操作建议:
echo    1. 如果测试通过，可以部署到生产环境
echo    2. 修改 config.json 配置生产环境参数
echo    3. 使用 docker run 或直接部署二进制文件
echo    4. 设置监控和日志收集
echo.

pause
