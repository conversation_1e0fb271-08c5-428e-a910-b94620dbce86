/**
 * PM2 生态系统配置文件
 * 用于管理微信机器人和Go程序内存监控
 */

module.exports = {
  apps: [
    {
      // 主应用：集成Go监控的统一启动器
      name: 'wechat-bot-unified',
      script: 'start-unified-with-go-monitor.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '300M',
      node_args: '--expose-gc',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      log_file: 'logs/pm2-wechat-bot-unified.log',
      out_file: 'logs/pm2-wechat-bot-unified-out.log',
      error_file: 'logs/pm2-wechat-bot-unified-error.log',
      time: true,
      merge_logs: true,
      max_restarts: 10,
      min_uptime: '10s',
      restart_delay: 5000
    },
    {
      // 独立的Go程序内存监控器
      name: 'go-memory-monitor',
      script: 'start-with-memory-monitor.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '100M',
      env: {
        NODE_ENV: 'production'
      },
      env_development: {
        NODE_ENV: 'development'
      },
      log_file: 'logs/pm2-go-monitor.log',
      out_file: 'logs/pm2-go-monitor-out.log',
      error_file: 'logs/pm2-go-monitor-error.log',
      time: true,
      merge_logs: true,
      max_restarts: 5,
      min_uptime: '30s',
      restart_delay: 10000
    },
    {
      // 仅优化版Bot（不含Go监控）
      name: 'wechat-bot-optimized',
      script: 'start-optimized.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '250M',
      node_args: '--expose-gc',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      log_file: 'logs/pm2-wechat-bot-optimized.log',
      out_file: 'logs/pm2-wechat-bot-optimized-out.log',
      error_file: 'logs/pm2-wechat-bot-optimized-error.log',
      time: true,
      merge_logs: true,
      max_restarts: 10,
      min_uptime: '10s',
      restart_delay: 5000,
      // 默认不启动，需要手动启动
      autorestart: false
    }
  ],

  // 部署配置（可选）
  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: 'your-repository-url',
      path: '/var/www/wechat-bot',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    },
    development: {
      user: 'deploy',
      host: 'dev-server.com',
      ref: 'origin/develop',
      repo: 'your-repository-url',
      path: '/var/www/wechat-bot-dev',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env development',
      'pre-setup': ''
    }
  }
};
