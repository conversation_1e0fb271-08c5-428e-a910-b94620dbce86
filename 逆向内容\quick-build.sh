#!/bin/bash

# 快速编译脚本 - 最简单的解决方案
# 直接使用中国代理编译原始代码

set -e

echo "🚀 快速编译第三个微信机器人..."

# 设置中国代理
export GOPROXY=https://goproxy.cn,direct
export GOSUMDB=sum.golang.google.cn
export GO111MODULE=on

# 设置编译参数
export CGO_ENABLED=0
export GOOS=linux
export GOARCH=amd64

echo "🌐 使用中国代理: $GOPROXY"

# 清理环境
echo "🧹 清理编译环境..."
rm -f go.sum myapp-linux-bot3

# 下载依赖
echo "📦 下载依赖..."
go mod tidy

# 编译
echo "🔨 编译程序..."
go build -ldflags="-s -w" -o myapp-linux-bot3 .

# 检查结果
if [ -f "myapp-linux-bot3" ]; then
    echo "✅ 编译成功！"
    ls -lh myapp-linux-bot3
    
    echo ""
    echo "🤖 第三个微信机器人已就绪:"
    echo "   📡 端口: 8057"
    echo "   🔑 机器人ID: wechat_bot_3"
    echo "   🗄️ Redis数据库: Db 3"
    echo "   🗃️ MySQL数据库: wechat_bot3"
    echo "   📨 RocketMQ主题: wx_sync_msg_topic_bot3"
    echo ""
    echo "🚀 运行命令:"
    echo "   ./myapp-linux-bot3"
    echo ""
    echo "🔍 验证命令:"
    echo "   curl http://localhost:8057/health"
    echo ""
    
else
    echo "❌ 编译失败！"
    exit 1
fi

echo "🎉 快速编译完成！"
