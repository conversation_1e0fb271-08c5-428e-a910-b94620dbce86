/**
 * 消息去重中间件
 * 基于Redis缓存实现智能消息去重，防止重复处理历史消息
 */

class MessageDeduplication {
  constructor(syncManager, options = {}) {
    this.syncManager = syncManager;
    this.options = {
      // 去重策略配置
      strategy: options.strategy || 'smart', // 'strict', 'smart', 'loose'
      
      // 时间窗口配置
      timeWindow: {
        enabled: options.timeWindow?.enabled !== false,
        duration: options.timeWindow?.duration || 5 * 60 * 1000, // 5分钟
        maxAge: options.timeWindow?.maxAge || 24 * 60 * 60 * 1000 // 24小时
      },
      
      // 消息类型过滤
      messageTypes: {
        enabled: options.messageTypes?.enabled !== false,
        allowedTypes: options.messageTypes?.allowedTypes || [1, 3, 34, 47, 49], // 文本、图片、语音、表情、链接等
        skipTypes: options.messageTypes?.skipTypes || [10000] // 系统消息等
      },
      
      // 批量处理配置
      batch: {
        enabled: options.batch?.enabled !== false,
        size: options.batch?.size || 50,
        timeout: options.batch?.timeout || 1000
      }
    };
    
    this.logger = console;
    this.pendingBatch = [];
    this.batchTimer = null;
    this.stats = {
      totalProcessed: 0,
      duplicatesFound: 0,
      batchesProcessed: 0,
      lastResetTime: Date.now()
    };
  }

  /**
   * 检查消息是否应该被处理（主要入口）
   * @param {Object} message - 消息对象
   * @returns {Promise<{shouldProcess: boolean, reason: string, metadata: Object}>}
   */
  async shouldProcessMessage(message) {
    try {
      const messageId = this.extractMessageId(message);
      const messageType = this.extractMessageType(message);
      const messageTime = this.extractMessageTime(message);
      
      this.stats.totalProcessed++;
      
      // 1. 基本验证
      if (!messageId) {
        return {
          shouldProcess: false,
          reason: 'invalid_message_id',
          metadata: { messageId, messageType, messageTime }
        };
      }
      
      // 2. 消息类型过滤
      if (this.options.messageTypes.enabled) {
        if (this.options.messageTypes.skipTypes.includes(messageType)) {
          return {
            shouldProcess: false,
            reason: 'message_type_skipped',
            metadata: { messageId, messageType, messageTime }
          };
        }
        
        if (this.options.messageTypes.allowedTypes.length > 0 && 
            !this.options.messageTypes.allowedTypes.includes(messageType)) {
          return {
            shouldProcess: false,
            reason: 'message_type_not_allowed',
            metadata: { messageId, messageType, messageTime }
          };
        }
      }
      
      // 3. 时间窗口检查
      if (this.options.timeWindow.enabled && messageTime) {
        const now = Date.now();
        const messageAge = now - messageTime;
        
        if (messageAge > this.options.timeWindow.maxAge) {
          return {
            shouldProcess: false,
            reason: 'message_too_old',
            metadata: { messageId, messageType, messageTime, messageAge }
          };
        }
      }
      
      // 4. 去重检查
      const isDuplicate = await this.syncManager.isMessageProcessed(messageId);
      
      if (isDuplicate) {
        this.stats.duplicatesFound++;
        
        // 根据策略决定是否处理
        const strategy = this.getDeduplicationStrategy(message, messageTime);
        
        if (strategy.shouldSkip) {
          return {
            shouldProcess: false,
            reason: 'duplicate_message',
            metadata: { 
              messageId, 
              messageType, 
              messageTime,
              strategy: strategy.name,
              details: strategy.details
            }
          };
        }
      }
      
      // 5. 消息应该被处理
      return {
        shouldProcess: true,
        reason: 'valid_new_message',
        metadata: { messageId, messageType, messageTime }
      };
      
    } catch (error) {
      this.logger.error(`[消息去重] 检查消息处理失败: ${error.message}`);
      
      // 出错时默认允许处理，避免丢失重要消息
      return {
        shouldProcess: true,
        reason: 'error_default_allow',
        metadata: { error: error.message }
      };
    }
  }

  /**
   * 标记消息为已处理
   * @param {Object} message - 消息对象
   * @param {Object} processingResult - 处理结果
   */
  async markMessageProcessed(message, processingResult = {}) {
    try {
      const messageId = this.extractMessageId(message);
      const messageType = this.extractMessageType(message);
      const messageTime = this.extractMessageTime(message);
      
      if (!messageId) return;
      
      const metadata = {
        messageType,
        messageTime,
        processedAt: Date.now(),
        processingResult: processingResult.success ? 'success' : 'failed',
        ...processingResult
      };
      
      if (this.options.batch.enabled) {
        // 批量处理
        this.pendingBatch.push({ messageId, metadata });
        
        if (this.pendingBatch.length >= this.options.batch.size) {
          await this.processBatch();
        } else {
          this.scheduleBatchProcessing();
        }
      } else {
        // 立即处理
        await this.syncManager.markMessageProcessed(messageId, metadata);
      }
      
    } catch (error) {
      this.logger.error(`[消息去重] 标记消息处理失败: ${error.message}`);
    }
  }

  /**
   * 获取去重策略
   * @param {Object} message - 消息对象
   * @param {number} messageTime - 消息时间
   * @returns {Object} 策略结果
   */
  getDeduplicationStrategy(message, messageTime) {
    const now = Date.now();
    const messageAge = messageTime ? now - messageTime : 0;
    
    switch (this.options.strategy) {
      case 'strict':
        // 严格模式：所有重复消息都跳过
        return {
          shouldSkip: true,
          name: 'strict',
          details: 'All duplicates are skipped'
        };
        
      case 'loose':
        // 宽松模式：只跳过最近的重复消息
        return {
          shouldSkip: messageAge < this.options.timeWindow.duration,
          name: 'loose',
          details: `Skip only if message age < ${this.options.timeWindow.duration}ms`
        };
        
      case 'smart':
      default:
        // 智能模式：根据消息类型和时间智能判断
        const messageType = this.extractMessageType(message);

        // 检查是否处于重连状态（通过全局状态或消息元数据判断）
        const isReconnectMode = message._isReconnectMode || this.isInReconnectMode();

        if (isReconnectMode) {
          // {{ EMERGENCY-FIX: 优化重连模式的去重策略 }}
          // 重连模式：更智能的去重策略
          const messageAge = messageTime ? now - messageTime : 0;
          const isVeryOld = messageAge > 5 * 60 * 1000; // 5分钟前的消息

          if (isVeryOld) {
            // 只跳过很旧的重复消息
            return {
              shouldSkip: true,
              name: 'smart_reconnect_old_message',
              details: `Reconnect mode: skip old duplicate message (age: ${Math.round(messageAge/1000)}s)`
            };
          } else {
            // 新消息允许重复处理，但记录警告
            console.warn(`[消息去重] 重连模式下处理可能重复的新消息: ${messageId}`);
            return {
              shouldSkip: false,
              name: 'smart_reconnect_allow_new',
              details: 'Reconnect mode: allow processing of recent messages'
            };
          }
        }

        // 对于重要消息类型（如红包、转账），更宽松
        const importantTypes = [49, 2000, 2001]; // 链接、红包、转账等
        if (importantTypes.includes(messageType)) {
          return {
            shouldSkip: messageAge < 60000, // 1分钟内的重复才跳过
            name: 'smart_important',
            details: 'Important message type, only skip very recent duplicates'
          };
        }

        // 对于普通消息，中等严格
        return {
          shouldSkip: messageAge < this.options.timeWindow.duration,
          name: 'smart_normal',
          details: 'Normal message type, skip duplicates within time window'
        };
    }
  }

  /**
   * 批量处理待处理的消息
   */
  async processBatch() {
    if (this.pendingBatch.length === 0) return;
    
    try {
      const batch = this.pendingBatch.splice(0);
      this.stats.batchesProcessed++;
      
      this.logger.debug(`[消息去重] 批量处理 ${batch.length} 条消息`);
      
      // 并行处理批量消息
      const promises = batch.map(({ messageId, metadata }) => 
        this.syncManager.markMessageProcessed(messageId, metadata)
      );
      
      await Promise.allSettled(promises);
      
      this.logger.debug(`[消息去重] 批量处理完成`);
      
    } catch (error) {
      this.logger.error(`[消息去重] 批量处理失败: ${error.message}`);
    }
    
    // 清理定时器
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
  }

  /**
   * 调度批量处理
   */
  scheduleBatchProcessing() {
    if (this.batchTimer) return;
    
    this.batchTimer = setTimeout(() => {
      this.processBatch();
    }, this.options.batch.timeout);
  }

  /**
   * 提取消息ID
   * @param {Object} message - 消息对象
   * @returns {string|null} 消息ID
   */
  extractMessageId(message) {
    return message.MsgId || 
           message.msg_id || 
           message.NewMsgId || 
           message.new_msg_id || 
           message.id ||
           null;
  }

  /**
   * 提取消息类型
   * @param {Object} message - 消息对象
   * @returns {number} 消息类型
   */
  extractMessageType(message) {
    return message.MsgType || 
           message.msg_type || 
           message.type || 
           1; // 默认为文本消息
  }

  /**
   * 提取消息时间
   * @param {Object} message - 消息对象
   * @returns {number|null} 消息时间戳
   */
  extractMessageTime(message) {
    const createTime = message.CreateTime || 
                      message.create_time || 
                      message.timestamp || 
                      message.time;
    
    if (!createTime) return null;
    
    // 如果是秒级时间戳，转换为毫秒
    return createTime < 10000000000 ? createTime * 1000 : createTime;
  }

  /**
   * 检查是否处于重连模式
   * @returns {boolean} 是否处于重连模式
   */
  isInReconnectMode() {
    // 通过同步管理器检查重连状态
    if (this.syncManager && this.syncManager.isReconnectMode) {
      return this.syncManager.isReconnectMode();
    }

    // 通过全局状态检查（如果有的话）
    if (global.webSocketReconnectMode) {
      return global.webSocketReconnectMode;
    }

    return false;
  }

  /**
   * 设置重连模式状态
   * @param {boolean} isReconnectMode - 是否处于重连模式
   */
  setReconnectMode(isReconnectMode) {
    if (this.syncManager && this.syncManager.setReconnectMode) {
      this.syncManager.setReconnectMode(isReconnectMode);
    }

    // 设置全局状态
    global.webSocketReconnectMode = isReconnectMode;

    if (isReconnectMode) {
      this.logger.info('[消息去重] 进入重连模式，启用严格去重策略');
    } else {
      this.logger.info('[消息去重] 退出重连模式，恢复正常去重策略');
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const now = Date.now();
    const uptime = now - this.stats.lastResetTime;

    return {
      ...this.stats,
      uptime,
      duplicateRate: this.stats.totalProcessed > 0 ?
        (this.stats.duplicatesFound / this.stats.totalProcessed * 100).toFixed(2) + '%' : '0%',
      pendingBatchSize: this.pendingBatch.length,
      averageProcessingRate: this.stats.totalProcessed > 0 ?
        (this.stats.totalProcessed / (uptime / 1000)).toFixed(2) + ' msg/s' : '0 msg/s',
      isReconnectMode: this.isInReconnectMode()
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      totalProcessed: 0,
      duplicatesFound: 0,
      batchesProcessed: 0,
      lastResetTime: Date.now()
    };
  }

  /**
   * 强制处理待处理的批量消息
   */
  async flush() {
    await this.processBatch();
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
    
    // 处理剩余的批量消息
    await this.flush();
    
    this.logger.info('[消息去重] 资源已清理');
  }
}

module.exports = MessageDeduplication;
