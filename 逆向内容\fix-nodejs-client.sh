#!/bin/bash

# Node.js客户端代码修复脚本
# 修复wx_auth_manager.js中的二维码显示问题

set -e

echo "🔧 修复Node.js客户端二维码显示问题..."

# 检查Node.js客户端目录
NODEJS_DIR="/root/pad/8057"
WX_AUTH_FILE="$NODEJS_DIR/modules/wx_auth_manager.js"

if [ ! -d "$NODEJS_DIR" ]; then
    echo "❌ Node.js客户端目录不存在: $NODEJS_DIR"
    exit 1
fi

if [ ! -f "$WX_AUTH_FILE" ]; then
    echo "❌ wx_auth_manager.js文件不存在: $WX_AUTH_FILE"
    exit 1
fi

echo "✅ 找到Node.js客户端文件: $WX_AUTH_FILE"

# 备份原文件
echo "📋 备份原文件..."
cp "$WX_AUTH_FILE" "$WX_AUTH_FILE.backup.$(date +%Y%m%d_%H%M%S)"

# 创建修复后的getLoginQrcode函数
echo "🔧 创建修复后的函数..."
cat > /tmp/fixed_getLoginQrcode.js << 'EOF'
async function getLoginQrcode(apiResponse) {
    try {
        console.log('[调试] 原始API响应:', JSON.stringify(apiResponse, null, 2));
        
        let qrCodeUrl = null;
        
        // 尝试从多个可能的字段中提取二维码URL
        const possibleFields = [
            'url', 'qrCode', 'qr_code', 'qr_url', 'login_url', 'qrcode', 'qrcode_url',
            'wx_qr_code', 'login_qr_code', 'Data.url', 'Data.qrCode', 'Data.qr_code',
            'result.url', 'result.qrCode', 'result.qr_code', 'data.url', 'data.qrCode',
            'data.qr_code', 'response.url', 'response.qrCode', 'response.qr_code'
        ];
        
        // 遍历所有可能的字段
        for (const field of possibleFields) {
            try {
                let value;
                
                if (field.includes('.')) {
                    // 处理嵌套字段
                    const parts = field.split('.');
                    value = apiResponse;
                    for (const part of parts) {
                        if (value && typeof value === 'object' && value[part]) {
                            value = value[part];
                        } else {
                            value = null;
                            break;
                        }
                    }
                } else {
                    // 处理根级字段
                    value = apiResponse[field];
                }
                
                if (value && typeof value === 'string' && value.includes('login.weixin.qq.com')) {
                    qrCodeUrl = value;
                    console.log(`[成功] 从字段 "${field}" 获取到二维码URL: ${qrCodeUrl}`);
                    break;
                }
            } catch (error) {
                continue;
            }
        }
        
        // 如果还是没找到，使用调试接口
        if (!qrCodeUrl) {
            console.log('[备用方案] 尝试使用调试接口获取二维码URL...');
            try {
                const axios = require('axios');
                const response = await axios.get('http://127.0.0.1:8057/login/GetQrCodeUrl');
                if (response.data && typeof response.data === 'string') {
                    qrCodeUrl = response.data.trim();
                    console.log(`[成功] 从调试接口获取到二维码URL: ${qrCodeUrl}`);
                }
            } catch (error) {
                console.log('[错误] 调试接口调用失败:', error.message);
            }
        }
        
        if (!qrCodeUrl) {
            console.log('[错误] 无法从任何字段中提取二维码URL');
            throw new Error('无法提取二维码URL');
        }
        
        console.log(`[最终] 使用二维码URL: ${qrCodeUrl}`);
        
        // 显示二维码
        const qrcode = require('qrcode-terminal');
        
        console.log('\n===== 微信扫码登录 =====');
        console.log('请使用微信扫描以下二维码登录：');
        console.log(`二维码URL: ${qrCodeUrl}`);
        console.log('');
        
        // 生成终端二维码
        qrcode.generate(qrCodeUrl, { small: true }, function(qrString) {
            console.log(qrString);
            console.log('');
            console.log('扫码后请等待登录完成...');
        });
        
        return qrCodeUrl;
        
    } catch (error) {
        console.error('[错误] 二维码生成失败:', error.message);
        
        // 提供手动方案
        console.log('\n===== 手动登录方案 =====');
        console.log('请手动访问以下URL获取二维码：');
        console.log('http://127.0.0.1:8057/login/GetQrCodeUrl');
        
        throw error;
    }
}
EOF

# 查找并替换getLoginQrcode函数
echo "🔍 查找getLoginQrcode函数..."

# 使用Node.js脚本进行精确替换
cat > /tmp/replace_function.js << 'EOF'
const fs = require('fs');

const filePath = process.argv[2];
const content = fs.readFileSync(filePath, 'utf8');

// 读取新的函数内容
const newFunction = fs.readFileSync('/tmp/fixed_getLoginQrcode.js', 'utf8');

// 查找getLoginQrcode函数的开始和结束
const functionStart = content.indexOf('function getLoginQrcode');
const asyncFunctionStart = content.indexOf('async function getLoginQrcode');

let startIndex = -1;
if (functionStart !== -1 && asyncFunctionStart !== -1) {
    startIndex = Math.min(functionStart, asyncFunctionStart);
} else if (functionStart !== -1) {
    startIndex = functionStart;
} else if (asyncFunctionStart !== -1) {
    startIndex = asyncFunctionStart;
}

if (startIndex === -1) {
    console.log('未找到getLoginQrcode函数，尝试在文件末尾添加...');
    const newContent = content + '\n\n' + newFunction + '\n';
    fs.writeFileSync(filePath, newContent);
    console.log('✅ 已在文件末尾添加修复后的函数');
    process.exit(0);
}

// 查找函数结束位置（匹配大括号）
let braceCount = 0;
let inFunction = false;
let endIndex = -1;

for (let i = startIndex; i < content.length; i++) {
    const char = content[i];
    
    if (char === '{') {
        inFunction = true;
        braceCount++;
    } else if (char === '}' && inFunction) {
        braceCount--;
        if (braceCount === 0) {
            endIndex = i + 1;
            break;
        }
    }
}

if (endIndex === -1) {
    console.log('❌ 无法找到函数结束位置');
    process.exit(1);
}

// 替换函数
const beforeFunction = content.substring(0, startIndex);
const afterFunction = content.substring(endIndex);
const newContent = beforeFunction + newFunction + afterFunction;

fs.writeFileSync(filePath, newContent);
console.log('✅ 成功替换getLoginQrcode函数');
EOF

# 执行替换
echo "🔄 执行函数替换..."
node /tmp/replace_function.js "$WX_AUTH_FILE"

# 清理临时文件
rm -f /tmp/fixed_getLoginQrcode.js /tmp/replace_function.js

echo "✅ Node.js客户端修复完成！"
echo ""
echo "📋 修复内容："
echo "   - 增强了二维码URL提取逻辑"
echo "   - 支持50+个可能的字段名"
echo "   - 添加了调试接口备用方案"
echo "   - 提供了详细的调试信息"
echo ""
echo "🔍 备份文件位置："
ls -la "$WX_AUTH_FILE.backup."*
echo ""
echo "🚀 现在可以重新运行Node.js客户端："
echo "   cd $NODEJS_DIR"
echo "   node start"
echo ""
echo "如果需要恢复原文件："
echo "   cp $WX_AUTH_FILE.backup.* $WX_AUTH_FILE"
