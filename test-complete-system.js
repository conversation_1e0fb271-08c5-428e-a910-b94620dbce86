/**
 * 完整系统测试脚本
 * 测试统一启动器和Go内存监控的集成功能
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class CompleteSystemTester {
  constructor() {
    this.testResults = [];
    this.testStartTime = Date.now();
  }

  /**
   * 运行完整系统测试
   */
  async runCompleteTests() {
    console.log('🧪 开始完整系统测试');
    console.log('='.repeat(50));

    try {
      // 1. 基础文件检查
      await this.testBasicFiles();
      
      // 2. 配置文件测试
      await this.testConfigFiles();
      
      // 3. Go程序检测测试
      await this.testGoDetection();
      
      // 4. 启动器功能测试
      await this.testStarterFunctionality();
      
      // 5. 内存监控核心测试
      await this.testMemoryMonitorCore();
      
      // 显示测试结果
      this.displayTestResults();
      
    } catch (error) {
      console.error(`❌ 测试过程中出现错误: ${error.message}`);
    }
  }

  /**
   * 基础文件检查
   */
  async testBasicFiles() {
    console.log('\n📋 测试1: 基础文件检查');
    
    const requiredFiles = [
      'start.js',
      'go-memory-monitor.js',
      'start-unified.js',
      'start-optimized.js',
      'config/starter-config.json'
    ];

    for (const file of requiredFiles) {
      const exists = fs.existsSync(file);
      this.addTestResult(`文件存在: ${file}`, exists, exists ? '文件存在' : '文件缺失');
    }

    // 检查已删除的冗余文件
    const deletedFiles = [
      'start-with-memory-monitor.js',
      'start-unified-with-go-monitor.js',
      'run-memory-monitor.sh',
      'run-memory-monitor.bat'
    ];

    for (const file of deletedFiles) {
      const notExists = !fs.existsSync(file);
      this.addTestResult(`冗余文件已删除: ${file}`, notExists, notExists ? '已删除' : '仍存在');
    }
  }

  /**
   * 配置文件测试
   */
  async testConfigFiles() {
    console.log('\n⚙️ 测试2: 配置文件测试');
    
    try {
      // 测试配置文件读取
      const configPath = 'config/starter-config.json';
      const configContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configContent);
      
      this.addTestResult('配置文件解析', true, '配置文件格式正确');
      
      // 检查必要的配置项
      const requiredConfigs = [
        'enableGoMonitor',
        'autoDetectGoProgram',
        'goMonitor.memoryWarningThreshold',
        'goMonitor.memoryRestartThreshold',
        'goMonitor.monitorInterval'
      ];

      for (const configKey of requiredConfigs) {
        const keys = configKey.split('.');
        let value = config;
        for (const key of keys) {
          value = value[key];
        }
        
        const exists = value !== undefined;
        this.addTestResult(`配置项: ${configKey}`, exists, exists ? `值: ${value}` : '缺失');
      }

      // 检查优化后的参数值
      this.addTestResult('内存警告阈值优化', 
        config.goMonitor.memoryWarningThreshold >= 800,
        `${config.goMonitor.memoryWarningThreshold}MB`
      );
      
      this.addTestResult('内存重启阈值优化', 
        config.goMonitor.memoryRestartThreshold >= 1200,
        `${config.goMonitor.memoryRestartThreshold}MB`
      );
      
      this.addTestResult('监控间隔优化', 
        config.goMonitor.monitorInterval >= 60000,
        `${config.goMonitor.monitorInterval / 1000}秒`
      );

    } catch (error) {
      this.addTestResult('配置文件测试', false, error.message);
    }
  }

  /**
   * Go程序检测测试
   */
  async testGoDetection() {
    console.log('\n🔍 测试3: Go程序检测测试');
    
    const possiblePaths = [
      './逆向内容/myapp-linux',
      './逆向内容/myapp-linux-fixed',
      './逆向内容/myapp-linux-bot3',
      './逆向内容/myapp-linux-bot3-complete',
      './逆向内容/myapp-linux-bot3-real'
    ];

    let foundProgram = null;
    for (const programPath of possiblePaths) {
      if (fs.existsSync(programPath)) {
        foundProgram = programPath;
        this.addTestResult(`Go程序检测: ${programPath}`, true, '程序存在');
        break;
      }
    }

    if (!foundProgram) {
      this.addTestResult('Go程序检测', false, '未找到Go程序文件');
      console.log('  ⚠️ 未找到Go程序，后续测试将跳过Go监控功能');
    } else {
      // 检查文件权限（Linux/Mac）
      try {
        const stats = fs.statSync(foundProgram);
        const isExecutable = (stats.mode & parseInt('111', 8)) !== 0;
        this.addTestResult('Go程序可执行权限', isExecutable, 
          isExecutable ? '有执行权限' : '无执行权限');
      } catch (error) {
        this.addTestResult('权限检查', false, error.message);
      }
    }
  }

  /**
   * 启动器功能测试
   */
  async testStarterFunctionality() {
    console.log('\n🚀 测试4: 启动器功能测试');
    
    try {
      // 测试MasterStarter类导入
      const MasterStarter = require('./start.js');
      this.addTestResult('启动器类导入', true, 'MasterStarter类可正常导入');

      // 测试配置加载
      const starter = new MasterStarter();
      const config = starter.config;
      
      this.addTestResult('配置加载', 
        config && typeof config === 'object',
        '配置对象加载成功'
      );

      // 测试Go程序检测方法
      const detectedProgram = starter.detectGoProgram();
      this.addTestResult('Go程序检测方法', 
        typeof detectedProgram === 'string' || detectedProgram === false,
        detectedProgram ? `检测到: ${detectedProgram}` : '未检测到Go程序'
      );

    } catch (error) {
      this.addTestResult('启动器功能测试', false, error.message);
    }
  }

  /**
   * 内存监控核心测试
   */
  async testMemoryMonitorCore() {
    console.log('\n📊 测试5: 内存监控核心测试');
    
    try {
      // 测试GoMemoryMonitor类导入
      const GoMemoryMonitor = require('./go-memory-monitor');
      this.addTestResult('内存监控类导入', true, 'GoMemoryMonitor类可正常导入');

      // 测试监控器创建
      const monitor = new GoMemoryMonitor({
        goProgram: './test-dummy-program',
        memoryWarningThreshold: 100,
        memoryRestartThreshold: 200,
        enableConsoleLog: false
      });

      this.addTestResult('监控器创建', true, '监控器实例创建成功');

      // 测试状态获取
      const status = monitor.getStatus();
      this.addTestResult('状态获取', 
        typeof status === 'object' && status.hasOwnProperty('isMonitoring'),
        '状态对象结构正确'
      );

      // 测试配置应用
      this.addTestResult('配置应用', 
        monitor.config.memoryWarningThreshold === 100 &&
        monitor.config.memoryRestartThreshold === 200,
        '自定义配置正确应用'
      );

    } catch (error) {
      this.addTestResult('内存监控核心测试', false, error.message);
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      name: testName,
      success,
      message,
      timestamp: new Date().toISOString()
    });

    const status = success ? '✅' : '❌';
    console.log(`  ${status} ${testName}: ${message}`);
  }

  /**
   * 显示测试结果
   */
  displayTestResults() {
    console.log('\n📊 完整系统测试结果汇总');
    console.log('='.repeat(50));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const testDuration = Date.now() - this.testStartTime;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${Math.round(passedTests / totalTests * 100)}%`);
    console.log(`测试耗时: ${testDuration}ms`);

    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.message}`);
        });
    }

    // 保存测试报告
    this.saveTestReport();

    // 给出建议
    this.provideSuggestions(passedTests, totalTests);
  }

  /**
   * 保存测试报告
   */
  saveTestReport() {
    const report = {
      timestamp: new Date().toISOString(),
      testType: 'complete-system-test',
      duration: Date.now() - this.testStartTime,
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.success).length,
        failed: this.testResults.filter(r => !r.success).length
      },
      results: this.testResults
    };

    // 确保日志目录存在
    if (!fs.existsSync('logs')) {
      fs.mkdirSync('logs', { recursive: true });
    }

    fs.writeFileSync('logs/complete-system-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 完整测试报告已保存到: logs/complete-system-test-report.json');
  }

  /**
   * 提供建议
   */
  provideSuggestions(passed, total) {
    console.log('\n💡 建议:');
    
    if (passed === total) {
      console.log('🎉 所有测试通过！系统已准备就绪');
      console.log('✅ 可以安全使用 node start.js 启动系统');
    } else if (passed / total >= 0.8) {
      console.log('⚠️ 大部分测试通过，系统基本可用');
      console.log('🔧 建议修复失败的测试项以获得最佳体验');
    } else {
      console.log('❌ 多个测试失败，建议检查系统配置');
      console.log('🔍 请查看失败的测试项并进行相应修复');
    }

    console.log('\n🚀 启动命令: node start.js');
    console.log('⚙️ 配置文件: config/starter-config.json');
    console.log('📊 监控日志: logs/go-memory-monitor.log');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new CompleteSystemTester();
  tester.runCompleteTests().then(() => {
    console.log('\n🎉 完整系统测试完成！');
    process.exit(0);
  }).catch(error => {
    console.error(`测试失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = CompleteSystemTester;
