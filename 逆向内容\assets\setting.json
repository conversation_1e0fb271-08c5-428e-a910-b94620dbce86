{"debug": false, "host": "127.0.0.1", "port": "8057", "apiVersion": "v3", "ghWxid": "wechat_bot_3", "adminKey": "3rd_bot_admin_key_2025_secure", "workerpoolsize": 500, "maxworkertasklen": 1000, "redisConfig": {"Host": "127.0.0.1", "Port": 6379, "Db": 3, "Pass": "", "MaxIdle": 10, "MaxActive": 20, "IdleTimeout": 5000, "MaxConnLifetime": 30, "ConnectTimeout": 0, "OriginalUrl": "https://127.0.0.1/"}, "mySqlConnectStr": "root:123456@tcp(127.0.0.1:3306)/wechat_bot3?charset=utf8mb4&parseTime=true&loc=Local", "disabledCmdList": ["A000"], "newsSynWxId": false, "rocketMq": true, "rocketMqHost": "127.0.0.1:9876", "rocketAccessKey": "123", "rocketSecretKey": "123!#@13$", "rocketMqOptimized": {"maxReconsumeTimes": 3, "consumeTimeout": 15000, "pullInterval": 1000, "pullBatchSize": 32, "consumeMessageBatchMaxSize": 1, "maxConcurrentlyConsume": 5, "messageQueueLimit": 1000, "reconnectConfig": {"maxRetries": 5, "retryInterval": 5000, "backoffMultiplier": 2.0, "maxRetryInterval": 30000, "skipHistoryOnReconnect": true, "historySkipWindow": 300000}, "rateLimitConfig": {"enabled": true, "maxMessagesPerSecond": 100, "burstSize": 200}}, "rabbitMq": false, "topic": "wx_sync_msg_topic_bot3", "rabbitMqUrl": "amqp://yunkong:123456@127.0.0.1:5672/", "kafka": false, "kafkaUrl": "*************:9093,*************:9093,*************:9093", "kafkaUsername": "yunkongkafka", "kafkaPassword": "iSPmfJmtBgcvcTJZ123456", "dt": true, "cluster": {"clusterName": "wechat_bot3_cluster", "zkAddr": "", "ectdAddr": ""}}