#!/bin/bash

# 第三个微信机器人最终编译脚本
# 包含完整的编译、修复和验证流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔨 第三个微信机器人最终编译脚本${NC}"
echo "======================================================"

# 1. 环境检查
echo -e "${PURPLE}🔍 第一步：环境检查${NC}"
echo "======================================================"

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}❌ Go未安装，请先安装Go 1.19或更高版本${NC}"
    exit 1
fi

GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo -e "${GREEN}✅ 检测到Go版本: $GO_VERSION${NC}"

# 检查必要文件
if [ ! -f "main_complete_api.go" ]; then
    echo -e "${RED}❌ 源文件 main_complete_api.go 不存在${NC}"
    exit 1
fi

if [ ! -f "assets/setting.json" ]; then
    echo -e "${RED}❌ 配置文件 assets/setting.json 不存在${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 必要文件检查通过${NC}"

# 2. 设置编译环境
echo -e "${PURPLE}🌐 第二步：设置编译环境${NC}"
echo "======================================================"

export CGO_ENABLED=0
export GOOS=linux
export GOARCH=amd64
export GOPROXY=https://goproxy.cn,direct
export GOSUMDB=sum.golang.google.cn

echo -e "${GREEN}✅ 编译环境设置完成${NC}"
echo "   CGO_ENABLED: $CGO_ENABLED"
echo "   GOOS: $GOOS"
echo "   GOARCH: $GOARCH"
echo "   GOPROXY: $GOPROXY"

# 3. 清理环境
echo -e "${PURPLE}🧹 第三步：清理编译环境${NC}"
echo "======================================================"

echo -e "${YELLOW}清理旧文件...${NC}"
rm -f go.sum myapp-linux-bot3-complete

echo -e "${GREEN}✅ 环境清理完成${NC}"

# 4. 下载依赖
echo -e "${PURPLE}📦 第四步：下载依赖${NC}"
echo "======================================================"

echo -e "${YELLOW}下载Go模块依赖...${NC}"
if go mod tidy; then
    echo -e "${GREEN}✅ 依赖下载成功${NC}"
else
    echo -e "${RED}❌ 依赖下载失败${NC}"
    exit 1
fi

# 5. 编译程序
echo -e "${PURPLE}🔨 第五步：编译程序${NC}"
echo "======================================================"

echo -e "${YELLOW}编译第三个微信机器人...${NC}"
if go build -ldflags="-s -w" -o myapp-linux-bot3-complete main_complete_api.go; then
    echo -e "${GREEN}✅ 编译成功！${NC}"
else
    echo -e "${RED}❌ 编译失败！${NC}"
    exit 1
fi

# 检查编译结果
if [ -f "myapp-linux-bot3-complete" ]; then
    FILE_SIZE=$(ls -lh myapp-linux-bot3-complete | awk '{print $5}')
    echo -e "${GREEN}✅ 可执行文件生成成功${NC}"
    echo "   文件大小: $FILE_SIZE"
    ls -lh myapp-linux-bot3-complete
else
    echo -e "${RED}❌ 可执行文件生成失败${NC}"
    exit 1
fi

# 6. 设置权限
echo -e "${PURPLE}🔐 第六步：设置权限${NC}"
echo "======================================================"

chmod +x myapp-linux-bot3-complete
echo -e "${GREEN}✅ 执行权限设置完成${NC}"

# 7. 验证编译结果
echo -e "${PURPLE}🧪 第七步：验证编译结果${NC}"
echo "======================================================"

# 检查文件类型
FILE_TYPE=$(file myapp-linux-bot3-complete)
echo "文件类型: $FILE_TYPE"

if echo "$FILE_TYPE" | grep -q "ELF.*executable"; then
    echo -e "${GREEN}✅ 可执行文件格式正确${NC}"
else
    echo -e "${RED}❌ 可执行文件格式错误${NC}"
    exit 1
fi

# 8. 创建日志目录
echo -e "${PURPLE}📁 第八步：创建日志目录${NC}"
echo "======================================================"

mkdir -p logs
echo -e "${GREEN}✅ 日志目录创建完成${NC}"

# 9. 生成运行脚本
echo -e "${PURPLE}📝 第九步：生成运行脚本${NC}"
echo "======================================================"

cat > run-final.sh << 'EOF'
#!/bin/bash

# 第三个微信机器人运行脚本

echo "🚀 启动第三个微信机器人..."

# 检查端口
if netstat -tlnp 2>/dev/null | grep -q ":8057 "; then
    echo "❌ 端口8057已被占用，请先停止占用该端口的程序"
    exit 1
fi

# 启动程序
echo "$(date): Starting WeChat Bot 3 (Final Version)..." >> logs/bot3-final.log
./myapp-linux-bot3-complete 2>&1 | tee -a logs/bot3-final.log
EOF

chmod +x run-final.sh
echo -e "${GREEN}✅ 运行脚本生成完成${NC}"

# 10. 编译总结
echo -e "${PURPLE}📊 第十步：编译总结${NC}"
echo "======================================================"

echo -e "${BLUE}🎉 编译完成！第三个微信机器人信息：${NC}"
echo ""
echo -e "${GREEN}📋 程序信息：${NC}"
echo "   可执行文件: myapp-linux-bot3-complete"
echo "   文件大小: $(ls -lh myapp-linux-bot3-complete | awk '{print $5}')"
echo "   编译时间: $(date)"
echo ""
echo -e "${GREEN}🔧 配置信息：${NC}"
echo "   端口: 8057"
echo "   机器人ID: wechat_bot_3"
echo "   Redis数据库: Db 3"
echo "   MySQL数据库: wechat_bot3"
echo "   RocketMQ主题: wx_sync_msg_topic_bot3"
echo "   管理密钥: 3rd_bot_admin_key_2025_secure"
echo ""
echo -e "${GREEN}🔧 支持的API接口：${NC}"
echo "   ✅ /health - 健康检查"
echo "   ✅ /login/WakeUpLogin - 唤醒登录"
echo "   ✅ /login/GetLoginStatus - 获取登录状态"
echo "   ✅ /login/GetLoginQrCodeNew - 获取登录二维码"
echo "   ✅ /login/GenAuthKey - 生成授权码"
echo "   ✅ /admin/GenAuthKey - 管理员生成授权码"
echo "   ✅ /admin/GenWxAuthKey - 生成微信授权码"
echo "   ✅ /api/status - API状态"
echo "   ✅ /ws - WebSocket端点"
echo "   ✅ /login/GetQrCodeUrl - 调试接口（直接返回二维码URL）"
echo ""
echo -e "${GREEN}🚀 运行命令：${NC}"
echo "   ./run-final.sh                    # 使用运行脚本"
echo "   ./myapp-linux-bot3-complete       # 直接运行"
echo ""
echo -e "${GREEN}🧪 测试命令：${NC}"
echo "   curl http://localhost:8057/health                    # 健康检查"
echo "   curl http://localhost:8057/login/GetQrCodeUrl        # 获取二维码URL"
echo "   ./final-test-all.sh                                 # 完整测试"
echo ""
echo -e "${GREEN}🔧 Node.js客户端修复：${NC}"
echo "   ./fix-nodejs-client.sh            # 修复Node.js客户端"
echo ""

# 询问是否立即运行
echo -e "${YELLOW}是否立即运行程序？(y/n): ${NC}"
read -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}🚀 启动第三个微信机器人...${NC}"
    echo "程序启动中，按 Ctrl+C 停止..."
    echo ""
    
    # 检查端口
    if netstat -tlnp 2>/dev/null | grep -q ":8057 "; then
        echo -e "${RED}❌ 端口8057已被占用，请先停止占用该端口的程序${NC}"
        exit 1
    fi
    
    # 启动程序
    echo "$(date): Starting WeChat Bot 3 (Final Version)..." >> logs/bot3-final.log
    ./myapp-linux-bot3-complete 2>&1 | tee -a logs/bot3-final.log
else
    echo -e "${GREEN}✅ 编译完成，程序已准备就绪！${NC}"
    echo ""
    echo -e "${YELLOW}📋 下一步操作：${NC}"
    echo "1. 运行程序: ./run-final.sh"
    echo "2. 修复Node.js客户端: ./fix-nodejs-client.sh"
    echo "3. 运行完整测试: ./final-test-all.sh"
    echo ""
    echo -e "${GREEN}🎉 第三个微信机器人编译完成！${NC}"
fi
