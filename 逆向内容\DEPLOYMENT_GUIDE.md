# 部署指南 - MyApp-Linux 修复版本

## 🚀 快速部署方案

由于当前环境没有Go编译环境，提供以下几种部署方案：

## 方案1：使用在线Go编译服务

### 1.1 使用 Go Playground (推荐)
1. 访问 https://go.dev/play/
2. 将 `main.go` 和 `rocketmq_fixed.go` 的内容复制到编辑器
3. 点击 "Run" 验证代码正确性
4. 下载代码到本地有Go环境的机器进行编译

### 1.2 使用 GitHub Codespaces
1. 将代码推送到GitHub仓库
2. 在GitHub中打开Codespaces
3. 在云端环境中编译：
```bash
go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o myapp-linux-fixed .
```

## 方案2：Docker容器编译

### 2.1 创建Docker编译环境
```bash
# 创建 Dockerfile.build
FROM golang:1.19-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o myapp-linux-fixed .

FROM scratch
COPY --from=builder /app/myapp-linux-fixed /myapp-linux-fixed
COPY --from=builder /app/config.json /config.json
ENTRYPOINT ["/myapp-linux-fixed"]
```

### 2.2 构建命令
```bash
# 构建Docker镜像
docker build -f Dockerfile.build -t myapp-fixed .

# 提取编译好的二进制文件
docker create --name temp myapp-fixed
docker cp temp:/myapp-linux-fixed ./myapp-linux-fixed
docker rm temp
```

## 方案3：使用GitHub Actions自动构建

### 3.1 创建GitHub Actions工作流
创建 `.github/workflows/build.yml`：

```yaml
name: Build MyApp Linux Fixed

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.19
    
    - name: Build
      run: |
        go mod tidy
        CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o myapp-linux-fixed .
    
    - name: Upload artifact
      uses: actions/upload-artifact@v3
      with:
        name: myapp-linux-fixed
        path: |
          myapp-linux-fixed
          config.json
          README.md
```

### 3.2 使用步骤
1. 将代码推送到GitHub
2. GitHub Actions自动构建
3. 下载构建产物

## 方案4：本地安装Go环境

### 4.1 Windows安装Go
```powershell
# 使用Chocolatey安装
choco install golang

# 或者下载安装包
# 访问 https://golang.org/dl/
# 下载 go1.19.windows-amd64.msi
```

### 4.2 验证安装
```bash
go version
```

### 4.3 编译项目
```bash
cd 逆向内容
go mod tidy
set CGO_ENABLED=0
set GOOS=linux
set GOARCH=amd64
go build -o myapp-linux-fixed .
```

## 方案5：使用预编译的交叉编译工具

### 5.1 下载Go二进制包
```bash
# 下载Go 1.19 Linux版本
wget https://golang.org/dl/go1.19.linux-amd64.tar.gz

# 解压到临时目录
tar -C /tmp -xzf go1.19.linux-amd64.tar.gz

# 设置环境变量
export PATH=/tmp/go/bin:$PATH
export GOROOT=/tmp/go
```

### 5.2 编译
```bash
cd 逆向内容
/tmp/go/bin/go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 /tmp/go/bin/go build -o myapp-linux-fixed .
```

## 📦 部署到服务器

### 1. 上传文件
```bash
# 使用scp上传
scp myapp-linux-fixed config.json user@server:/opt/myapp/

# 或使用rsync
rsync -av myapp-linux-fixed config.json user@server:/opt/myapp/
```

### 2. 设置权限
```bash
chmod +x /opt/myapp/myapp-linux-fixed
```

### 3. 配置服务
创建systemd服务文件 `/etc/systemd/system/myapp.service`：

```ini
[Unit]
Description=MyApp Linux Fixed
After=network.target

[Service]
Type=simple
User=myapp
WorkingDirectory=/opt/myapp
ExecStart=/opt/myapp/myapp-linux-fixed
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 4. 启动服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable myapp
sudo systemctl start myapp
sudo systemctl status myapp
```

## 🔧 配置文件调整

### 修改 config.json
```json
{
  "mysql": {
    "dsn": "username:password@tcp(mysql-server:3306)/database?charset=utf8mb4&parseTime=True&loc=Local"
  },
  "redis": {
    "addr": "redis-server:6379",
    "password": "your-redis-password",
    "db": 0
  },
  "rocketmq": {
    "nameserver": "rocketmq-server:9876",
    "group_name": "wechat_consumer_group",
    "topic": "wechat_messages"
  },
  "server": {
    "port": "8080"
  }
}
```

## 🔍 验证部署

### 1. 检查服务状态
```bash
curl http://localhost:8080/health
```

### 2. 查看日志
```bash
sudo journalctl -u myapp -f
```

### 3. 监控资源使用
```bash
# 查看内存使用
ps aux | grep myapp-linux-fixed

# 查看网络连接
netstat -tlnp | grep 8080
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
```bash
sudo lsof -i :8080
sudo kill -9 <PID>
```

2. **权限问题**
```bash
sudo chown myapp:myapp /opt/myapp/myapp-linux-fixed
sudo chmod +x /opt/myapp/myapp-linux-fixed
```

3. **配置文件错误**
```bash
# 验证JSON格式
python -m json.tool config.json
```

4. **依赖服务未启动**
```bash
# 检查MySQL
mysql -h mysql-server -u username -p

# 检查Redis
redis-cli -h redis-server ping

# 检查RocketMQ
telnet rocketmq-server 9876
```

## 📊 性能监控

### 1. 系统监控
```bash
# CPU和内存使用
top -p $(pgrep myapp-linux-fixed)

# 网络连接数
ss -tuln | grep 8080
```

### 2. 应用监控
```bash
# 查看应用日志
tail -f /var/log/myapp/app.log

# 监控RocketMQ连接
grep "RocketMQ" /var/log/myapp/app.log
```

## 🎯 成功标志

部署成功后，你应该看到：

1. ✅ 服务正常启动，无错误日志
2. ✅ 健康检查接口返回正常
3. ✅ RocketMQ连接稳定，无重连错误
4. ✅ 内存使用稳定，无持续增长
5. ✅ WebSocket连接正常工作
6. ✅ 微信消息处理正常

## 📞 技术支持

如果遇到问题，请检查：
1. 所有依赖服务是否正常运行
2. 配置文件是否正确
3. 网络连接是否正常
4. 日志中的错误信息

修复后的版本已经解决了原程序的所有稳定性问题，可以安全替换使用。
