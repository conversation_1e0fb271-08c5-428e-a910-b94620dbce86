package main

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/sirupsen/logrus"
)

// FixedRocketMQConsumer 修复后的RocketMQ消费者
type FixedRocketMQConsumer struct {
	consumer        rocketmq.PushConsumer
	config          *RocketMQConfig
	logger          *logrus.Logger
	isRunning       int32
	stopChan        chan struct{}
	wg              sync.WaitGroup
	messageHandler  MessageHandler
	reconnectCount  int32
	maxReconnects   int32
	reconnectDelay  time.Duration
	healthCheckTicker *time.Ticker
	mu              sync.RWMutex
}

// RocketMQConfig RocketMQ配置 - 兼容原有setting.json格式
type RocketMQConfig struct {
	NameServers                 []string
	GroupName                   string
	Topic                       string
	MaxRetries                  int32
	ConsumeTimeout              time.Duration
	MaxConcurrency              int
	ReconnectDelay              time.Duration
	MaxReconnects               int32
	MaxReconsumeTimes           int
	PullInterval                time.Duration
	PullBatchSize               int
	ConsumeMessageBatchMaxSize  int
	MessageQueueLimit           int
	RateLimitEnabled            bool
	MaxMessagesPerSecond        int
	BurstSize                   int
}

// MessageHandler 消息处理器接口
type MessageHandler func(ctx context.Context, msg *primitive.MessageExt) error

// NewFixedRocketMQConsumer 创建修复后的RocketMQ消费者
func NewFixedRocketMQConsumer(config *RocketMQConfig, handler MessageHandler, logger *logrus.Logger) *FixedRocketMQConsumer {
	return &FixedRocketMQConsumer{
		config:         config,
		logger:         logger,
		stopChan:       make(chan struct{}),
		messageHandler: handler,
		maxReconnects:  config.MaxReconnects,
		reconnectDelay: config.ReconnectDelay,
	}
}

// Start 启动消费者
func (f *FixedRocketMQConsumer) Start() error {
	if !atomic.CompareAndSwapInt32(&f.isRunning, 0, 1) {
		return fmt.Errorf("consumer is already running")
	}

	f.logger.Info("Starting fixed RocketMQ consumer...")

	// 启动健康检查
	f.startHealthCheck()

	// 启动消费者
	return f.startConsumer()
}

// Stop 停止消费者
func (f *FixedRocketMQConsumer) Stop() error {
	if !atomic.CompareAndSwapInt32(&f.isRunning, 1, 0) {
		return fmt.Errorf("consumer is not running")
	}

	f.logger.Info("Stopping fixed RocketMQ consumer...")

	// 停止健康检查
	if f.healthCheckTicker != nil {
		f.healthCheckTicker.Stop()
	}

	// 发送停止信号
	close(f.stopChan)

	// 等待所有goroutine结束
	f.wg.Wait()

	// 关闭消费者
	if f.consumer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if err := f.consumer.Shutdown(); err != nil {
			f.logger.Errorf("Failed to shutdown consumer: %v", err)
			return err
		}
	}

	f.logger.Info("Fixed RocketMQ consumer stopped successfully")
	return nil
}

// startConsumer 启动消费者
func (f *FixedRocketMQConsumer) startConsumer() error {
	var err error

	// 创建消费者配置
	f.consumer, err = rocketmq.NewPushConsumer(
		consumer.WithGroupName(f.config.GroupName),
		consumer.WithNameServer(f.config.NameServers),
		consumer.WithConsumeFromWhere(consumer.ConsumeFromLastOffset),
		consumer.WithConsumerModel(consumer.Clustering),
		consumer.WithRetry(int(f.config.MaxRetries)),
		consumer.WithConsumeTimeout(f.config.ConsumeTimeout),
		consumer.WithMaxReconsumeTimes(int(f.config.MaxRetries)),
		// 修复：设置消费并发数，避免资源耗尽
		consumer.WithConsumeMessageBatchMaxSize(1),
	)

	if err != nil {
		return fmt.Errorf("failed to create consumer: %v", err)
	}

	// 订阅主题
	err = f.consumer.Subscribe(f.config.Topic, consumer.MessageSelector{}, f.createMessageHandler())
	if err != nil {
		return fmt.Errorf("failed to subscribe topic: %v", err)
	}

	// 启动消费者
	err = f.consumer.Start()
	if err != nil {
		return fmt.Errorf("failed to start consumer: %v", err)
	}

	f.logger.Infof("RocketMQ consumer started successfully for topic: %s", f.config.Topic)
	return nil
}

// createMessageHandler 创建消息处理器
func (f *FixedRocketMQConsumer) createMessageHandler() func(context.Context, ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
	return func(ctx context.Context, msgs ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
		// 修复：增加panic恢复机制
		defer func() {
			if r := recover(); r != nil {
				f.logger.Errorf("Message handler panic recovered: %v", r)
			}
		}()

		// 修复：检查消费者状态
		if atomic.LoadInt32(&f.isRunning) == 0 {
			return consumer.ConsumeRetryLater, fmt.Errorf("consumer is stopping")
		}

		// 修复：并发控制，避免goroutine泄漏
		semaphore := make(chan struct{}, f.config.MaxConcurrency)
		var wg sync.WaitGroup
		var hasError int32

		for _, msg := range msgs {
			select {
			case <-f.stopChan:
				return consumer.ConsumeRetryLater, fmt.Errorf("consumer is stopping")
			case semaphore <- struct{}{}:
				wg.Add(1)
				go func(message *primitive.MessageExt) {
					defer func() {
						<-semaphore
						wg.Done()
						if r := recover(); r != nil {
							f.logger.Errorf("Message processing panic: %v", r)
							atomic.StoreInt32(&hasError, 1)
						}
					}()

					// 修复：为每个消息设置超时
					msgCtx, cancel := context.WithTimeout(ctx, f.config.ConsumeTimeout)
					defer cancel()

					if err := f.messageHandler(msgCtx, message); err != nil {
						f.logger.Errorf("Failed to process message: %v", err)
						atomic.StoreInt32(&hasError, 1)
					}
				}(msg)
			default:
				// 修复：如果并发数达到上限，直接返回重试
				f.logger.Warn("Max concurrency reached, retrying later")
				return consumer.ConsumeRetryLater, fmt.Errorf("max concurrency reached")
			}
		}

		// 等待所有消息处理完成
		wg.Wait()

		// 检查是否有错误
		if atomic.LoadInt32(&hasError) > 0 {
			return consumer.ConsumeRetryLater, fmt.Errorf("some messages failed to process")
		}

		return consumer.ConsumeSuccess, nil
	}
}

// startHealthCheck 启动健康检查
func (f *FixedRocketMQConsumer) startHealthCheck() {
	f.healthCheckTicker = time.NewTicker(30 * time.Second)
	
	f.wg.Add(1)
	go func() {
		defer f.wg.Done()
		defer f.healthCheckTicker.Stop()

		for {
			select {
			case <-f.stopChan:
				return
			case <-f.healthCheckTicker.C:
				f.performHealthCheck()
			}
		}
	}()
}

// performHealthCheck 执行健康检查
func (f *FixedRocketMQConsumer) performHealthCheck() {
	f.mu.RLock()
	defer f.mu.RUnlock()

	if f.consumer == nil {
		f.logger.Warn("Consumer is nil during health check")
		f.attemptReconnect()
		return
	}

	// 这里可以添加更多的健康检查逻辑
	// 比如检查消费者状态、连接状态等
	f.logger.Debug("Health check passed")
}

// attemptReconnect 尝试重连
func (f *FixedRocketMQConsumer) attemptReconnect() {
	if atomic.LoadInt32(&f.reconnectCount) >= f.maxReconnects {
		f.logger.Error("Max reconnect attempts reached, stopping consumer")
		f.Stop()
		return
	}

	atomic.AddInt32(&f.reconnectCount, 1)
	f.logger.Infof("Attempting to reconnect (attempt %d/%d)", 
		atomic.LoadInt32(&f.reconnectCount), f.maxReconnects)

	f.wg.Add(1)
	go func() {
		defer f.wg.Done()

		// 等待重连延迟
		select {
		case <-f.stopChan:
			return
		case <-time.After(f.reconnectDelay):
		}

		f.mu.Lock()
		defer f.mu.Unlock()

		// 关闭旧的消费者
		if f.consumer != nil {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			f.consumer.Shutdown()
			cancel()
		}

		// 重新启动消费者
		if err := f.startConsumer(); err != nil {
			f.logger.Errorf("Failed to reconnect: %v", err)
			// 递归重试
			f.attemptReconnect()
		} else {
			f.logger.Info("Reconnected successfully")
			atomic.StoreInt32(&f.reconnectCount, 0) // 重置重连计数
		}
	}()
}

// IsRunning 检查消费者是否运行中
func (f *FixedRocketMQConsumer) IsRunning() bool {
	return atomic.LoadInt32(&f.isRunning) == 1
}

// GetReconnectCount 获取重连次数
func (f *FixedRocketMQConsumer) GetReconnectCount() int32 {
	return atomic.LoadInt32(&f.reconnectCount)
}
