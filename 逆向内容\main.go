package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// 全局变量
var (
	db           *gorm.DB
	redisClient  *redis.Client
	rocketMQConsumer rocketmq.PushConsumer
	wsConnections sync.Map
	logger       *logrus.Logger
)

// 配置结构 - 兼容原有assets/setting.json格式
type Config struct {
	Debug               bool   `json:"debug"`
	Host                string `json:"host"`
	Port                string `json:"port"`
	APIVersion          string `json:"apiVersion"`
	GhWxid              string `json:"ghWxid"`
	AdminKey            string `json:"adminKey"`
	WorkerPoolSize      int    `json:"workerpoolsize"`
	MaxWorkerTaskLen    int    `json:"maxworkertasklen"`
	RedisConfig         struct {
		Host               string `json:"Host"`
		Port               int    `json:"Port"`
		Db                 int    `json:"Db"`
		Pass               string `json:"Pass"`
		MaxIdle            int    `json:"MaxIdle"`
		MaxActive          int    `json:"MaxActive"`
		IdleTimeout        int    `json:"IdleTimeout"`
		MaxConnLifetime    int    `json:"MaxConnLifetime"`
		ConnectTimeout     int    `json:"ConnectTimeout"`
		OriginalUrl        string `json:"OriginalUrl"`
	} `json:"redisConfig"`
	MySQLConnectStr     string   `json:"mySqlConnectStr"`
	DisabledCmdList     []string `json:"disabledCmdList"`
	NewsSynWxId         bool     `json:"newsSynWxId"`
	RocketMq            bool     `json:"rocketMq"`
	RocketMqHost        string   `json:"rocketMqHost"`
	RocketAccessKey     string   `json:"rocketAccessKey"`
	RocketSecretKey     string   `json:"rocketSecretKey"`
	RocketMqOptimized   struct {
		MaxReconsumeTimes           int `json:"maxReconsumeTimes"`
		ConsumeTimeout              int `json:"consumeTimeout"`
		PullInterval                int `json:"pullInterval"`
		PullBatchSize               int `json:"pullBatchSize"`
		ConsumeMessageBatchMaxSize  int `json:"consumeMessageBatchMaxSize"`
		MaxConcurrentlyConsume      int `json:"maxConcurrentlyConsume"`
		MessageQueueLimit           int `json:"messageQueueLimit"`
		ReconnectConfig             struct {
			MaxRetries              int     `json:"maxRetries"`
			RetryInterval           int     `json:"retryInterval"`
			BackoffMultiplier       float64 `json:"backoffMultiplier"`
			MaxRetryInterval        int     `json:"maxRetryInterval"`
			SkipHistoryOnReconnect  bool    `json:"skipHistoryOnReconnect"`
			HistorySkipWindow       int     `json:"historySkipWindow"`
		} `json:"reconnectConfig"`
		RateLimitConfig struct {
			Enabled               bool `json:"enabled"`
			MaxMessagesPerSecond  int  `json:"maxMessagesPerSecond"`
			BurstSize             int  `json:"burstSize"`
		} `json:"rateLimitConfig"`
	} `json:"rocketMqOptimized"`
	RabbitMq            bool   `json:"rabbitMq"`
	Topic               string `json:"topic"`
	RabbitMqUrl         string `json:"rabbitMqUrl"`
	Kafka               bool   `json:"kafka"`
	KafkaUrl            string `json:"kafkaUrl"`
	KafkaUsername       string `json:"kafkaUsername"`
	KafkaPassword       string `json:"kafkaPassword"`
	Dt                  bool   `json:"dt"`
	Cluster             struct {
		ClusterName string `json:"clusterName"`
		ZkAddr      string `json:"zkAddr"`
		EctdAddr    string `json:"ectdAddr"`
	} `json:"cluster"`
}

// WebSocket升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// 微信消息结构
type WeChatMessage struct {
	Type      string `json:"type"`
	Content   string `json:"content"`
	FromUser  string `json:"from_user"`
	ToUser    string `json:"to_user"`
	Timestamp int64  `json:"timestamp"`
}

// 初始化日志
func initLogger() {
	logger = logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.JSONFormatter{})
}

// 初始化数据库
func initDatabase(config *Config) error {
	var err error
	db, err = gorm.Open(mysql.Open(config.MySQLConnectStr), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get database instance: %v", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(config.RedisConfig.MaxIdle)
	sqlDB.SetMaxOpenConns(config.RedisConfig.MaxActive)
	sqlDB.SetConnMaxLifetime(time.Duration(config.RedisConfig.MaxConnLifetime) * time.Second)

	logger.Info("Database connected successfully")
	return nil
}

// 初始化Redis
func initRedis(config *Config) error {
	redisAddr := fmt.Sprintf("%s:%d", config.RedisConfig.Host, config.RedisConfig.Port)
	redisClient = redis.NewClient(&redis.Options{
		Addr:         redisAddr,
		Password:     config.RedisConfig.Pass,
		DB:           config.RedisConfig.Db,
		MaxRetries:   3,
		DialTimeout:  time.Duration(config.RedisConfig.ConnectTimeout) * time.Second,
		IdleTimeout:  time.Duration(config.RedisConfig.IdleTimeout) * time.Second,
		PoolSize:     config.RedisConfig.MaxActive,
		MinIdleConns: config.RedisConfig.MaxIdle,
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("failed to connect to Redis: %v", err)
	}

	logger.Info("Redis connected successfully")
	return nil
}

// 转换配置格式
func convertToRocketMQConfig(config *Config) *RocketMQConfig {
	rocketConfig := config.RocketMqOptimized
	return &RocketMQConfig{
		NameServers:                []string{config.RocketMqHost},
		GroupName:                  "wechat_consumer_group",
		Topic:                      config.Topic,
		MaxRetries:                 int32(rocketConfig.MaxReconsumeTimes),
		ConsumeTimeout:             time.Duration(rocketConfig.ConsumeTimeout) * time.Millisecond,
		MaxConcurrency:             rocketConfig.MaxConcurrentlyConsume,
		ReconnectDelay:             time.Duration(rocketConfig.ReconnectConfig.RetryInterval) * time.Millisecond,
		MaxReconnects:              int32(rocketConfig.ReconnectConfig.MaxRetries),
		MaxReconsumeTimes:          rocketConfig.MaxReconsumeTimes,
		PullInterval:               time.Duration(rocketConfig.PullInterval) * time.Millisecond,
		PullBatchSize:              rocketConfig.PullBatchSize,
		ConsumeMessageBatchMaxSize: rocketConfig.ConsumeMessageBatchMaxSize,
		MessageQueueLimit:          rocketConfig.MessageQueueLimit,
		RateLimitEnabled:           rocketConfig.RateLimitConfig.Enabled,
		MaxMessagesPerSecond:       rocketConfig.RateLimitConfig.MaxMessagesPerSecond,
		BurstSize:                  rocketConfig.RateLimitConfig.BurstSize,
	}
}

// 修复后的RocketMQ初始化 - 解决goroutine泄漏和重连问题
func initRocketMQ(config *Config) error {
	// 检查是否启用RocketMQ
	if !config.RocketMq {
		logger.Info("RocketMQ is disabled in configuration")
		return nil
	}

	// 转换配置格式
	rocketConfig := convertToRocketMQConfig(config)

	// 使用修复后的RocketMQ消费者
	messageHandler := func(ctx context.Context, msg *primitive.MessageExt) error {
		return processMessage(ctx, msg)
	}

	fixedConsumer := NewFixedRocketMQConsumer(rocketConfig, messageHandler, logger)

	// 启动消费者
	err := fixedConsumer.Start()
	if err != nil {
		return fmt.Errorf("failed to start fixed RocketMQ consumer: %v", err)
	}

	logger.Info("Fixed RocketMQ consumer started successfully")
	return nil
}


// 处理RocketMQ消息
func processMessage(ctx context.Context, msg *primitive.MessageExt) error {
	// 修复：增加context超时检查
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}
	
	var wechatMsg WeChatMessage
	err := json.Unmarshal(msg.Body, &wechatMsg)
	if err != nil {
		logger.Errorf("Failed to unmarshal message: %v", err)
		return err
	}
	
	// 处理微信消息逻辑
	logger.Infof("Processing WeChat message: %+v", wechatMsg)
	
	// 广播消息到所有WebSocket连接
	broadcastMessage(wechatMsg)
	
	// 存储到数据库（如果需要）
	// saveMessageToDB(wechatMsg)
	
	return nil
}

// 广播消息到WebSocket连接
func broadcastMessage(msg WeChatMessage) {
	wsConnections.Range(func(key, value interface{}) bool {
		conn := value.(*websocket.Conn)
		err := conn.WriteJSON(msg)
		if err != nil {
			logger.Errorf("Failed to send message to WebSocket: %v", err)
			conn.Close()
			wsConnections.Delete(key)
		}
		return true
	})
}

// WebSocket处理器
func handleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.Errorf("Failed to upgrade to WebSocket: %v", err)
		return
	}
	
	// 存储连接
	connID := fmt.Sprintf("%p", conn)
	wsConnections.Store(connID, conn)
	
	defer func() {
		conn.Close()
		wsConnections.Delete(connID)
	}()
	
	// 保持连接活跃
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			logger.Infof("WebSocket connection closed: %v", err)
			break
		}
	}
}

// 健康检查接口
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"timestamp": time.Now().Unix(),
	})
}

// 主函数
func main() {
	// 初始化日志
	initLogger()
	
	// 读取配置 - 优先使用assets/setting.json
	configFile := "assets/setting.json"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	// 如果assets/setting.json不存在，尝试使用config.json
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		configFile = "config.json"
		logger.Warnf("assets/setting.json not found, using %s", configFile)
	}

	configData, err := os.ReadFile(configFile)
	if err != nil {
		logger.Fatalf("Failed to read config file %s: %v", configFile, err)
	}
	
	var config Config
	err = json.Unmarshal(configData, &config)
	if err != nil {
		logger.Fatalf("Failed to parse config: %v", err)
	}
	
	// 初始化各个组件
	if err := initDatabase(&config); err != nil {
		logger.Fatalf("Database initialization failed: %v", err)
	}
	
	if err := initRedis(&config); err != nil {
		logger.Fatalf("Redis initialization failed: %v", err)
	}
	
	if err := initRocketMQ(&config); err != nil {
		logger.Fatalf("RocketMQ initialization failed: %v", err)
	}
	
	// 设置Gin路由
	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()
	
	// API路由
	r.GET("/health", healthCheck)
	r.GET("/ws", handleWebSocket)
	
	// 启动HTTP服务器
	serverAddr := fmt.Sprintf("%s:%s", config.Host, config.Port)
	srv := &http.Server{
		Addr:    serverAddr,
		Handler: r,
	}

	go func() {
		logger.Infof("Server starting on %s", serverAddr)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("Server failed to start: %v", err)
		}
	}()
	
	// 优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logger.Info("Shutting down server...")
	
	// 修复：优雅关闭RocketMQ消费者，避免资源泄漏
	if rocketMQConsumer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		
		err := rocketMQConsumer.Shutdown()
		if err != nil {
			logger.Errorf("Failed to shutdown RocketMQ consumer: %v", err)
		} else {
			logger.Info("RocketMQ consumer shutdown successfully")
		}
	}
	
	// 关闭HTTP服务器
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(shutdownCtx); err != nil {
		logger.Errorf("Server forced to shutdown: %v", err)
	}
	
	logger.Info("Server exited")
}
