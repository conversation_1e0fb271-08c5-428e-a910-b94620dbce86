#!/bin/bash

# Node.js客户端二维码显示问题修复脚本

set -e

echo "🔧 修复Node.js客户端二维码显示问题..."

# 重新编译程序
echo "🔨 重新编译程序..."
export CGO_ENABLED=0
export GOOS=linux
export GOARCH=amd64
export GOPROXY=https://goproxy.cn,direct

go build -ldflags="-s -w" -o myapp-linux-bot3-complete main_complete_api.go

if [ -f "myapp-linux-bot3-complete" ]; then
    echo "✅ 编译成功！"
else
    echo "❌ 编译失败！"
    exit 1
fi

# 检查程序是否在运行
if pgrep -f "myapp-linux-bot3-complete" > /dev/null; then
    echo "🔄 停止旧程序..."
    pkill -f "myapp-linux-bot3-complete"
    sleep 2
fi

# 启动程序
echo "🚀 启动修复后的程序..."
chmod +x myapp-linux-bot3-complete
./myapp-linux-bot3-complete &

# 等待程序启动
sleep 3

# 测试新的调试接口
echo "🧪 测试调试接口..."
echo "直接获取二维码URL:"
curl -s http://localhost:8057/login/GetQrCodeUrl
echo ""

echo "🧪 测试完整二维码API..."
echo "完整二维码API响应:"
curl -s -X POST http://localhost:8057/login/GetLoginQrCodeNew \
     -H "Content-Type: application/json" \
     -d '{"Check":false,"Proxy":""}' | jq '.' 2>/dev/null || \
curl -s -X POST http://localhost:8057/login/GetLoginQrCodeNew \
     -H "Content-Type: application/json" \
     -d '{"Check":false,"Proxy":""}'

echo ""
echo "✅ 修复完成！"
echo ""
echo "📋 新增的调试接口:"
echo "   GET /login/GetQrCodeUrl - 直接返回二维码URL字符串"
echo ""
echo "🔍 测试命令:"
echo "   curl http://localhost:8057/login/GetQrCodeUrl"
echo ""
echo "现在Node.js客户端应该能够正确获取二维码URL了！"
