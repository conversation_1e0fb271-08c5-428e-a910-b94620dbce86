<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码审查报告</title>
    <style>
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .metadata p {
            margin: 5px 0;
            color: #666;
        }
        
        section {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
        }
        
        .summary-card h3 {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .metric {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .health-indicator {
            font-size: 18px;
            font-weight: bold;
            padding: 10px;
            border-radius: 4px;
        }
        
        .health-indicator.excellent { background: #d4edda; color: #155724; }
        .health-indicator.good { background: #d1ecf1; color: #0c5460; }
        .health-indicator.fair { background: #fff3cd; color: #856404; }
        .health-indicator.poor { background: #f8d7da; color: #721c24; }
        .health-indicator.unknown { background: #e2e3e5; color: #383d41; }
        
        .issue-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .issue-type {
            text-align: center;
            padding: 15px;
            border-radius: 6px;
        }
        
        .issue-type.critical { background: #f8d7da; color: #721c24; }
        .issue-type.high { background: #fff3cd; color: #856404; }
        .issue-type.medium { background: #d1ecf1; color: #0c5460; }
        .issue-type.low { background: #d4edda; color: #155724; }
        
        .recommendation {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin: 15px 0;
            overflow: hidden;
        }
        
        .recommendation-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .recommendation-title {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .recommendation-priority {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .priority-high { background: #dc3545; color: white; }
        .priority-medium { background: #ffc107; color: black; }
        .priority-low { background: #28a745; color: white; }
        
        .recommendation-content {
            padding: 15px;
        }
        
        .issue-item {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin: 10px 0;
            padding: 15px;
        }
        
        .issue-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .issue-title {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .issue-severity {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .severity-critical { background: #dc3545; color: white; }
        .severity-high { background: #fd7e14; color: white; }
        .severity-medium { background: #ffc107; color: black; }
        .severity-low { background: #28a745; color: white; }
        
        .issue-location {
            color: #666;
            font-size: 14px;
            margin: 5px 0;
        }
        
        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>代码审查分析报告</h1>
            <div class="metadata">
                <p>项目路径: E:\桌面\newbot\code-review-analyzer</p>
                <p>分析时间: 2025/7/22 17:19:12</p>
                <p>分析耗时: 343ms</p>
            </div>
        </header>

        <section class="executive-summary">
            <h2>执行摘要</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>整体健康度</h3>
                    <div class="health-indicator poor">
                        较差
                    </div>
                </div>
                <div class="summary-card">
                    <h3>模块总数</h3>
                    <div class="metric">20</div>
                </div>
                <div class="summary-card">
                    <h3>问题总数</h3>
                    <div class="metric">1568</div>
                </div>
                <div class="summary-card">
                    <h3>代码重复</h3>
                    <div class="metric">0</div>
                </div>
            </div>
            
            
        <div class="issue-breakdown">
            <div class="issue-type critical">
                <div class="stat-label">严重</div>
                <div class="stat-value">0</div>
            </div>
            <div class="issue-type high">
                <div class="stat-label">高</div>
                <div class="stat-value">1496</div>
            </div>
            <div class="issue-type medium">
                <div class="stat-label">中</div>
                <div class="stat-value">72</div>
            </div>
            <div class="issue-type low">
                <div class="stat-label">低</div>
                <div class="stat-value">0</div>
            </div>
        </div>
        </section>

        <section class="key-findings">
            <h2>关键发现</h2>
            <ul>
                <li>Most common issue type: undefined (1532 instances)</li><li>Most complex module: logical-consistency-validator (complexity: 189)</li>
            </ul>
        </section>

        <section class="recommendations">
            <h2>优化建议</h2>
            
        <div class="recommendation">
            <div class="recommendation-header">
                <span class="recommendation-title">Address High Priority Issues</span>
                <span class="recommendation-priority priority-high">
                    优先级: 8
                </span>
            </div>
            <div class="recommendation-content">
                <p><strong>描述:</strong> Found 1496 high priority issues</p>
                <p><strong>建议方案:</strong> Systematic approach to fix high priority issues</p>
                <p><strong>预期影响:</strong> High - significant quality improvement</p>
                <p><strong>工作量估算:</strong> medium</p>
                
                <div>
                    <strong>收益:</strong>
                    <ul>
                        <li>Improved code quality</li><li>Better maintainability</li><li>Reduced technical debt</li>
                    </ul>
                </div>
            </div>
        </div>
        </section>

        <section class="detailed-issues">
            <h2>详细问题列表</h2>
            
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Add try-catch block or .catch() handler for async operations</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Add try-catch block or .catch() handler for async operations</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Add try-catch block or .catch() handler for async operations</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        <div class="issue-item">
            <div class="issue-header">
                <span class="issue-title">undefined</span>
                <span class="issue-severity severity-high">high</span>
            </div>
            <div class="issue-location">
                文件: Unknown
                
                
            </div>
            <p><strong>描述:</strong> undefined</p>
            <p><strong>影响:</strong> undefined</p>
            <p><strong>建议:</strong> Import the function or check for typos in function name</p>
        </div>
        </section>

        <section class="statistics">
            <h2>统计信息</h2>
            
        <div class="statistics-grid">
            <div class="stat-item">
                <div class="stat-label">分析耗时</div>
                <div class="stat-value">344ms</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">分析模块数</div>
                <div class="stat-value">20</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">代码总行数</div>
                <div class="stat-value">5595</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">发现问题数</div>
                <div class="stat-value">1568</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">代码重复数</div>
                <div class="stat-value">0</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">平均复杂度</div>
                <div class="stat-value">53.6</div>
            </div>
        </div>
        </section>
    </div>

    <script>
        
        // 添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 可以添加图表、筛选等交互功能
            console.log('Code Review Report loaded');
        });
    </script>
</body>
</html>