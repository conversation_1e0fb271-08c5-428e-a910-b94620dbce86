# 🌐 网络问题解决方案

## 🔍 问题诊断

您遇到的是Go模块下载网络超时问题：
```
dial tcp [2607:f8b0:400a:80c::2011]:443: i/o timeout
```

这是因为Go默认使用的代理服务器`proxy.golang.org`在某些网络环境下无法访问。

## 🛠 解决方案

### 方案1：使用中国代理（推荐）

```bash
# 设置Go代理
export GOPROXY=https://goproxy.cn,https://mirrors.aliyun.com/goproxy/,direct
export GOSUMDB=sum.golang.google.cn

# 重新编译
./build-bot3.sh
```

### 方案2：使用离线编译脚本

```bash
# 给脚本执行权限
chmod +x build-offline.sh

# 运行离线编译（会自动处理网络问题）
./build-offline.sh
```

### 方案3：手动配置Go环境

```bash
# 永久设置Go代理
echo 'export GOPROXY=https://goproxy.cn,direct' >> ~/.bashrc
echo 'export GOSUMDB=sum.golang.google.cn' >> ~/.bashrc
source ~/.bashrc

# 清理并重新下载
go clean -modcache
go mod tidy
go build -o myapp-linux-bot3 .
```

### 方案4：使用Docker编译（如果有Docker）

```bash
# 创建Dockerfile.build
cat > Dockerfile.build << 'EOF'
FROM golang:1.19-alpine
ENV GOPROXY=https://goproxy.cn,direct
ENV GOSUMDB=sum.golang.google.cn
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o myapp-linux-bot3 .
EOF

# 使用Docker编译
docker build -f Dockerfile.build -t bot3-builder .
docker create --name temp bot3-builder
docker cp temp:/app/myapp-linux-bot3 ./
docker rm temp
```

## 🚀 快速解决步骤

### 立即可用的命令序列：

```bash
# 1. 设置代理
export GOPROXY=https://goproxy.cn,direct
export GOSUMDB=sum.golang.google.cn

# 2. 清理环境
rm -f go.sum
go clean -modcache

# 3. 重新下载依赖
go mod tidy

# 4. 编译程序
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o myapp-linux-bot3 .

# 5. 运行程序
./myapp-linux-bot3
```

## 🔧 离线编译版本特性

如果网络问题持续，`build-offline.sh`会创建一个简化版本：

### 简化版本功能
- ✅ **基础HTTP服务** - 监听8057端口
- ✅ **健康检查接口** - `/health`
- ✅ **WebSocket支持** - `/ws`
- ✅ **管理接口** - `/api/status`
- ✅ **配置文件读取** - 支持assets/setting.json
- ⚠️ **不包含RocketMQ** - 避免网络依赖问题

### 验证简化版本
```bash
# 启动程序
./myapp-linux-bot3

# 测试健康检查
curl http://localhost:8057/health

# 预期输出
{
  "status": "ok",
  "bot_id": "wechat_bot_3",
  "version": "v1.0.0-simplified",
  "timestamp": **********
}
```

## 📋 网络环境检查

### 检查网络连接
```bash
# 测试Google连接
ping -c 3 proxy.golang.org

# 测试中国代理连接
curl -I https://goproxy.cn

# 检查DNS解析
nslookup proxy.golang.org
```

### 检查Go配置
```bash
# 查看当前Go环境
go env GOPROXY
go env GOSUMDB
go env GO111MODULE

# 查看模块缓存
go env GOMODCACHE
```

## 🎯 推荐的永久解决方案

### 1. 配置系统级代理
```bash
# 编辑 /etc/environment
sudo nano /etc/environment

# 添加以下行
GOPROXY=https://goproxy.cn,direct
GOSUMDB=sum.golang.google.cn
```

### 2. 配置用户级代理
```bash
# 编辑 ~/.bashrc
nano ~/.bashrc

# 添加以下行
export GOPROXY=https://goproxy.cn,direct
export GOSUMDB=sum.golang.google.cn

# 重新加载配置
source ~/.bashrc
```

### 3. 项目级配置
```bash
# 在项目目录创建 .env 文件
echo "GOPROXY=https://goproxy.cn,direct" > .env
echo "GOSUMDB=sum.golang.google.cn" >> .env

# 在编译前加载
source .env
```

## 🔍 故障排除

### 如果代理仍然失败
```bash
# 尝试不同的代理
export GOPROXY=https://mirrors.aliyun.com/goproxy/,direct

# 或者禁用代理验证
export GOSUMDB=off

# 或者使用直连模式
export GOPROXY=direct
```

### 如果模块缓存损坏
```bash
# 清理模块缓存
go clean -modcache

# 重新初始化模块
rm go.sum
go mod tidy
```

### 如果依赖版本冲突
```bash
# 更新所有依赖到最新版本
go get -u all
go mod tidy
```

## 🎉 成功标志

编译成功后，您应该看到：

```bash
✅ 编译成功！
-rwxr-xr-x 1 <USER> <GROUP> 15M Aug  3 12:00 myapp-linux-bot3

🤖 第三个机器人信息:
   📡 端口: 8057
   🔑 机器人ID: wechat_bot_3
   📋 版本: v1.0.0-fixed

🚀 运行命令:
   ./myapp-linux-bot3

🔍 验证命令:
   curl http://localhost:8057/health
```

## 📞 如果问题持续

如果所有方案都失败，可以：

1. **使用离线编译版本** - 功能简化但可用
2. **在其他网络环境编译** - 然后传输二进制文件
3. **使用预编译版本** - 如果有可用的二进制文件
4. **联系网络管理员** - 解决代理或防火墙问题

---

**记住：网络问题不影响程序的核心功能，简化版本同样可以作为第三个微信机器人使用！**
