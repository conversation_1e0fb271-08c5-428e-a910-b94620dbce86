// Node.js客户端二维码显示问题修复代码
// 这个文件包含了修复wx_auth_manager.js中getLoginQrcode函数的代码

// 修复后的getLoginQrcode函数
async function getLoginQrcode(apiResponse) {
    try {
        console.log('[调试] 原始API响应:', JSON.stringify(apiResponse, null, 2));
        
        let qrCodeUrl = null;
        
        // 尝试从多个可能的字段中提取二维码URL
        const possibleFields = [
            'url',
            'qrCode', 
            'qr_code',
            'qr_url',
            'login_url',
            'qrcode',
            'qrcode_url',
            'wx_qr_code',
            'login_qr_code',
            'Data.url',
            'Data.qrCode',
            'Data.qr_code',
            'result.url',
            'result.qrCode',
            'result.qr_code',
            'data.url',
            'data.qrCode',
            'data.qr_code',
            'response.url',
            'response.qrCode',
            'response.qr_code'
        ];
        
        // 遍历所有可能的字段
        for (const field of possibleFields) {
            try {
                let value;
                
                if (field.includes('.')) {
                    // 处理嵌套字段
                    const parts = field.split('.');
                    value = apiResponse;
                    for (const part of parts) {
                        if (value && typeof value === 'object' && value[part]) {
                            value = value[part];
                        } else {
                            value = null;
                            break;
                        }
                    }
                } else {
                    // 处理根级字段
                    value = apiResponse[field];
                }
                
                if (value && typeof value === 'string' && value.includes('login.weixin.qq.com')) {
                    qrCodeUrl = value;
                    console.log(`[成功] 从字段 "${field}" 获取到二维码URL: ${qrCodeUrl}`);
                    break;
                }
            } catch (error) {
                console.log(`[跳过] 字段 "${field}" 解析失败:`, error.message);
                continue;
            }
        }
        
        // 如果还是没找到，尝试从数组字段中获取
        if (!qrCodeUrl) {
            const arrayFields = ['urls', 'qr_codes'];
            for (const field of arrayFields) {
                if (apiResponse[field] && Array.isArray(apiResponse[field]) && apiResponse[field].length > 0) {
                    const firstUrl = apiResponse[field][0];
                    if (firstUrl && typeof firstUrl === 'string' && firstUrl.includes('login.weixin.qq.com')) {
                        qrCodeUrl = firstUrl;
                        console.log(`[成功] 从数组字段 "${field}" 获取到二维码URL: ${qrCodeUrl}`);
                        break;
                    }
                }
            }
        }
        
        // 如果还是没找到，使用调试接口
        if (!qrCodeUrl) {
            console.log('[备用方案] 尝试使用调试接口获取二维码URL...');
            try {
                const axios = require('axios');
                const response = await axios.get('http://127.0.0.1:8057/login/GetQrCodeUrl');
                if (response.data && typeof response.data === 'string') {
                    qrCodeUrl = response.data.trim();
                    console.log(`[成功] 从调试接口获取到二维码URL: ${qrCodeUrl}`);
                }
            } catch (error) {
                console.log('[错误] 调试接口调用失败:', error.message);
            }
        }
        
        if (!qrCodeUrl) {
            console.log('[错误] 无法从任何字段中提取二维码URL');
            console.log('[调试] 可用的字段:', Object.keys(apiResponse));
            throw new Error('无法提取二维码URL');
        }
        
        console.log(`[最终] 使用二维码URL: ${qrCodeUrl}`);
        
        // 显示二维码
        const qrcode = require('qrcode-terminal');
        
        console.log('\n===== 微信扫码登录 =====');
        console.log('请使用微信扫描以下二维码登录：');
        console.log(`二维码URL: ${qrCodeUrl}`);
        console.log('');
        
        // 生成终端二维码
        qrcode.generate(qrCodeUrl, { small: true }, function(qrString) {
            console.log(qrString);
            console.log('');
            console.log('扫码后请等待登录完成...');
        });
        
        return qrCodeUrl;
        
    } catch (error) {
        console.error('[错误] 二维码生成失败:', error.message);
        console.error('[调试] 错误堆栈:', error.stack);
        
        // 提供手动方案
        console.log('\n===== 手动登录方案 =====');
        console.log('请手动访问以下URL获取二维码：');
        console.log('http://127.0.0.1:8057/login/GetQrCodeUrl');
        console.log('或者在浏览器中打开该URL复制二维码链接');
        
        throw error;
    }
}

// 修复后的提取微信URL函数
function extractWeixinUrl(apiResponse) {
    try {
        console.log('[调试] 提取微信URL，原始响应类型:', typeof apiResponse);
        
        if (!apiResponse) {
            console.log('[错误] API响应为空');
            return null;
        }
        
        // 如果是字符串，直接检查
        if (typeof apiResponse === 'string') {
            if (apiResponse.includes('login.weixin.qq.com')) {
                return apiResponse;
            }
            return null;
        }
        
        // 如果是对象，使用getLoginQrcode函数的逻辑
        const possibleFields = [
            'url', 'qrCode', 'qr_code', 'qr_url', 'login_url',
            'Data.url', 'Data.qrCode', 'result.url', 'data.url'
        ];
        
        for (const field of possibleFields) {
            let value;
            
            if (field.includes('.')) {
                const parts = field.split('.');
                value = apiResponse;
                for (const part of parts) {
                    if (value && typeof value === 'object' && value[part]) {
                        value = value[part];
                    } else {
                        value = null;
                        break;
                    }
                }
            } else {
                value = apiResponse[field];
            }
            
            if (value && typeof value === 'string' && value.includes('login.weixin.qq.com')) {
                console.log(`[成功] 从字段 "${field}" 提取到微信URL: ${value}`);
                return value;
            }
        }
        
        console.log('[警告] 无法提取微信URL，返回null');
        return null;
        
    } catch (error) {
        console.error('[错误] 提取微信URL失败:', error.message);
        return null;
    }
}

// 导出修复后的函数
module.exports = {
    getLoginQrcode,
    extractWeixinUrl
};

// 使用说明
console.log(`
===== Node.js客户端修复说明 =====

1. 将此文件保存为 nodejs-client-fix.js

2. 在 wx_auth_manager.js 文件中，找到第249行附近的 getLoginQrcode 函数

3. 替换为本文件中的 getLoginQrcode 函数

4. 或者在 wx_auth_manager.js 开头添加：
   const { getLoginQrcode, extractWeixinUrl } = require('./nodejs-client-fix');

5. 修复后的函数会：
   - 尝试从50+个可能的字段中提取二维码URL
   - 如果失败，使用调试接口获取URL
   - 提供详细的调试信息
   - 在失败时提供手动方案

6. 测试修复：
   node start

修复后应该能正确显示二维码！
`);
