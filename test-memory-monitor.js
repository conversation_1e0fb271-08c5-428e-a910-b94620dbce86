/**
 * Go程序内存监控器测试脚本
 * 用于测试内存监控和自动重启功能
 */

const GoMemoryMonitor = require('./go-memory-monitor');
const fs = require('fs');
const path = require('path');

class MemoryMonitorTester {
  constructor() {
    this.testResults = [];
    this.monitor = null;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始Go程序内存监控器测试');
    console.log('='.repeat(50));

    try {
      // 基础功能测试
      await this.testBasicFunctionality();
      
      // 配置测试
      await this.testConfiguration();
      
      // 内存监控测试
      await this.testMemoryMonitoring();
      
      // 重启功能测试
      await this.testRestartFunctionality();
      
      // 错误处理测试
      await this.testErrorHandling();
      
      // 显示测试结果
      this.displayTestResults();
      
    } catch (error) {
      console.error(`❌ 测试过程中出现错误: ${error.message}`);
    } finally {
      // 清理资源
      await this.cleanup();
    }
  }

  /**
   * 基础功能测试
   */
  async testBasicFunctionality() {
    console.log('\n📋 测试1: 基础功能测试');
    
    try {
      // 测试监控器创建
      this.monitor = new GoMemoryMonitor({
        goProgram: './test-dummy-program.sh', // 使用测试程序
        monitorInterval: 5000, // 5秒间隔用于测试
        memoryWarningThreshold: 100,
        memoryRestartThreshold: 200,
        logFile: 'logs/test-monitor.log',
        enableConsoleLog: false
      });

      this.addTestResult('监控器创建', true, '成功创建监控器实例');

      // 测试状态获取
      const status = this.monitor.getStatus();
      this.addTestResult('状态获取', 
        typeof status === 'object' && status.hasOwnProperty('isMonitoring'),
        `状态对象: ${JSON.stringify(status, null, 2)}`
      );

    } catch (error) {
      this.addTestResult('基础功能测试', false, error.message);
    }
  }

  /**
   * 配置测试
   */
  async testConfiguration() {
    console.log('\n⚙️ 测试2: 配置测试');
    
    try {
      // 测试默认配置
      const defaultMonitor = new GoMemoryMonitor();
      const defaultConfig = defaultMonitor.config;
      
      this.addTestResult('默认配置', 
        defaultConfig.monitorInterval === 30000 &&
        defaultConfig.memoryWarningThreshold === 500 &&
        defaultConfig.memoryRestartThreshold === 1000,
        `默认配置正确`
      );

      // 测试自定义配置
      const customMonitor = new GoMemoryMonitor({
        monitorInterval: 10000,
        memoryWarningThreshold: 300,
        memoryRestartThreshold: 600
      });
      
      this.addTestResult('自定义配置',
        customMonitor.config.monitorInterval === 10000 &&
        customMonitor.config.memoryWarningThreshold === 300 &&
        customMonitor.config.memoryRestartThreshold === 600,
        '自定义配置正确应用'
      );

    } catch (error) {
      this.addTestResult('配置测试', false, error.message);
    }
  }

  /**
   * 内存监控测试
   */
  async testMemoryMonitoring() {
    console.log('\n📊 测试3: 内存监控测试');
    
    try {
      // 创建测试用的虚拟程序
      await this.createDummyProgram();
      
      // 创建监控器
      const testMonitor = new GoMemoryMonitor({
        goProgram: './test-dummy-program.sh',
        monitorInterval: 2000, // 2秒间隔
        memoryWarningThreshold: 50,
        memoryRestartThreshold: 100,
        logFile: 'logs/test-memory-monitor.log',
        enableConsoleLog: false
      });

      let memoryCheckReceived = false;
      let warningReceived = false;

      // 监听事件
      testMonitor.on('memoryCheck', (data) => {
        memoryCheckReceived = true;
        console.log(`  📊 内存检查: ${data.memoryMB}MB`);
      });

      testMonitor.on('memoryWarning', (data) => {
        warningReceived = true;
        console.log(`  ⚠️ 内存警告: ${data.memoryMB}MB`);
      });

      // 启动监控
      await testMonitor.start();
      
      // 等待几次监控周期
      await this.sleep(6000);
      
      this.addTestResult('内存监控启动', true, '监控成功启动');
      this.addTestResult('内存检查事件', memoryCheckReceived, '收到内存检查事件');
      
      // 停止监控
      await testMonitor.shutdown();
      
    } catch (error) {
      this.addTestResult('内存监控测试', false, error.message);
    }
  }

  /**
   * 重启功能测试
   */
  async testRestartFunctionality() {
    console.log('\n🔄 测试4: 重启功能测试');
    
    try {
      // 创建测试监控器
      const restartMonitor = new GoMemoryMonitor({
        goProgram: './test-dummy-program.sh',
        monitorInterval: 1000,
        memoryWarningThreshold: 10,
        memoryRestartThreshold: 20,
        minRestartInterval: 1000, // 1秒最小间隔用于测试
        logFile: 'logs/test-restart-monitor.log',
        enableConsoleLog: false
      });

      let restartStarted = false;
      let restartCompleted = false;

      // 监听重启事件
      restartMonitor.on('restartStarted', (data) => {
        restartStarted = true;
        console.log(`  🔄 重启开始: ${data.reason}`);
      });

      restartMonitor.on('restartCompleted', (data) => {
        restartCompleted = true;
        console.log(`  ✅ 重启完成: 第${data.count}次`);
      });

      // 启动监控
      await restartMonitor.start();
      
      // 手动触发重启
      const manualRestartSuccess = await restartMonitor.manualRestart();
      this.addTestResult('手动重启', manualRestartSuccess, '手动重启功能正常');
      
      // 等待事件
      await this.sleep(3000);
      
      this.addTestResult('重启事件', restartStarted && restartCompleted, '重启事件正常触发');
      
      // 停止监控
      await restartMonitor.shutdown();
      
    } catch (error) {
      this.addTestResult('重启功能测试', false, error.message);
    }
  }

  /**
   * 错误处理测试
   */
  async testErrorHandling() {
    console.log('\n❌ 测试5: 错误处理测试');
    
    try {
      // 测试不存在的程序
      const errorMonitor = new GoMemoryMonitor({
        goProgram: './non-existent-program',
        logFile: 'logs/test-error-monitor.log',
        enableConsoleLog: false
      });

      let errorCaught = false;
      try {
        await errorMonitor.start();
      } catch (error) {
        errorCaught = true;
        console.log(`  ❌ 预期错误: ${error.message}`);
      }

      this.addTestResult('错误处理', errorCaught, '正确处理不存在的程序文件');
      
      // 测试重启保护
      const protectionMonitor = new GoMemoryMonitor({
        goProgram: './test-dummy-program.sh',
        minRestartInterval: 60000, // 1分钟间隔
        logFile: 'logs/test-protection-monitor.log',
        enableConsoleLog: false
      });

      await protectionMonitor.start();
      
      // 连续两次重启，第二次应该被保护机制阻止
      const firstRestart = await protectionMonitor.manualRestart();
      await this.sleep(1000);
      const secondRestart = await protectionMonitor.manualRestart();
      
      this.addTestResult('重启保护', firstRestart && !secondRestart, '重启保护机制正常工作');
      
      await protectionMonitor.shutdown();
      
    } catch (error) {
      this.addTestResult('错误处理测试', false, error.message);
    }
  }

  /**
   * 创建测试用的虚拟程序
   */
  async createDummyProgram() {
    const dummyScript = `#!/bin/bash
# 测试用的虚拟Go程序
echo "Dummy Go program started"
while true; do
  sleep 1
done
`;

    fs.writeFileSync('./test-dummy-program.sh', dummyScript);
    
    // 设置执行权限
    try {
      const { exec } = require('child_process');
      await new Promise((resolve, reject) => {
        exec('chmod +x ./test-dummy-program.sh', (error) => {
          if (error) reject(error);
          else resolve();
        });
      });
    } catch (error) {
      console.log('  ⚠️ 无法设置执行权限，可能在Windows环境');
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      name: testName,
      success,
      message,
      timestamp: new Date().toISOString()
    });

    const status = success ? '✅' : '❌';
    console.log(`  ${status} ${testName}: ${message}`);
  }

  /**
   * 显示测试结果
   */
  displayTestResults() {
    console.log('\n📊 测试结果汇总');
    console.log('='.repeat(50));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${Math.round(passedTests / totalTests * 100)}%`);

    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.message}`);
        });
    }

    // 保存测试报告
    this.saveTestReport();
  }

  /**
   * 保存测试报告
   */
  saveTestReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.success).length,
        failed: this.testResults.filter(r => !r.success).length
      },
      results: this.testResults
    };

    // 确保日志目录存在
    if (!fs.existsSync('logs')) {
      fs.mkdirSync('logs', { recursive: true });
    }

    fs.writeFileSync('logs/test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 测试报告已保存到: logs/test-report.json');
  }

  /**
   * 清理测试资源
   */
  async cleanup() {
    console.log('\n🧹 清理测试资源...');
    
    try {
      // 停止监控器
      if (this.monitor) {
        await this.monitor.shutdown();
      }

      // 删除测试文件
      if (fs.existsSync('./test-dummy-program.sh')) {
        fs.unlinkSync('./test-dummy-program.sh');
      }

      console.log('✅ 清理完成');
      
    } catch (error) {
      console.log(`⚠️ 清理时出现错误: ${error.message}`);
    }
  }

  /**
   * 工具方法：睡眠
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new MemoryMonitorTester();
  tester.runAllTests().then(() => {
    console.log('\n🎉 所有测试完成！');
    process.exit(0);
  }).catch(error => {
    console.error(`测试失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = MemoryMonitorTester;
