#!/bin/bash

# 真实微信协议版本运行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 启动真实微信协议版本${NC}"
echo "======================================================"

# 检查可执行文件
if [ ! -f "myapp-linux-bot3-real" ]; then
    echo -e "${RED}❌ 可执行文件不存在: myapp-linux-bot3-real${NC}"
    echo "请先运行编译脚本："
    echo "  chmod +x compile-real-wechat.sh && ./compile-real-wechat.sh"
    echo "或者："
    echo "  go build -ldflags='-s -w' -o myapp-linux-bot3-real main_complete_api.go"
    exit 1
fi

# 检查端口占用
if netstat -tlnp 2>/dev/null | grep -q ":8057 "; then
    echo -e "${YELLOW}⚠️ 端口8057已被占用${NC}"
    echo "正在尝试停止占用该端口的程序..."
    
    # 尝试优雅停止
    pkill -f "myapp-linux-bot3" || true
    sleep 2
    
    # 检查是否还在占用
    if netstat -tlnp 2>/dev/null | grep -q ":8057 "; then
        echo -e "${RED}❌ 端口8057仍被占用，请手动停止相关程序${NC}"
        echo "可以使用以下命令查看占用进程："
        echo "  netstat -tlnp | grep :8057"
        echo "  lsof -i :8057"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 端口已释放${NC}"
fi

# 设置执行权限
chmod +x myapp-linux-bot3-real

# 创建日志目录
mkdir -p logs

echo -e "${GREEN}✅ 环境检查完成${NC}"
echo ""
echo -e "${BLUE}📋 真实微信协议特性：${NC}"
echo "  ✅ 使用真实的微信API端点: https://login.wx.qq.com"
echo "  ✅ 生成真实可用的微信登录二维码"
echo "  ✅ 真实的登录状态检查"
echo "  ✅ 完全兼容Node.js客户端"
echo "  ✅ 智能降级机制"
echo ""
echo -e "${BLUE}🔧 API接口：${NC}"
echo "  📍 健康检查: http://localhost:8057/health"
echo "  📍 获取二维码: http://localhost:8057/login/GetQrCodeUrl"
echo "  📍 完整二维码API: http://localhost:8057/login/GetLoginQrCodeNew"
echo "  📍 登录状态: http://localhost:8057/login/GetLoginStatus"
echo ""
echo -e "${YELLOW}🚀 启动真实微信协议服务器...${NC}"
echo "按 Ctrl+C 停止服务器"
echo ""

# 启动程序并记录日志
echo "$(date): Starting Real WeChat Protocol Bot..." >> logs/real-wechat-bot.log
./myapp-linux-bot3-real 2>&1 | tee -a logs/real-wechat-bot.log
