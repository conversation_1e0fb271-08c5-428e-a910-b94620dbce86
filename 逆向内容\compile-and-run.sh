#!/bin/bash

# 第三个微信机器人一键编译和运行脚本
# 包含完整的编译、运行和测试流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🤖 第三个微信机器人一键编译和运行脚本${NC}"
echo "======================================================"

# 检查Go环境
echo -e "${YELLOW}🔍 检查Go环境...${NC}"
if ! command -v go &> /dev/null; then
    echo -e "${RED}❌ Go未安装，请先安装Go 1.19或更高版本${NC}"
    exit 1
fi

GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo -e "${GREEN}✅ 检测到Go版本: $GO_VERSION${NC}"

# 设置环境变量
export CGO_ENABLED=0
export GOOS=linux
export GOARCH=amd64
export GOPROXY=https://goproxy.cn,direct
export GOSUMDB=sum.golang.google.cn

echo -e "${YELLOW}🌐 使用中国代理: $GOPROXY${NC}"

# 清理环境
echo -e "${YELLOW}🧹 清理编译环境...${NC}"
rm -f go.sum myapp-linux-bot3-complete

# 编译程序
echo -e "${YELLOW}🔨 编译第三个微信机器人...${NC}"
go mod tidy
go build -ldflags="-s -w" -o myapp-linux-bot3-complete main_complete_api.go

# 检查编译结果
if [ -f "myapp-linux-bot3-complete" ]; then
    echo -e "${GREEN}✅ 编译成功！${NC}"
    ls -lh myapp-linux-bot3-complete
else
    echo -e "${RED}❌ 编译失败！${NC}"
    exit 1
fi

# 检查配置文件
echo -e "${YELLOW}🔍 检查配置文件...${NC}"
if [ ! -f "assets/setting.json" ]; then
    echo -e "${RED}❌ 配置文件 assets/setting.json 不存在${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 配置文件检查通过${NC}"

# 检查端口
echo -e "${YELLOW}🔍 检查端口8057...${NC}"
if netstat -tlnp 2>/dev/null | grep -q ":8057 "; then
    echo -e "${RED}❌ 端口8057已被占用，请先停止占用该端口的程序${NC}"
    echo "占用情况:"
    netstat -tlnp 2>/dev/null | grep ":8057 "
    exit 1
else
    echo -e "${GREEN}✅ 端口8057可用${NC}"
fi

# 设置执行权限
chmod +x myapp-linux-bot3-complete

# 创建日志目录
mkdir -p logs

echo ""
echo -e "${GREEN}🎉 编译完成！准备启动第三个微信机器人...${NC}"
echo ""
echo -e "${BLUE}🤖 第三个微信机器人信息:${NC}"
echo "   📡 端口: 8057"
echo "   🔑 机器人ID: wechat_bot_3"
echo "   🗄️  Redis数据库: Db 3"
echo "   🗃️  MySQL数据库: wechat_bot3"
echo "   📨 RocketMQ主题: wx_sync_msg_topic_bot3"
echo "   🔑 管理密钥: 3rd_bot_admin_key_2025_secure"
echo ""
echo -e "${BLUE}🔧 支持的API接口:${NC}"
echo "   ✅ /health - 健康检查"
echo "   ✅ /login/WakeUpLogin - 唤醒登录"
echo "   ✅ /login/GetLoginStatus - 获取登录状态"
echo "   ✅ /login/GetLoginQrCodeNew - 获取登录二维码"
echo "   ✅ /login/GenAuthKey - 生成授权码"
echo "   ✅ /admin/GenAuthKey - 管理员生成授权码"
echo "   ✅ /admin/GenWxAuthKey - 生成微信授权码"
echo "   ✅ /api/status - API状态"
echo "   ✅ /ws - WebSocket端点"
echo ""

# 询问是否立即运行
read -p "是否立即启动程序？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🚀 启动第三个微信机器人...${NC}"
    echo "程序启动中，按 Ctrl+C 停止..."
    echo ""
    
    # 记录启动日志
    echo "$(date): Starting WeChat Bot 3 (Complete API)..." >> logs/bot3-complete.log
    
    # 启动程序
    ./myapp-linux-bot3-complete 2>&1 | tee -a logs/bot3-complete.log
    
    # 记录停止日志
    echo "$(date): WeChat Bot 3 (Complete API) stopped" >> logs/bot3-complete.log
else
    echo -e "${YELLOW}📋 手动启动命令:${NC}"
    echo "   ./myapp-linux-bot3-complete"
    echo ""
    echo -e "${YELLOW}🔍 测试命令:${NC}"
    echo "   curl http://localhost:8057/health"
    echo "   ./test-all-apis.sh"
    echo ""
    echo -e "${GREEN}✅ 编译完成，程序已准备就绪！${NC}"
fi
