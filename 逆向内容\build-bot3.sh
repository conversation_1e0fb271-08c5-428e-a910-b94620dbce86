#!/bin/bash

# 第三个微信机器人编译脚本
# 修复RocketMQ问题的完整版本

set -e

echo "🤖 开始编译第三个微信机器人..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go 1.19或更高版本"
    exit 1
fi

# 检查Go版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo "✅ 检测到Go版本: $GO_VERSION"

# 清理go.sum文件（避免格式问题）
echo "🧹 清理go.sum文件..."
rm -f go.sum

# 设置环境变量
export CGO_ENABLED=0
export GOOS=linux
export GOARCH=amd64

# 清理之前的构建
echo "🧹 清理之前的构建文件..."
rm -f myapp-linux-bot3
rm -f myapp-linux-bot3.tar.gz

# 配置Go代理（解决网络问题）
echo "🌐 配置Go代理..."
export GOPROXY=https://goproxy.cn,https://goproxy.io,direct
export GOSUMDB=sum.golang.google.cn
export GO111MODULE=on

# 初始化Go模块并下载依赖
echo "📦 初始化Go模块并下载依赖..."
go mod tidy

# 验证依赖
echo "🔍 验证依赖..."
go mod verify

# 构建应用
echo "🔨 构建第三个微信机器人..."
go build -ldflags="-s -w -X main.version=$(date +%Y%m%d%H%M%S)" -o myapp-linux-bot3 .

# 检查构建结果
if [ -f "myapp-linux-bot3" ]; then
    echo "✅ 构建成功！"
    
    # 显示文件信息
    ls -lh myapp-linux-bot3
    
    # 创建部署包
    echo "📦 创建部署包..."
    tar -czf myapp-linux-bot3.tar.gz myapp-linux-bot3 assets/
    
    echo "🎉 部署包创建完成: myapp-linux-bot3.tar.gz"
    
    # 显示配置信息
    echo ""
    echo "🤖 第三个机器人配置信息:"
    echo "   📡 端口: 8057"
    echo "   🗄️  Redis数据库: Db 3"
    echo "   🗃️  MySQL数据库: wechat_bot3"
    echo "   📨 RocketMQ主题: wx_sync_msg_topic_bot3"
    echo "   🔑 机器人ID: wechat_bot_3"
    echo ""
    echo "🔧 RocketMQ修复说明:"
    echo "   ✅ 修复了goroutine泄漏问题"
    echo "   ✅ 修复了重连时崩溃问题"
    echo "   ✅ 修复了内存无限增长问题"
    echo "   ✅ 增加了健康检查机制"
    echo "   ✅ 增加了优雅关闭机制"
    echo ""
    echo "📋 部署前准备:"
    echo "   1. 创建MySQL数据库: CREATE DATABASE wechat_bot3;"
    echo "   2. 创建RocketMQ Topic: wx_sync_msg_topic_bot3"
    echo "   3. 确保端口8057未被占用"
    echo ""
    echo "🚀 运行命令:"
    echo "   ./myapp-linux-bot3"
    echo ""
    echo "🔍 验证命令:"
    echo "   curl http://localhost:8057/health"
    echo "   wscat -c ws://localhost:8057/ws"
    echo ""
    
else
    echo "❌ 构建失败！"
    exit 1
fi

echo "🎉 第三个微信机器人编译完成！"
