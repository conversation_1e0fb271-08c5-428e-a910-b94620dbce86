#!/bin/bash

# 真实微信协议测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 真实微信协议测试脚本${NC}"
echo "======================================================"

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_step() {
    local test_name="$1"
    local command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}📋 测试 $TOTAL_TESTS: $test_name${NC}"
    
    if eval "$command"; then
        echo -e "${GREEN}✅ 测试通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ 测试失败${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    echo ""
}

# 1. 检查Go环境
echo -e "${PURPLE}🔍 第一阶段：环境检查${NC}"
echo "======================================================"

test_step "Go环境检查" "command -v go &> /dev/null && echo 'Go版本:' && go version"

# 2. 编译程序
echo -e "${PURPLE}🔨 第二阶段：编译真实微信协议版本${NC}"
echo "======================================================"

test_step "清理环境" "rm -f myapp-linux-bot3-real go.sum"

test_step "设置环境变量" "export CGO_ENABLED=0 && export GOOS=linux && export GOARCH=amd64 && export GOPROXY=https://goproxy.cn,direct && echo '环境变量设置完成'"

test_step "下载依赖" "go mod tidy"

test_step "编译真实微信协议版本" "go build -ldflags='-s -w' -o myapp-linux-bot3-real main_complete_api.go && ls -lh myapp-linux-bot3-real"

# 3. 启动API服务器
echo -e "${PURPLE}🚀 第三阶段：启动真实微信协议API服务器${NC}"
echo "======================================================"

# 检查端口是否被占用
if netstat -tlnp 2>/dev/null | grep -q ":8057 "; then
    echo -e "${YELLOW}⚠️ 端口8057已被占用，尝试停止现有进程...${NC}"
    pkill -f "myapp-linux-bot3" || true
    sleep 2
fi

test_step "设置执行权限" "chmod +x myapp-linux-bot3-real"

echo -e "${YELLOW}🚀 启动真实微信协议API服务器...${NC}"
./myapp-linux-bot3-real &
SERVER_PID=$!

# 等待服务器启动
sleep 5

test_step "检查服务器进程" "ps -p $SERVER_PID > /dev/null && echo '服务器进程运行正常'"

# 4. 测试真实微信协议API
echo -e "${PURPLE}🔧 第四阶段：测试真实微信协议API${NC}"
echo "======================================================"

BASE_URL="http://localhost:8057"

test_step "健康检查接口" "curl -s $BASE_URL/health | grep -q 'wechat_bot_3' && echo 'API响应正常'"

echo -e "${YELLOW}🧪 测试真实微信UUID获取...${NC}"
UUID_RESPONSE=$(curl -s $BASE_URL/login/GetQrCodeUrl)
echo "获取到的二维码URL: $UUID_RESPONSE"

if echo "$UUID_RESPONSE" | grep -q "login.wx.qq.com"; then
    echo -e "${GREEN}✅ 真实微信二维码URL获取成功${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
    
    # 验证URL格式
    if echo "$UUID_RESPONSE" | grep -q "https://login.wx.qq.com/qrcode/"; then
        echo -e "${GREEN}✅ 二维码URL格式正确${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ 二维码URL格式错误${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
else
    echo -e "${RED}❌ 真实微信二维码URL获取失败${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 2))

echo -e "${YELLOW}🧪 测试完整二维码API...${NC}"
QR_API_RESPONSE=$(curl -s -X POST "$BASE_URL/login/GetLoginQrCodeNew" \
    -H "Content-Type: application/json" \
    -d '{"Check":false,"Proxy":""}')

echo "完整二维码API响应:"
echo "$QR_API_RESPONSE" | jq '.' 2>/dev/null || echo "$QR_API_RESPONSE"

if echo "$QR_API_RESPONSE" | grep -q "login.wx.qq.com"; then
    echo -e "${GREEN}✅ 完整二维码API返回真实微信URL${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ 完整二维码API未返回真实微信URL${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo -e "${YELLOW}🧪 测试真实登录状态检查...${NC}"
LOGIN_STATUS_RESPONSE=$(curl -s "$BASE_URL/login/GetLoginStatus")

echo "登录状态检查响应:"
echo "$LOGIN_STATUS_RESPONSE" | jq '.' 2>/dev/null || echo "$LOGIN_STATUS_RESPONSE"

if echo "$LOGIN_STATUS_RESPONSE" | grep -q '"status"'; then
    echo -e "${GREEN}✅ 登录状态检查API正常${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ 登录状态检查API异常${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))

# 5. 验证真实微信协议特性
echo -e "${PURPLE}🎯 第五阶段：验证真实微信协议特性${NC}"
echo "======================================================"

echo -e "${YELLOW}🔍 验证微信协议特性...${NC}"

# 检查是否使用了真实的微信API端点
if echo "$UUID_RESPONSE" | grep -q "https://login.wx.qq.com"; then
    echo -e "${GREEN}✅ 使用真实的微信API端点${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ 未使用真实的微信API端点${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# 检查UUID格式
if echo "$UUID_RESPONSE" | grep -E "uuid_[0-9]+|[a-zA-Z0-9_-]+"; then
    echo -e "${GREEN}✅ UUID格式符合微信协议${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ UUID格式不符合微信协议${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 2))

# 6. 性能测试
echo -e "${PURPLE}⚡ 第六阶段：性能测试${NC}"
echo "======================================================"

test_step "内存使用检查" "ps -p $SERVER_PID -o pid,ppid,cmd,%mem,%cpu --no-headers && echo '内存使用正常'"

test_step "API响应时间测试" "time curl -s $BASE_URL/health > /dev/null && echo 'API响应时间正常'"

# 7. 清理和总结
echo -e "${PURPLE}🧹 第七阶段：清理和总结${NC}"
echo "======================================================"

# 停止服务器
if ps -p $SERVER_PID > /dev/null; then
    echo -e "${YELLOW}🛑 停止API服务器...${NC}"
    kill $SERVER_PID
    sleep 2
    
    if ps -p $SERVER_PID > /dev/null; then
        kill -9 $SERVER_PID
    fi
    
    echo -e "${GREEN}✅ API服务器已停止${NC}"
fi

# 测试结果统计
echo "======================================================"
echo -e "${BLUE}📊 真实微信协议测试结果统计${NC}"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"

SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
echo "成功率: ${SUCCESS_RATE}%"

echo ""
echo -e "${BLUE}🎯 真实微信协议测试项目总结${NC}"
echo "✅ Go环境检查"
echo "✅ 真实微信协议版本编译"
echo "✅ API服务器启动"
echo "✅ 真实微信UUID获取"
echo "✅ 真实微信二维码生成"
echo "✅ 真实登录状态检查"
echo "✅ 微信协议特性验证"
echo "✅ 性能测试"

if [ $FAILED_TESTS -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 所有测试通过！真实微信协议集成100%成功！${NC}"
    echo ""
    echo -e "${BLUE}📋 真实微信协议特性确认：${NC}"
    echo "✅ 使用真实的微信API端点"
    echo "✅ 生成真实可用的二维码"
    echo "✅ 真实的登录状态检查"
    echo "✅ 完全兼容Node.js客户端"
    echo ""
    echo -e "${GREEN}🏆 第三个微信机器人现在使用真实的微信协议！${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查相关问题${NC}"
    exit 1
fi
