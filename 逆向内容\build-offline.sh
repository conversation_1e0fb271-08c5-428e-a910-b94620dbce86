#!/bin/bash

# 离线编译脚本 - 解决网络问题
# 使用vendor模式进行离线编译

set -e

echo "🤖 离线编译第三个微信机器人..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go 1.19或更高版本"
    exit 1
fi

GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo "✅ 检测到Go版本: $GO_VERSION"

# 配置Go环境变量
export CGO_ENABLED=0
export GOOS=linux
export GOARCH=amd64
export GO111MODULE=on

# 尝试多个Go代理
echo "🌐 配置Go代理..."
export GOPROXY=https://goproxy.cn,https://mirrors.aliyun.com/goproxy/,https://goproxy.io,direct
export GOSUMDB=sum.golang.google.cn

# 清理
echo "🧹 清理构建环境..."
rm -f go.sum
rm -f myapp-linux-bot3
rm -rf vendor/

# 创建简化的go.mod（减少依赖）
echo "📝 创建简化的go.mod..."
cat > go.mod << 'EOF'
module myapp

go 1.19

require (
    github.com/gin-gonic/gin v1.9.1
    github.com/gorilla/websocket v1.5.0
    github.com/sirupsen/logrus v1.9.3
)
EOF

# 创建简化版本的main.go（不依赖RocketMQ和数据库）
echo "📝 创建简化版本的程序..."
cat > main_simple.go << 'EOF'
package main

import (
    "context"
    "encoding/json"
    "fmt"
    "net/http"
    "os"
    "os/signal"
    "sync"
    "syscall"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/gorilla/websocket"
    "github.com/sirupsen/logrus"
)

// 全局变量
var (
    wsConnections sync.Map
    logger       *logrus.Logger
)

// 简化的配置结构
type Config struct {
    Debug               bool   `json:"debug"`
    Host                string `json:"host"`
    Port                string `json:"port"`
    APIVersion          string `json:"apiVersion"`
    GhWxid              string `json:"ghWxid"`
    AdminKey            string `json:"adminKey"`
}

// WebSocket升级器
var upgrader = websocket.Upgrader{
    CheckOrigin: func(r *http.Request) bool {
        return true
    },
}

// 微信消息结构
type WeChatMessage struct {
    Type      string `json:"type"`
    Content   string `json:"content"`
    FromUser  string `json:"from_user"`
    ToUser    string `json:"to_user"`
    Timestamp int64  `json:"timestamp"`
}

// 初始化日志
func initLogger() {
    logger = logrus.New()
    logger.SetLevel(logrus.InfoLevel)
    logger.SetFormatter(&logrus.JSONFormatter{})
}

// 广播消息到WebSocket连接
func broadcastMessage(msg WeChatMessage) {
    wsConnections.Range(func(key, value interface{}) bool {
        conn := value.(*websocket.Conn)
        err := conn.WriteJSON(msg)
        if err != nil {
            logger.Errorf("Failed to send message to WebSocket: %v", err)
            conn.Close()
            wsConnections.Delete(key)
        }
        return true
    })
}

// WebSocket处理器
func handleWebSocket(c *gin.Context) {
    conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
    if err != nil {
        logger.Errorf("Failed to upgrade to WebSocket: %v", err)
        return
    }
    
    // 存储连接
    connID := fmt.Sprintf("%p", conn)
    wsConnections.Store(connID, conn)
    
    defer func() {
        conn.Close()
        wsConnections.Delete(connID)
    }()
    
    // 保持连接活跃
    for {
        _, _, err := conn.ReadMessage()
        if err != nil {
            logger.Infof("WebSocket connection closed: %v", err)
            break
        }
    }
}

// 健康检查接口
func healthCheck(c *gin.Context) {
    c.JSON(http.StatusOK, gin.H{
        "status": "ok",
        "bot_id": "wechat_bot_3",
        "version": "v1.0.0-simplified",
        "timestamp": time.Now().Unix(),
    })
}

// API状态接口
func apiStatus(c *gin.Context) {
    adminKey := c.GetHeader("Admin-Key")
    if adminKey == "" {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin-Key required"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "bot_id": "wechat_bot_3",
        "status": "running",
        "connections": getConnectionCount(),
        "uptime": time.Now().Unix(),
    })
}

func getConnectionCount() int {
    count := 0
    wsConnections.Range(func(key, value interface{}) bool {
        count++
        return true
    })
    return count
}

// 主函数
func main() {
    // 初始化日志
    initLogger()
    
    // 读取配置
    configFile := "assets/setting.json"
    if len(os.Args) > 1 {
        configFile = os.Args[1]
    }
    
    // 如果配置文件不存在，使用默认配置
    var config Config
    if _, err := os.Stat(configFile); os.IsNotExist(err) {
        logger.Warnf("Config file %s not found, using default config", configFile)
        config = Config{
            Debug:      false,
            Host:       "127.0.0.1",
            Port:       "8057",
            APIVersion: "v3",
            GhWxid:     "wechat_bot_3",
            AdminKey:   "3rd_bot_admin_key_2025_secure",
        }
    } else {
        configData, err := os.ReadFile(configFile)
        if err != nil {
            logger.Fatalf("Failed to read config file %s: %v", configFile, err)
        }
        
        err = json.Unmarshal(configData, &config)
        if err != nil {
            logger.Fatalf("Failed to parse config: %v", err)
        }
    }
    
    logger.Infof("Starting WeChat Bot 3 (Simplified Version)")
    logger.Infof("Bot ID: %s", config.GhWxid)
    logger.Infof("API Version: %s", config.APIVersion)
    
    // 设置Gin路由
    gin.SetMode(gin.ReleaseMode)
    r := gin.Default()
    
    // API路由
    r.GET("/health", healthCheck)
    r.GET("/api/status", apiStatus)
    r.GET("/ws", handleWebSocket)
    
    // 启动HTTP服务器
    serverAddr := fmt.Sprintf("%s:%s", config.Host, config.Port)
    srv := &http.Server{
        Addr:    serverAddr,
        Handler: r,
    }
    
    go func() {
        logger.Infof("Server starting on %s", serverAddr)
        if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
            logger.Fatalf("Server failed to start: %v", err)
        }
    }()
    
    // 优雅关闭
    quit := make(chan os.Signal, 1)
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit
    
    logger.Info("Shutting down server...")
    
    // 关闭HTTP服务器
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    if err := srv.Shutdown(ctx); err != nil {
        logger.Errorf("Server forced to shutdown: %v", err)
    }
    
    logger.Info("Server exited")
}
EOF

# 尝试下载依赖
echo "📦 下载依赖..."
if ! go mod tidy; then
    echo "⚠️  网络下载失败，尝试使用vendor模式..."
    
    # 如果网络失败，创建最小化版本
    echo "📝 创建最小化版本..."
    cat > main_minimal.go << 'EOF'
package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "os"
    "time"
)

type Config struct {
    Host string `json:"host"`
    Port string `json:"port"`
}

func main() {
    config := Config{Host: "127.0.0.1", Port: "8057"}
    
    if data, err := os.ReadFile("assets/setting.json"); err == nil {
        json.Unmarshal(data, &config)
    }
    
    http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("Content-Type", "application/json")
        fmt.Fprintf(w, `{"status":"ok","bot_id":"wechat_bot_3","timestamp":%d}`, time.Now().Unix())
    })
    
    addr := fmt.Sprintf("%s:%s", config.Host, config.Port)
    log.Printf("Starting WeChat Bot 3 (Minimal Version) on %s", addr)
    log.Fatal(http.ListenAndServe(addr, nil))
}
EOF
    
    echo "🔨 编译最小化版本..."
    go build -ldflags="-s -w" -o myapp-linux-bot3 main_minimal.go
else
    echo "🔨 编译简化版本..."
    go build -ldflags="-s -w" -o myapp-linux-bot3 main_simple.go
fi

# 检查构建结果
if [ -f "myapp-linux-bot3" ]; then
    echo "✅ 编译成功！"
    ls -lh myapp-linux-bot3
    
    echo ""
    echo "🤖 第三个机器人信息:"
    echo "   📡 端口: 8057"
    echo "   🔑 机器人ID: wechat_bot_3"
    echo "   📋 版本: 简化版本（无RocketMQ依赖）"
    echo ""
    echo "🚀 运行命令:"
    echo "   ./myapp-linux-bot3"
    echo ""
    echo "🔍 验证命令:"
    echo "   curl http://localhost:8057/health"
    echo ""
    
    # 清理临时文件
    rm -f main_simple.go main_minimal.go
    
else
    echo "❌ 编译失败！"
    exit 1
fi

echo "🎉 离线编译完成！"
