/**
 * 消息缓存服务
 * 用于缓存消息内容，支持远程转发功能
 */

const logger = require('../utils/logger');

class MessageCacheService {
    constructor(options = {}) {
        // {{ EMERGENCY-FIX: 优化缓存配置 }}
        this.maxCacheSize = options.maxCacheSize || 50; // 降低最大缓存数量
        this.cacheExpireTime = options.cacheExpireTime || 10 * 60 * 1000; // 10分钟过期
        this.lastCleanupTime = Date.now();
        this.cleanupThreshold = 10; // 每10条消息清理一次
        this.messagesSinceCleanup = 0;

        // 消息缓存，格式: { messageId: { content, timestamp, fromUser, fromGroup, ... } }
        this.messageCache = new Map();

        // 最近消息列表（按时间排序）
        this.recentMessages = [];

        // {{ AURA-X: Add - 注册到统一缓存管理器. Approval: 寸止(ID:1751501600). }}
        // 注册到统一缓存管理器
        if (global.cacheManager) {
            global.cacheManager.registerCache('message_cache', this, {
                type: 'message',
                priority: 'normal',
                component: 'message_cache_service',
                description: '消息内容缓存',
                maxSize: this.maxCacheSize,
                ttl: this.cacheExpireTime
            });
            console.log('[消息缓存服务] 已注册到统一缓存管理器');
        }

        // 注册内存清理函数
        this.registerMemoryCleanupFunctions();

        logger.info('MessageCacheService 初始化完成');
    }

    /**
     * 注册内存清理函数
     */
    registerMemoryCleanupFunctions() {
        // 尝试获取全局内存优化器
        const memoryOptimizer = global.memoryOptimizer;
        if (!memoryOptimizer) {
            console.log('[消息缓存服务] 内存优化器不可用，跳过清理函数注册');
            return;
        }

        // 注册消息缓存清理函数
        memoryOptimizer.registerCleanupFunction('messageCacheService', (cleanupActions = {}) => {
            let cleaned = 0;

            try {
                // 获取当前缓存大小
                const currentCacheSize = this.messageCache.size;
                const currentRecentSize = this.recentMessages.length;

                if (cleanupActions.allNonCriticalCache || cleanupActions.forceGarbageCollection) {
                    // Heavy级清理：清理所有消息缓存
                    console.log(`[消息缓存服务] Heavy级清理：清理所有消息缓存 (缓存${currentCacheSize}条，最近${currentRecentSize}条)`);
                    this.clearCache();
                    cleaned = currentCacheSize + currentRecentSize;
                } else if (cleanupActions.messageHistory || cleanupActions.resetCounters) {
                    // 中度清理：清理过期消息和超过1小时的消息
                    const oneHourAgo = Date.now() - (60 * 60 * 1000);
                    const beforeCacheSize = this.messageCache.size;
                    const beforeRecentSize = this.recentMessages.length;

                    // 强制清理过期缓存
                    this.cleanupCache();

                    // 清理超过1小时的消息
                    for (const [messageId, message] of this.messageCache) {
                        if (message.timestamp < oneHourAgo) {
                            this.messageCache.delete(messageId);
                            cleaned++;
                        }
                    }

                    // 清理最近消息列表中超过1小时的消息
                    this.recentMessages = this.recentMessages.filter(msg => msg.timestamp >= oneHourAgo);

                    const afterCacheSize = this.messageCache.size;
                    const afterRecentSize = this.recentMessages.length;
                    cleaned += (beforeCacheSize - afterCacheSize) + (beforeRecentSize - afterRecentSize);

                    console.log(`[消息缓存服务] 中度清理：缓存 ${beforeCacheSize}->${afterCacheSize}，最近 ${beforeRecentSize}->${afterRecentSize}`);
                } else if (cleanupActions.tempData || cleanupActions.expiredCache) {
                    // 轻度清理：只清理明确过期的缓存
                    const beforeCacheSize = this.messageCache.size;
                    const beforeRecentSize = this.recentMessages.length;

                    this.cleanupCache();

                    const afterCacheSize = this.messageCache.size;
                    const afterRecentSize = this.recentMessages.length;
                    cleaned = (beforeCacheSize - afterCacheSize) + (beforeRecentSize - afterRecentSize);

                    console.log(`[消息缓存服务] 轻度清理：缓存 ${beforeCacheSize}->${afterCacheSize}，最近 ${beforeRecentSize}->${afterRecentSize}`);
                }

            } catch (error) {
                console.error('[消息缓存服务] 缓存清理失败:', error.message);
            }

            return { cleaned };
        });

        console.log('[消息缓存服务] 已注册内存清理函数');
    }

    /**
     * 缓存消息
     * @param {Object} message - 消息对象
     * @returns {string} - 消息ID
     */
    cacheMessage(message) {
        try {
            const messageId = this.generateMessageId();
            const timestamp = Date.now();
            
            const cachedMessage = {
                id: messageId,
                content: this.extractContent(message),
                timestamp: timestamp,
                fromUser: this.extractFromUser(message),
                fromGroup: this.extractFromGroup(message),
                messageType: this.extractMessageType(message),
                originalMessage: message,
                cached: true
            };

            // 添加到缓存
            this.messageCache.set(messageId, cachedMessage);
            
            // 添加到最近消息列表
            this.recentMessages.unshift(cachedMessage);
            
            // {{ EMERGENCY-FIX: 减少清理频率，提高性能 }}
            this.messagesSinceCleanup++;
            if (this.messagesSinceCleanup >= this.cleanupThreshold) {
                this.cleanupCache();
                this.messagesSinceCleanup = 0;
            }
            
            logger.info(`消息已缓存: ${messageId}, 内容: ${cachedMessage.content.substring(0, 50)}...`);
            return messageId;
            
        } catch (error) {
            logger.error('缓存消息失败:', error);
            return null;
        }
    }

    /**
     * 获取缓存的消息
     * @param {string} messageId - 消息ID
     * @returns {Object|null} - 缓存的消息对象
     */
    getCachedMessage(messageId) {
        const message = this.messageCache.get(messageId);
        if (message && !this.isExpired(message)) {
            return message;
        }
        return null;
    }

    /**
     * 获取最近的消息列表
     * @param {number} limit - 限制数量
     * @param {string} fromGroup - 可选，过滤特定群组的消息
     * @returns {Array} - 消息列表
     */
    getRecentMessages(limit = 10, fromGroup = null) {
        let messages = this.recentMessages.filter(msg => !this.isExpired(msg));
        
        if (fromGroup) {
            messages = messages.filter(msg => msg.fromGroup === fromGroup);
        }
        
        return messages.slice(0, limit);
    }

    /**
     * 搜索缓存的消息
     * @param {string} keyword - 搜索关键词
     * @param {number} limit - 限制数量
     * @returns {Array} - 匹配的消息列表
     */
    searchMessages(keyword, limit = 10) {
        const results = [];
        
        for (const message of this.recentMessages) {
            if (this.isExpired(message)) continue;
            
            if (message.content.includes(keyword)) {
                results.push(message);
                if (results.length >= limit) break;
            }
        }
        
        return results;
    }

    /**
     * 清理过期和超量的缓存
     */
    cleanupCache() {
        const now = Date.now();
        
        // 清理过期消息
        for (const [messageId, message] of this.messageCache) {
            if (this.isExpired(message)) {
                this.messageCache.delete(messageId);
            }
        }
        
        // 清理最近消息列表中的过期消息
        this.recentMessages = this.recentMessages.filter(msg => !this.isExpired(msg));
        
        // 限制缓存数量
        if (this.recentMessages.length > this.maxCacheSize) {
            const excess = this.recentMessages.splice(this.maxCacheSize);
            excess.forEach(msg => {
                this.messageCache.delete(msg.id);
            });
        }
        
        logger.debug(`缓存清理完成，当前缓存消息数: ${this.messageCache.size}`);
    }

    /**
     * 检查消息是否过期
     * @param {Object} message - 消息对象
     * @returns {boolean} - 是否过期
     */
    isExpired(message) {
        return Date.now() - message.timestamp > this.cacheExpireTime;
    }

    /**
     * 生成消息ID
     * @returns {string} - 消息ID
     */
    generateMessageId() {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 提取消息内容
     * @param {Object} message - 消息对象
     * @returns {string} - 消息内容
     */
    extractContent(message) {
        return message.Content || message._text || message.content || '';
    }

    /**
     * 提取发送者信息
     * @param {Object} message - 消息对象
     * @returns {string} - 发送者ID
     */
    extractFromUser(message) {
        return message.ActualUserName || message.FromUserName || message.senderId || '';
    }

    /**
     * 提取群组信息
     * @param {Object} message - 消息对象
     * @returns {string} - 群组ID
     */
    extractFromGroup(message) {
        const fromUserName = message.FromUserName || message.fromId || '';
        return fromUserName.endsWith('@chatroom') ? fromUserName : '';
    }

    /**
     * 提取消息类型
     * @param {Object} message - 消息对象
     * @returns {string} - 消息类型
     */
    extractMessageType(message) {
        if (message.MsgType !== undefined) {
            return message.MsgType === 1 ? 'text' : 'other';
        }
        return 'text'; // 默认为文本消息
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} - 统计信息
     */
    getStats() {
        const now = Date.now();
        const validMessages = Array.from(this.messageCache.values()).filter(msg => !this.isExpired(msg));
        
        return {
            totalCached: this.messageCache.size,
            validMessages: validMessages.length,
            recentCount: this.recentMessages.length,
            oldestMessage: validMessages.length > 0 ? 
                Math.min(...validMessages.map(msg => msg.timestamp)) : null,
            newestMessage: validMessages.length > 0 ? 
                Math.max(...validMessages.map(msg => msg.timestamp)) : null
        };
    }

    /**
     * 清空所有缓存
     */
    clearCache() {
        this.messageCache.clear();
        this.recentMessages = [];
        logger.info('消息缓存已清空');
    }

    /**
     * 格式化消息用于显示
     * @param {Object} message - 消息对象
     * @returns {string} - 格式化的消息
     */
    formatMessageForDisplay(message) {
        const time = new Date(message.timestamp).toLocaleString();
        const content = message.content.length > 50 ? 
            message.content.substring(0, 50) + '...' : message.content;
        
        return `[${message.id}] ${time} - ${content}`;
    }

    /**
     * 批量缓存消息
     * @param {Array} messages - 消息数组
     * @returns {Array} - 消息ID数组
     */
    batchCacheMessages(messages) {
        const messageIds = [];

        for (const message of messages) {
            const messageId = this.cacheMessage(message);
            if (messageId) {
                messageIds.push(messageId);
            }
        }

        return messageIds;
    }

    /**
     * {{ AURA-X: Add - 统一缓存管理器接口. Approval: 寸止(ID:1751501600). }}
     * 获取缓存大小（统一缓存管理器接口）
     */
    size() {
        return this.messageCache.size;
    }

    /**
     * 清理缓存（统一缓存管理器接口）
     */
    async cleanup(options = {}) {
        const sizeBefore = this.messageCache.size;

        if (options.aggressive || options.allNonCriticalCache) {
            this.clearCache();
            return sizeBefore;
        } else {
            this.cleanupCache();
            return sizeBefore - this.messageCache.size;
        }
    }

    /**
     * 清空缓存（统一缓存管理器接口）
     */
    clear() {
        this.clearCache();
    }

    /**
     * 获取内存使用量估算
     */
    getMemoryUsage() {
        const avgMessageSize = 200; // 假设平均消息大小
        const estimatedBytes = this.messageCache.size * avgMessageSize;

        if (estimatedBytes < 1024) {
            return `${estimatedBytes} B`;
        } else if (estimatedBytes < 1024 * 1024) {
            return `${(estimatedBytes / 1024).toFixed(2)} KB`;
        } else {
            return `${(estimatedBytes / (1024 * 1024)).toFixed(2)} MB`;
        }
    }
}

module.exports = MessageCacheService;
