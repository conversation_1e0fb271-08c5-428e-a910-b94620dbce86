@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 开始构建修复后的myapp-linux...

REM 检查Go环境
where go >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Go未安装，请先安装Go 1.19或更高版本
    pause
    exit /b 1
)

REM 检查Go版本
for /f "tokens=3" %%i in ('go version') do set GO_VERSION=%%i
echo ✅ 检测到Go版本: %GO_VERSION%

REM 设置环境变量
set CGO_ENABLED=0
set GOOS=linux
set GOARCH=amd64

REM 清理之前的构建
echo 🧹 清理之前的构建文件...
if exist myapp-linux-fixed del /f myapp-linux-fixed
if exist myapp-linux-fixed.tar.gz del /f myapp-linux-fixed.tar.gz

REM 下载依赖
echo 📦 下载Go模块依赖...
go mod tidy
go mod download

REM 构建应用
echo 🔨 构建应用程序...
for /f "tokens=1-6 delims=/ :" %%a in ("%date% %time%") do set datetime=%%c%%a%%b%%d%%e%%f
set datetime=%datetime: =0%
go build -ldflags="-s -w -X main.version=%datetime%" -o myapp-linux-fixed .

REM 检查构建结果
if exist myapp-linux-fixed (
    echo ✅ 构建成功！
    
    REM 显示文件信息
    dir myapp-linux-fixed
    
    echo 🎉 构建完成: myapp-linux-fixed
    
    REM 显示修复说明
    echo.
    echo 🔧 RocketMQ修复说明:
    echo    ✅ 修复了goroutine泄漏问题
    echo    ✅ 修复了重连时崩溃问题
    echo    ✅ 修复了内存无限增长问题
    echo    ✅ 增加了健康检查机制
    echo    ✅ 增加了优雅关闭机制
    echo    ✅ 增加了并发控制机制
    echo    ✅ 增加了panic恢复机制
    echo.
    echo 📋 部署说明:
    echo    1. 将 myapp-linux-fixed 和 config.json 上传到Linux服务器
    echo    2. 在Linux服务器上设置执行权限: chmod +x myapp-linux-fixed
    echo    3. 修改 config.json 配置文件
    echo    4. 运行: ./myapp-linux-fixed
    echo.
    
) else (
    echo ❌ 构建失败！
    pause
    exit /b 1
)

pause
