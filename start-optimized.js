/**
 * 优化版启动脚本
 * 集成了统一错误处理、内存管理、日志清理等优化功能
 */

// 启用垃圾回收（如果可用）
if (global.gc) {
  console.log('✅ 垃圾回收已启用');
} else {
  console.log('⚠️  垃圾回收未启用，建议使用 --expose-gc 参数启动');
}

// 导入核心模块
const { globalErrorHandler } = require('./modules/error-handler');
const { globalMemoryManager } = require('./modules/memory-manager');
const { globalAutoLogCleaner } = require('./modules/auto-log-cleaner');
const { defaultLogger } = require('./modules/unified-logger');
const { globalConfigManager } = require('./modules/unified-config-manager');
// {{ AURA-X: Add - 集成新增的优化模块. Approval: 寸止(ID:**********). }}
const { globalConfigAccessor } = require('./modules/unified-config-accessor');
const { globalPerformanceMonitor } = require('./modules/enhanced-performance-monitor');
// {{ EMERGENCY-FIX: 添加紧急监控模块 }}
const EmergencyMonitor = require('./modules/emergency-monitor');

// {{ AURA-X: Fix - 统一使用V2架构组件. Approval: 寸止(ID:**********). }}
// 导入统一V2架构模块
const AuthManager = require('./modules/auth-manager');
const ServiceLoader = require('./modules/service-loader');
// {{ AURA-X: Fix - 恢复使用V2版本，语法错误已修复. Approval: 寸止(ID:**********). }}
const MessageProcessor = require('./modules/message-processor-v2'); // 统一使用V2
const MessageRouter = require('./modules/message-router');
const WebSocketManager = require('./modules/websocket-manager-v2'); // 统一使用V2
const SystemHealthMonitor = require('./modules/system-health-monitor').SystemHealthMonitor;

/**
 * 优化版应用启动器
 */
class OptimizedApp {
  constructor() {
    this.authManager = null;
    this.serviceLoader = null;
    this.messageProcessor = null;
    this.messageRouter = null;
    this.webSocketManager = null;
    this.healthMonitor = null;
    this.isRunning = false;
    this.startTime = Date.now();

    // 绑定事件处理器
    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    // 进程退出处理
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('uncaughtException', (error) => this.handleUncaughtException(error));
    process.on('unhandledRejection', (reason, promise) => this.handleUnhandledRejection(reason, promise));

    // 内存管理器事件
    globalMemoryManager.on('memoryAlert', (data) => {
      if (data.level === 'critical') {
        defaultLogger.error(`内存使用严重: ${Math.round(data.usage / 1024 / 1024)}MB`);
        // 立即执行垃圾回收
        if (global.gc) {
          global.gc();
        }
      } else {
        defaultLogger.warn(`内存使用警告: ${Math.round(data.usage / 1024 / 1024)}MB`);
      }
    });

    // 配置管理器事件
    globalConfigManager.on('configReloaded', (data) => {
      defaultLogger.info(`配置已重新加载: ${data.configName}`);
    });
  }

  /**
   * 启动应用
   */
  async start() {
    try {
      defaultLogger.info('🚀 启动优化版Bot应用...');

      // 1. 初始化配置管理
      await this.initializeConfig();

      // 2. 初始化认证管理器
      await this.initializeAuth();

      // 3. 初始化消息处理器（必须在服务加载器之前）
      await this.initializeMessageProcessor();

      // 4. 初始化服务加载器（需要MessageProcessor）
      await this.initializeServices();

      // 5. 初始化消息路由器
      await this.initializeMessageRouter();

      // 6. 初始化WebSocket管理器
      await this.initializeWebSocket();

      // 7. 初始化健康监控
      await this.initializeHealthMonitor();

      // 8. 初始化增强功能模块
      await this.initializeEnhancedModules();

      // 9. 启动所有服务
      await this.startServices();

      this.isRunning = true;
      const startupTime = Date.now() - this.startTime;

      defaultLogger.info(`✅ 应用启动完成，耗时: ${startupTime}ms`);
      this.printStartupSummary();

      // {{ AURA-X: Add - 检查重启标记文件并发送通知. Approval: 寸止(ID:**********). }}
      // 检查是否是重启后的第一次运行，如果是，发送通知
      await this.checkAndProcessRestartFlag();

    } catch (error) {
      defaultLogger.error(`❌ 应用启动失败: ${error.message}`);
      if (error.stack) {
        defaultLogger.error(`错误堆栈: ${error.stack}`);
      }
      process.exit(1);
    }
  }

  /**
   * 初始化配置管理
   */
  async initializeConfig() {
    defaultLogger.info('📋 初始化配置管理...');
    
    // {{ AURA-X: Modify - 修复认证配置验证逻辑. Approval: 寸止(ID:**********). }}
    // 注册配置验证器（允许空的authKey，因为可能在运行时从其他地方获取）
    globalConfigManager.registerValidator('bot-config', (config) => {
      if (!config.auth) {
        return '缺少认证配置对象';
      }
      // 允许authKey为空，因为可能在运行时从wx_login_info.json或其他地方获取
      return true;
    });

    // 加载主要配置
    const botConfig = globalConfigManager.loadConfig('bot-config', {
      auth: { authKey: '' },
      websocket: { url: 'ws://localhost:8082/ws' },
      features: { enableImageDownload: true }
    });

    defaultLogger.info('✅ 配置管理初始化完成');
  }

  /**
   * 初始化认证管理器
   */
  async initializeAuth() {
    defaultLogger.info('🔐 初始化认证管理器...');

    this.authManager = new AuthManager();
    // AuthManager 在构造函数中自动加载登录信息，无需额外初始化

    defaultLogger.info('✅ 认证管理器初始化完成');
  }

  /**
   * 初始化服务加载器
   */
  async initializeServices() {
    defaultLogger.info('🔧 初始化服务加载器...');

    // 先加载基础配置
    const baseConfig = globalConfigManager.loadConfig('bot-config', {
      auth: { authKey: '' },
      websocket: { url: 'ws://localhost:8082/ws' },
      features: { enableImageDownload: true }
    });

    // 然后加载并合并运行时配置（包含chatRecord等所有配置）
    const runtimeConfig = require('./config/runtime-config.json');

    // 使用 bot-config.js 的完整配置对象（包含 save 方法）
    const botConfig = require('./config/bot-config');

    // 将运行时配置合并到完整的配置对象中，保留原有的方法
    const config = Object.assign(botConfig, runtimeConfig);

    console.log(`[ServiceLoader配置] 合并后的配置键: ${Object.keys(config).join(', ')}`);
    console.log(`[ServiceLoader配置] chatRecord存在: ${!!config.chatRecord}, enabled: ${config.chatRecord?.enabled}`);

    this.serviceLoader = new ServiceLoader(this.authManager, this.messageProcessor, config);
    await this.serviceLoader.initServices(); // 使用正确的方法名

    // 将ServiceLoader设置到MessageProcessor中（建立双向引用）
    if (this.messageProcessor && this.messageProcessor.setServiceLoader) {
      this.messageProcessor.setServiceLoader(this.serviceLoader);
    }

    // 注册服务到内存管理器
    globalMemoryManager.registerCacheManager('serviceLoader', this.serviceLoader);

    defaultLogger.info('✅ 服务加载器初始化完成');
  }

  /**
   * 初始化消息处理器
   */
  async initializeMessageProcessor() {
    defaultLogger.info('🔄 初始化消息处理器...');

    // 加载配置
    const config = globalConfigManager.loadConfig('bot-config', {
      whitelist: { users: { ai: [], aiBlacklist: [] }, groups: { ai: [] } },
      admin: { users: [] },
      reportConfig: {},
      messageProcessing: {}
    });

    this.messageProcessor = new MessageProcessor(this.authManager, {
      whitelist: config.whitelist,
      admin: config.admin,
      reportConfig: config.reportConfig,
      messageProcessing: config.messageProcessing,
      maxQueueSize: config.maxQueueSize || 1000 // {{ EMERGENCY-FIX: 添加队列大小配置 }}
    }, null); // 暂时传入null，稍后在ServiceLoader初始化时设置

    // 注册消息处理器到内存管理器
    globalMemoryManager.registerCacheManager('messageProcessor', this.messageProcessor);

    defaultLogger.info('✅ 消息处理器初始化完成');
  }

  /**
   * 初始化消息路由器
   */
  async initializeMessageRouter() {
    defaultLogger.info('🔄 初始化消息路由器...');

    // 加载配置
    const config = globalConfigManager.loadConfig('bot-config', {});

    // 创建消息路由器
    this.messageRouter = new MessageRouter(this.authManager, this.serviceLoader, config);

    // 将MessageRouter设置到ServiceLoader中
    this.serviceLoader.setMessageRouter(this.messageRouter);

    // 注册消息处理器到路由器
    await this.registerMessageHandlers();

    // 注册消息路由器到内存管理器
    globalMemoryManager.registerCacheManager('messageRouter', this.messageRouter);

    defaultLogger.info('✅ 消息路由器初始化完成');
  }

  /**
   * 注册消息处理器到路由器
   */
  async registerMessageHandlers() {
    defaultLogger.info('📝 注册消息处理器...');

    // 注册文本消息处理器 (类型1)
    this.messageRouter.registerHandler(1, {
      handleMessages: async (messages) => {
        return this.messageProcessor.processMessages(messages);
      }
    }, '消息处理器(类型1)');

    // 注册图片消息处理器 (类型3)
    this.messageRouter.registerHandler(3, {
      handleMessages: async (messages) => {
        return this.messageProcessor.processMessages(messages);
      }
    }, '消息处理器(类型3)');

    // 注册其他类型消息处理器
    const otherTypes = [34, 43, 47, 49, 51, 10000, 10002]; // 语音、视频、表情等
    otherTypes.forEach(type => {
      this.messageRouter.registerHandler(type, {
        handleMessages: async (messages) => {
          return this.messageProcessor.processMessages(messages);
        }
      }, `消息处理器(类型${type})`);
    });

    defaultLogger.info(`✅ 已注册 ${2 + otherTypes.length} 个消息处理器`);
  }

  /**
   * {{ AURA-X: Fix - 深度优化V2架构，解决消息接收问题. Approval: 寸止(ID:**********). }}
   * 初始化WebSocket管理器
   */
  async initializeWebSocket() {
    defaultLogger.info('🌐 深度优化V2架构，解决消息接收问题...');

    // {{ AURA-X: Fix - 简化配置加载，使用统一配置管理. Approval: 寸止(ID:**********). }}
    // 简化配置加载（统一使用V2架构）
    console.log('✅ 使用统一V2架构配置');

    // 加载基础配置
    let botConfig = {};
    let v2Config = {};
    try {
      botConfig = globalConfigManager.loadConfig('bot-config', {
        v2Architecture: { enabled: true }
      });

      // 加载V2架构配置
      const { getConfig } = require('./config/v2-architecture-config');
      const environment = process.env.NODE_ENV || 'development';
      v2Config = getConfig(environment);

      console.log('📊 统一架构配置加载完成');
    } catch (error) {
      console.warn(`⚠️ V2架构配置加载失败: ${error.message}，使用默认配置`);
      // 使用默认配置
      botConfig = { v2Architecture: { enabled: true } };
      v2Config = { concurrency: { maxWorkers: 6, defaultTimeout: 30000 } };
    }

    // {{ AURA-X: Add - 创建V2架构管理器. Approval: 寸止(ID:**********). }}
    // {{ AURA-X: Fix - 优化测试：使用轻量级并发管理器. Approval: 寸止(ID:**********). }}
    // 优化测试：使用轻量级并发管理器解决消息接收问题
    defaultLogger.info('🔧 优化测试：使用轻量级并发管理器...');
    defaultLogger.info('🔧 解决V2架构消息接收问题');

    // 创建V2架构管理器（如果不存在）
    if (!global.concurrencyManager || !global.resourceManager || !global.stateManager) {
      defaultLogger.info('🔧 创建所有支撑组件（轻量级优化版）...');

      try {
        // {{ AURA-X: Fix - 优化测试：创建轻量级支撑组件. Approval: 寸止(ID:**********). }}
        // 优化测试：创建轻量级支撑组件，解决消息接收问题
        defaultLogger.info('🔧 优化测试：创建轻量级支撑组件');

        // {{ AURA-X: Fix - 使用轻量级并发管理器，避免干扰消息接收. Approval: 寸止(ID:**********). }}
        // 创建轻量级并发管理器
        if (!global.concurrencyManager) {
          defaultLogger.info('🔧 加载轻量级并发管理器模块...');
          const ConcurrencyManagerLite = require('./modules/concurrency/concurrency-manager-lite');
          defaultLogger.info('🔧 创建轻量级并发管理器实例...');
          global.concurrencyManager = new ConcurrencyManagerLite({
            maxWorkers: v2Config.concurrency?.maxWorkers || 6,
            defaultTimeout: v2Config.concurrency?.defaultTimeout || 30000,
            maxTimeout: v2Config.concurrency?.maxTimeout || 300000
          });
          defaultLogger.info('✅ 轻量级并发管理器创建完成（优化版）');
        }

        // 创建资源管理器
        if (!global.resourceManager) {
          defaultLogger.info('🔧 加载资源管理器模块...');
          const ResourceManager = require('./modules/resources/resource-manager');
          defaultLogger.info('🔧 创建资源管理器实例...');
          global.resourceManager = new ResourceManager({
            maxResourceAge: 30000,    // 30秒资源生存期
            cleanupInterval: 10000,   // 10秒清理间隔
            maxResources: 300,        // 降低最大资源数量
            enableMemoryGuard: true,
            emergencyCleanupTarget: 0.5  // 紧急清理到50%
          });
          defaultLogger.info('✅ 资源管理器创建完成（第三轮测试）');
        }

        // {{ AURA-X: Fix - 第三轮测试：创建状态管理器. Approval: 寸止(ID:**********). }}
        // 创建状态管理器
        if (!global.stateManager) {
          defaultLogger.info('🔧 加载状态管理器模块...');
          const StateManager = require('./modules/state/state-manager');
          defaultLogger.info('🔧 创建状态管理器实例...');
          global.stateManager = new StateManager({
            enablePersistence: false,
            maxNamespaces: 50
          });
          defaultLogger.info('✅ 状态管理器创建完成（第三轮测试）');
        }

        // {{ AURA-X: Fix - 优化测试：验证轻量级组件创建. Approval: 寸止(ID:**********). }}
        // 验证优化测试的组件创建状态
        defaultLogger.info('✅ 优化测试：组件创建状态');
        defaultLogger.info(`✅ 轻量级并发管理器: ${global.concurrencyManager ? global.concurrencyManager.constructor.name : '创建失败'}`);
        defaultLogger.info(`✅ 资源管理器: ${global.resourceManager ? global.resourceManager.constructor.name : '创建失败'}`);
        defaultLogger.info(`✅ 状态管理器: ${global.stateManager ? global.stateManager.constructor.name : '创建失败'}`);

        if (global.concurrencyManager && global.resourceManager && global.stateManager) {
          defaultLogger.info('✅ 优化测试：所有支撑组件创建成功');
        } else {
          defaultLogger.error('❌ 优化测试：组件创建不完整');
        }
      } catch (error) {
        defaultLogger.warn(`⚠️ V2架构管理器创建失败: ${error.message}`);
        defaultLogger.warn('将使用传统模式运行WebSocket管理器');

        // 清理可能部分创建的管理器
        global.concurrencyManager = null;
        global.resourceManager = null;
        global.stateManager = null;
      }
    }

    // {{ AURA-X: Fix - 优化V2架构实例管理，减少全局变量依赖. Approval: 寸止(ID:**********). }}
    // 创建V2版本的WebSocket管理器，优化实例管理
    if (this.webSocketManager && this.webSocketManager.isConnected && this.webSocketManager.isConnected()) {
      console.log('[启动器] 使用已存在的WebSocket管理器实例');
      // 复用现有实例
    } else {
      // 启用V2架构深度调试模式
      v2Config.debug = {
        enabled: true,
        messageReceiving: true,
        asyncOperations: true,
        resourceManagement: true
      };

      console.log('[启动器] 🔧 创建V2版本WebSocket管理器（深度调试模式）');
      const WebSocketManagerV2 = require('./modules/websocket-manager-v2');
      this.webSocketManager = new WebSocketManagerV2(
        this.authManager,
        this.messageProcessor,
        this.serviceLoader,
        v2Config  // 传递包含调试配置的V2架构配置
      );
      // {{ AURA-X: Fix - 减少全局变量使用，改用实例属性管理. Approval: 寸止(ID:**********). }}
      // 只在必要时设置全局引用（用于清理冲突）
      if (!global.webSocketManagerV2) {
        global.webSocketManagerV2 = this.webSocketManager;
      }
      console.log('✅ WebSocket管理器V2已创建（深度调试模式）');
    }

    // {{ AURA-X: Fix - 完整的消息处理器V2依赖注入. Approval: 寸止(ID:**********). }}
    // 为MessageProcessor V2注入完整依赖
    if (global.concurrencyManager && global.resourceManager && global.stateManager && this.messageProcessor) {
      if (this.messageProcessor.injectDependencies) {
        this.messageProcessor.injectDependencies(
          global.concurrencyManager,
          global.resourceManager,
          global.stateManager
        );
        defaultLogger.info('✅ MessageProcessor V2完整依赖注入完成');
      } else if (this.messageProcessor.setConcurrencyManager) {
        // 回退到只注入并发管理器
        this.messageProcessor.setConcurrencyManager(global.concurrencyManager);
        defaultLogger.info('✅ 并发管理器已注入到消息处理器V2（回退模式）');
      } else {
        defaultLogger.warn('⚠️ 消息处理器V2不支持依赖注入');
      }
    } else {
      defaultLogger.warn('⚠️ 无法注入依赖到消息处理器V2');
      defaultLogger.warn(`- concurrencyManager: ${global.concurrencyManager ? '✅ 可用' : '❌ 不可用'}`);
      defaultLogger.warn(`- resourceManager: ${global.resourceManager ? '✅ 可用' : '❌ 不可用'}`);
      defaultLogger.warn(`- stateManager: ${global.stateManager ? '✅ 可用' : '❌ 不可用'}`);
      defaultLogger.warn(`- messageProcessor: ${this.messageProcessor ? '✅ 可用' : '❌ 不可用'}`);
    }

    // 优化测试：注入轻量级支撑组件
    defaultLogger.info('🔧 优化测试：注入轻量级支撑组件');
    defaultLogger.info(`检查组件状态:`);
    defaultLogger.info(`- concurrencyManager: ${global.concurrencyManager ? '✅ 可用' : '❌ 不可用'}`);
    defaultLogger.info(`- resourceManager: ${global.resourceManager ? '✅ 可用' : '❌ 不可用'}`);
    defaultLogger.info(`- stateManager: ${global.stateManager ? '✅ 可用' : '❌ 不可用'}`);

    if (global.concurrencyManager && global.resourceManager && global.stateManager) {
      if (typeof this.webSocketManager.injectDependencies === 'function') {
        defaultLogger.info('🔧 执行WebSocket管理器V2依赖注入（轻量级支撑组件）...');
        this.webSocketManager.injectDependencies(
          global.concurrencyManager,  // 注入轻量级并发管理器
          global.resourceManager,     // 注入资源管理器
          global.stateManager         // 注入状态管理器
        );
        defaultLogger.info('✅ 优化测试：轻量级支撑组件注入完成');
      } else {
        defaultLogger.warn('⚠️ WebSocket管理器V2没有injectDependencies方法');
      }
    } else {
      defaultLogger.warn('⚠️ 组件创建不完整，跳过依赖注入');
      defaultLogger.warn(`并发管理器: ${global.concurrencyManager ? '✅' : '❌'}`);
      defaultLogger.warn(`资源管理器: ${global.resourceManager ? '✅' : '❌'}`);
      defaultLogger.warn(`状态管理器: ${global.stateManager ? '✅' : '❌'}`);
    }

    // 将MessageRouter设置到WebSocketManager中（V1版本）
    if (typeof this.webSocketManager.setMessageRouter === 'function') {
      this.webSocketManager.setMessageRouter(this.messageRouter);
      defaultLogger.info('✅ 已将MessageRouter设置到WebSocketManager中');
    }

    // 将WebSocket管理器设置到ServiceLoader中
    this.serviceLoader.setWebSocketManager(this.webSocketManager);

    // 注册WebSocket管理器到内存管理器
    globalMemoryManager.registerCacheManager('webSocketManager', this.webSocketManager);

    defaultLogger.info('✅ WebSocket管理器V2初始化完成（优化测试：轻量级支撑组件）');
  }

  /**
   * 初始化健康监控
   */
  async initializeHealthMonitor() {
    defaultLogger.info('🏥 初始化健康监控...');

    this.healthMonitor = new SystemHealthMonitor({
      messageRouter: this.webSocketManager.messageRouter,
      webSocketManager: this.webSocketManager,
      logger: defaultLogger
    });

    this.healthMonitor.start();

    defaultLogger.info('✅ 健康监控初始化完成');
  }

  /**
   * {{ AURA-X: Add - 初始化增强功能模块. Approval: 寸止(ID:**********). }}
   * 初始化增强功能模块
   */
  async initializeEnhancedModules() {
    defaultLogger.info('🚀 初始化增强功能模块...');

    try {
      // 1. 验证统一配置访问器
      defaultLogger.info('🔧 验证统一配置访问器...');
      const configValidation = globalConfigAccessor.validateConfigs();
      defaultLogger.info(`配置验证结果: 有效${configValidation.valid.length}个, 无效${configValidation.invalid.length}个, 缺失${configValidation.missing.length}个`);

      if (configValidation.invalid.length > 0) {
        defaultLogger.warn('发现无效配置:', configValidation.invalid);
      }

      if (configValidation.missing.length > 0) {
        defaultLogger.warn('发现缺失配置:', configValidation.missing);
      }

      // 2. 启动增强性能监控器
      defaultLogger.info('📊 启动增强性能监控器...');
      globalPerformanceMonitor.startMonitoring();

      // 监听性能告警
      globalPerformanceMonitor.on('performance-alert', (alert) => {
        defaultLogger.warn(`性能告警: ${alert.type} - ${alert.message || '性能指标超出阈值'}`);
      });

      // {{ EMERGENCY-FIX: 3. 启动紧急监控器 }}
      defaultLogger.info('🚨 启动紧急监控器...');
      this.emergencyMonitor = new EmergencyMonitor({
        checkInterval: 60000,        // 1分钟检查一次
        memoryThreshold: 150 * 1024 * 1024, // 150MB内存阈值
        queueThreshold: 800,         // 队列长度阈值
        logInterval: 10 * 60 * 1000, // 10分钟记录一次
        enableAutoCleanup: true      // 启用自动清理
      });

      // 设置全局引用供监控器访问
      global.emergencyMonitor = this.emergencyMonitor;
      global.messageProcessor = this.messageProcessor;
      global.messageRouter = this.messageRouter;

      defaultLogger.info('✅ 增强功能模块初始化完成');

    } catch (error) {
      defaultLogger.error(`❌ 增强功能模块初始化失败: ${error.message}`);
      // 不抛出错误，允许系统继续运行
    }
  }

  /**
   * {{ AURA-X: Add - 清理WebSocket连接冲突. Approval: 寸止(ID:**********). }}
   * 清理可能的WebSocket连接冲突
   */
  async cleanupWebSocketConflicts() {
    console.log('[启动器] 🧹 开始清理WebSocket连接冲突...');

    // 清理全局WebSocket管理器引用
    if (global.wsManager && global.wsManager !== this.webSocketManager) {
      console.log('[启动器] 发现旧的全局wsManager引用，清理中...');
      try {
        if (typeof global.wsManager.cleanup === 'function') {
          await global.wsManager.cleanup();
        }
        if (typeof global.wsManager.close === 'function') {
          await global.wsManager.close();
        }
      } catch (error) {
        console.warn(`[启动器] 清理旧wsManager时出错: ${error.message}`);
      }
      global.wsManager = null;
    }

    // 清理可能存在的其他WebSocket实例
    if (global.webSocketManagerV2 && global.webSocketManagerV2 !== this.webSocketManager) {
      console.log('[启动器] 发现旧的webSocketManagerV2引用，清理中...');
      try {
        if (typeof global.webSocketManagerV2.cleanup === 'function') {
          await global.webSocketManagerV2.cleanup();
        }
      } catch (error) {
        console.warn(`[启动器] 清理旧webSocketManagerV2时出错: ${error.message}`);
      }
    }

    // 等待一段时间确保连接完全关闭
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('[启动器] ✅ WebSocket连接冲突清理完成');
  }

  /**
   * 启动所有服务
   */
  async startServices() {
    defaultLogger.info('🎯 启动所有服务...');

    // {{ AURA-X: Add - 启动前清理WebSocket冲突. Approval: 寸止(ID:**********). }}
    // 先清理可能的WebSocket连接冲突
    await this.cleanupWebSocketConflicts();

    // {{ AURA-X: Fix - 修复WebSocket连接状态检查逻辑. Approval: 寸止(ID:**********). }}
    // 检查WebSocket是否已经连接，避免重复启动
    // 注意：WebSocketManagerV2使用内部isConnected属性，不是方法
    if (this.webSocketManager.isConnected === true) {
      console.log('[启动器] WebSocket已连接，跳过重复启动');
      console.log(`[启动器] 当前连接状态: ${this.webSocketManager.isConnected}`);
    } else {
      console.log('[启动器] WebSocket未连接，开始启动连接...');
      console.log(`[启动器] 当前连接状态: ${this.webSocketManager.isConnected}`);

      // 清理可能存在的旧连接
      if (this.webSocketManager.ws) {
        console.log('[启动器] 发现旧的WebSocket连接，先清理...');
        this.webSocketManager.close();
      }

      // 启动WebSocket连接
      await this.webSocketManager.startWebSocketListener();
    }

    defaultLogger.info('✅ 所有服务启动完成');
  }

  /**
   * 打印启动摘要
   */
  printStartupSummary() {
    const memUsage = process.memoryUsage();
    const uptime = Date.now() - this.startTime;
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 Bot应用启动摘要');
    console.log('='.repeat(60));
    console.log(`📊 内存使用: ${Math.round(memUsage.rss / 1024 / 1024)}MB`);
    console.log(`⏱️  启动时间: ${uptime}ms`);
    console.log(`🔧 错误处理器: 已启用`);
    console.log(`💾 内存管理器: 已启用`);
    console.log(`📝 自动日志清理: 已启用`);
    console.log(`🏥 健康监控: 已启用`);
    console.log(`📋 配置管理: 已启用`);
    console.log('='.repeat(60));
    console.log('✨ 应用已就绪，等待消息处理...\n');
  }

  /**
   * 优雅关闭
   */
  async gracefulShutdown(signal) {
    defaultLogger.info(`📴 收到${signal}信号，开始优雅关闭...`);
    
    this.isRunning = false;
    
    try {
      // {{ EMERGENCY-FIX: 停止紧急监控器 }}
      if (this.emergencyMonitor) {
        this.emergencyMonitor.shutdown();
        defaultLogger.info('✅ 紧急监控器已停止');
      }

      // 停止健康监控
      if (this.healthMonitor) {
        this.healthMonitor.stop();
        defaultLogger.info('✅ 健康监控已停止');
      }

      // 停止WebSocket管理器
      if (this.webSocketManager) {
        this.webSocketManager.cleanup();
        defaultLogger.info('✅ WebSocket管理器已停止');
      }

      // 停止服务加载器
      if (this.serviceLoader) {
        // ServiceLoader 没有 cleanup 方法，直接清理引用
        this.serviceLoader = null;
        defaultLogger.info('✅ 服务加载器已清理');
      }

      // 停止全局管理器
      globalMemoryManager.destroy();
      globalAutoLogCleaner.destroy();
      globalConfigManager.destroy();
      
      defaultLogger.info('✅ 应用已优雅关闭');
      process.exit(0);
      
    } catch (error) {
      defaultLogger.error(`❌ 优雅关闭失败: ${error.message}`);
      process.exit(1);
    }
  }

  /**
   * 处理未捕获异常
   */
  handleUncaughtException(error) {
    defaultLogger.fatal(`未捕获异常: ${error.message}`);
    defaultLogger.fatal(`错误堆栈: ${error.stack}`);
    
    // 尝试优雅关闭
    this.gracefulShutdown('uncaughtException');
  }

  /**
   * {{ AURA-X: Modify - 增强Promise拒绝错误追踪和恢复策略. Approval: 寸止(ID:**********). }}
   * 处理未处理的Promise拒绝
   */
  handleUnhandledRejection(reason, promise) {
    defaultLogger.error(`未处理的Promise拒绝: ${reason}`);
    defaultLogger.error(`Promise: ${promise}`);

    // 添加详细的错误追踪
    if (reason && reason.stack) {
      defaultLogger.error(`详细错误堆栈: ${reason.stack}`);
    }

    // 错误分类和恢复策略
    const errorType = this.classifyPromiseRejection(reason);
    defaultLogger.warn(`错误类型: ${errorType}`);

    // 根据错误类型执行相应的恢复策略
    this.executeRecoveryStrategy(errorType, reason);

    // 处理特殊错误
    this.handleSpecialRejectionErrors(reason);
  }

  /**
   * 分类Promise拒绝错误
   */
  classifyPromiseRejection(reason) {
    if (!reason) return 'UNKNOWN';

    const reasonStr = reason.toString().toLowerCase();

    if (reasonStr.includes('timeout')) return 'TIMEOUT_ERROR';
    if (reasonStr.includes('network') || reasonStr.includes('econnreset')) return 'NETWORK_ERROR';
    if (reasonStr.includes('memory') || reasonStr.includes('heap')) return 'MEMORY_ERROR';
    if (reasonStr.includes('websocket') || reasonStr.includes('ws')) return 'WEBSOCKET_ERROR';
    if (reasonStr.includes('auth') || reasonStr.includes('unauthorized')) return 'AUTH_ERROR';

    return 'GENERAL_ERROR';
  }

  /**
   * 执行恢复策略
   */
  executeRecoveryStrategy(errorType, reason) {
    switch (errorType) {
      case 'MEMORY_ERROR':
        defaultLogger.warn('检测到内存错误，执行内存清理');
        if (globalMemoryManager) {
          globalMemoryManager.performCleanup('emergency');
        }
        break;

      case 'WEBSOCKET_ERROR':
        defaultLogger.warn('检测到WebSocket错误，标记需要重连');
        // 不直接重连，只是标记，让WebSocket管理器自己处理
        break;

      case 'NETWORK_ERROR':
        defaultLogger.warn('检测到网络错误，等待网络恢复');
        break;

      default:
        defaultLogger.info('执行通用错误恢复策略');
        break;
    }
  }

  /**
   * 处理特殊的Promise拒绝错误
   */
  handleSpecialRejectionErrors(reason) {
    // 特别处理null.get()错误
    if (reason && reason.message && reason.message.includes('Cannot read properties of null')) {
      defaultLogger.error(`检测到null.get()错误，详细信息:`);
      defaultLogger.error(`错误消息: ${reason.message}`);
      defaultLogger.error(`错误类型: ${reason.constructor.name}`);

      // 尝试从堆栈中提取更多信息
      if (reason.stack) {
        const stackLines = reason.stack.split('\n');
        const relevantLines = stackLines.filter(line =>
          line.includes('.js:') &&
          !line.includes('node_modules') &&
          !line.includes('internal/')
        );

        if (relevantLines.length > 0) {
          defaultLogger.error(`相关代码位置:`);
          relevantLines.slice(0, 3).forEach(line => {
            defaultLogger.error(`  ${line.trim()}`);
          });
        }
      }
    }

    // 记录错误但不退出进程
    globalErrorHandler.recordError('unhandledRejection', new Error(reason));
  }

  /**
   * 获取应用状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      uptime: Date.now() - this.startTime,
      memory: process.memoryUsage(),
      errorStats: globalErrorHandler.getErrorStats(),
      memoryStats: globalMemoryManager.getMemoryStats(),
      logCleanerStats: globalAutoLogCleaner.getStats(),
      configStats: globalConfigManager.getStats()
    };
  }

  /**
   * 检查并处理重启标记文件，如果存在则向管理员发送启动通知
   * {{ AURA-X: Add - 支持Supervisor环境下的重启通知. Approval: 寸止(ID:**********). }}
   */
  async checkAndProcessRestartFlag() {
    try {
      const fs = require('fs');
      const path = require('path');
      const restartFlagPath = path.join(__dirname, '.restart_flag');

      // 检查重启标记文件是否存在
      if (!fs.existsSync(restartFlagPath)) {
        console.log('未发现重启标记文件，跳过重启通知');
        return;
      }

      console.log('发现重启标记文件，准备发送重启完成通知');

      // 读取重启信息
      const restartInfoJson = fs.readFileSync(restartFlagPath, 'utf8');
      const restartInfo = JSON.parse(restartInfoJson);

      // 删除重启标记文件，防止下次启动时重复处理
      fs.unlinkSync(restartFlagPath);

      // 如果不需要通知，直接返回
      if (!restartInfo.shouldNotify) {
        console.log('根据设置，跳过重启通知');
        return;
      }

      // 获取配置中的管理员列表（尝试多个配置路径）
      let admins = [];

      try {
        // 1. 尝试从权限服务获取管理员列表
        const permissionsService = this.serviceLoader?.getService('permissions');
        if (permissionsService && typeof permissionsService.getRoleMembers === 'function') {
          const adminList = permissionsService.getRoleMembers('admin');
          if (adminList && adminList.length > 0) {
            admins = adminList;
            console.log(`从权限服务获取到 ${admins.length} 个管理员`);
          }
        }

        // 2. 如果权限服务没有管理员，尝试从bot配置获取
        if (admins.length === 0) {
          const botConfig = globalConfigManager.loadConfig('bot-config', {
            admin: { admins: [] }
          });
          if (botConfig.admin?.admins && Array.isArray(botConfig.admin.admins)) {
            admins = botConfig.admin.admins;
            console.log(`从bot配置获取到 ${admins.length} 个管理员`);
          }
        }

        // 3. 如果还是没有，尝试从基础权限配置获取
        if (admins.length === 0) {
          try {
            const basePermissions = require('./config/permissions/base-permissions');
            if (basePermissions.admin?.admins && Array.isArray(basePermissions.admin.admins)) {
              admins = basePermissions.admin.admins;
              console.log(`从基础权限配置获取到 ${admins.length} 个管理员`);
            }
          } catch (error) {
            console.log('无法加载基础权限配置:', error.message);
          }
        }

      } catch (error) {
        console.error('获取管理员列表时出错:', error.message);
      }

      if (admins.length === 0) {
        console.log('未找到任何管理员配置，无法发送重启通知');
        console.log('请检查以下配置文件中的管理员设置:');
        console.log('- config/permissions/base-permissions.js');
        console.log('- config/bot-config.js');
        return;
      }

      // 获取消息发送服务
      const messageSender = this.serviceLoader.getService('messageSender');
      if (!messageSender) {
        console.error('无法获取消息发送服务，无法发送重启通知');
        return;
      }

      // 准备通知消息
      const uptime = process.uptime();
      const memoryUsage = process.memoryUsage();
      const heapUsed = Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100;

      const botInfo = {
        wxid: this.authManager.getAuthInfo().wxid || '未知',
        uptime: `${Math.floor(uptime)}秒`,
        memory: `${heapUsed}MB`,
        timestamp: new Date().toLocaleString()
      };

      const statusMessage = `✅ 机器人已成功重启并完成初始化!\n\n` +
                            `机器人ID: ${botInfo.wxid}\n` +
                            `启动时间: ${botInfo.timestamp}\n` +
                            `运行时长: ${botInfo.uptime}\n` +
                            `内存使用: ${botInfo.memory}\n\n` +
                            `重启信息:\n` +
                            `- 触发者: ${restartInfo.triggeredBy}\n` +
                            `- 时间戳: ${new Date(restartInfo.timestamp).toLocaleString()}`;

      // 向所有管理员发送通知
      let successCount = 0;
      for (const adminId of admins) {
        try {
          await messageSender.sendText(adminId, statusMessage);
          console.log(`已向管理员 ${adminId} 发送重启通知`);
          successCount++;
        } catch (error) {
          console.error(`向管理员 ${adminId} 发送重启通知失败: ${error.message}`);
        }
      }

      console.log(`重启通知已发送给 ${successCount}/${admins.length} 位管理员`);
    } catch (error) {
      console.error(`处理重启通知出错: ${error.message}`);
    }
  }
}

// 启动应用
if (require.main === module) {
  const app = new OptimizedApp();
  app.start().catch(error => {
    console.error('启动失败:', error);
    process.exit(1);
  });
}

module.exports = OptimizedApp;
