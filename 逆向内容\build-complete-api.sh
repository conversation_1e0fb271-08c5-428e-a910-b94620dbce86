#!/bin/bash

# 编译完整API版本的第三个微信机器人

set -e

echo "🚀 编译完整API版本的第三个微信机器人..."

# 设置环境变量
export CGO_ENABLED=0
export GOOS=linux
export GOARCH=amd64

# 清理旧文件
echo "🧹 清理旧文件..."
rm -f myapp-linux-bot3-complete

# 编译完整API版本
echo "🔨 编译完整API版本..."
go build -ldflags="-s -w" -o myapp-linux-bot3-complete main_complete_api.go

# 检查编译结果
if [ -f "myapp-linux-bot3-complete" ]; then
    echo "✅ 编译成功！"
    ls -lh myapp-linux-bot3-complete
    
    echo ""
    echo "🤖 第三个微信机器人 (完整API版本) 信息:"
    echo "   📡 端口: 8057"
    echo "   🔑 机器人ID: wechat_bot_3"
    echo "   🗄️  Redis数据库: Db 3"
    echo "   🗃️  MySQL数据库: wechat_bot3"
    echo "   📨 RocketMQ主题: wx_sync_msg_topic_bot3"
    echo "   🔑 管理密钥: 972f1de87cf245afbc914d3356261e56"
    echo ""
    echo "🔧 支持的API接口:"
    echo "   ✅ /login/WakeUpLogin - 唤醒登录"
    echo "   ✅ /login/GetLoginStatus - 获取登录状态"
    echo "   ✅ /login/GenAuthKey - 生成授权码"
    echo "   ✅ /admin/GenAuthKey - 管理员生成授权码"
    echo "   ✅ /admin/GenWxAuthKey - 生成微信授权码"
    echo "   ✅ /health - 健康检查"
    echo "   ✅ /api/status - API状态"
    echo "   ✅ /ws - WebSocket端点"
    echo ""
    echo "🚀 运行命令:"
    echo "   ./myapp-linux-bot3-complete"
    echo ""
    echo "🔍 测试命令:"
    echo "   curl http://localhost:8057/health"
    echo "   curl -X POST http://localhost:8057/login/WakeUpLogin -d '{\"Check\":false,\"Proxy\":\"\"}'"
    echo "   curl 'http://localhost:8057/login/GenAuthKey?key=972f1de87cf245afbc914d3356261e56&count=1&days=30'"
    echo ""
    
else
    echo "❌ 编译失败！"
    exit 1
fi

echo "🎉 完整API版本编译完成！"
