#!/bin/bash

# Go程序内存监控器启动脚本
# 用于启动和管理Go程序的内存监控

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🤖 Go程序内存监控器${NC}"
echo -e "${BLUE}用于监控和管理myapp_linux的内存使用${NC}"
echo -e "${BLUE}自动重启功能可防止RocketMQ消息队列无限增长${NC}"
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js未安装，请先安装Node.js${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js已安装: $(node --version)${NC}"

# 检查必要文件是否存在
required_files=(
    "go-memory-monitor.js"
    "start-with-memory-monitor.js"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo -e "${RED}❌ 缺少必要文件: $file${NC}"
        exit 1
    fi
done

echo -e "${GREEN}✅ 所有必要文件存在${NC}"

# 检查Go程序文件
go_programs=(
    "./逆向内容/myapp-linux"
    "./逆向内容/myapp-linux-fixed"
    "./逆向内容/myapp-linux-bot3"
    "./逆向内容/myapp-linux-bot3-complete"
    "./逆向内容/myapp-linux-bot3-real"
)

found_program=""
for program in "${go_programs[@]}"; do
    if [ -f "$program" ]; then
        found_program="$program"
        echo -e "${GREEN}✅ 检测到Go程序: $program${NC}"
        break
    fi
done

if [ -z "$found_program" ]; then
    echo -e "${YELLOW}⚠️ 未找到Go程序文件，将使用默认路径${NC}"
    echo -e "${YELLOW}   请确保Go程序文件存在于 ./逆向内容/ 目录中${NC}"
fi

# 创建日志目录
mkdir -p logs
echo -e "${GREEN}✅ 日志目录已创建: logs/${NC}"

# 检查端口占用（如果Go程序使用8057端口）
if netstat -tlnp 2>/dev/null | grep -q ":8057 "; then
    echo -e "${YELLOW}⚠️ 端口8057已被占用${NC}"
    echo "占用情况:"
    netstat -tlnp 2>/dev/null | grep ":8057 "
    echo ""
    echo -e "${YELLOW}监控器将尝试管理现有的Go程序进程${NC}"
else
    echo -e "${GREEN}✅ 端口8057可用${NC}"
fi

echo ""
echo -e "${BLUE}📋 启动选项:${NC}"
echo "1. 标准模式启动 (推荐)"
echo "2. 测试模式启动 (先运行测试)"
echo "3. 调试模式启动 (显示详细日志)"
echo "4. PM2守护进程模式"
echo "5. 查看配置信息"
echo "6. 运行测试套件"
echo ""

read -p "请选择启动模式 (1-6，默认1): " choice

# 如果用户直接按回车，设置默认值为1
if [ -z "$choice" ]; then
    choice=1
fi

case $choice in
    1)
        echo -e "${GREEN}🚀 启动标准模式...${NC}"
        echo -e "${YELLOW}按 Ctrl+C 停止监控${NC}"
        echo ""
        node start-with-memory-monitor.js
        ;;
    2)
        echo -e "${BLUE}🧪 启动测试模式...${NC}"
        echo "先运行测试套件，然后启动监控器"
        echo ""
        
        # 运行测试
        echo -e "${YELLOW}运行测试套件...${NC}"
        node test-memory-monitor.js
        
        if [ $? -eq 0 ]; then
            echo ""
            echo -e "${GREEN}✅ 测试通过，启动监控器...${NC}"
            echo -e "${YELLOW}按 Ctrl+C 停止监控${NC}"
            echo ""
            node start-with-memory-monitor.js
        else
            echo -e "${RED}❌ 测试失败，请检查问题后重试${NC}"
            exit 1
        fi
        ;;
    3)
        echo -e "${BLUE}🔍 启动调试模式...${NC}"
        echo -e "${YELLOW}将显示详细的调试信息${NC}"
        echo -e "${YELLOW}按 Ctrl+C 停止监控${NC}"
        echo ""
        DEBUG=* node start-with-memory-monitor.js
        ;;
    4)
        echo -e "${BLUE}🔧 启动PM2守护进程模式...${NC}"
        
        # 检查PM2是否安装
        if ! command -v pm2 &> /dev/null; then
            echo -e "${YELLOW}⚠️ PM2未安装，正在安装...${NC}"
            npm install -g pm2
            
            if [ $? -ne 0 ]; then
                echo -e "${RED}❌ PM2安装失败，请手动安装: npm install -g pm2${NC}"
                exit 1
            fi
        fi
        
        echo -e "${GREEN}✅ PM2已安装: $(pm2 --version)${NC}"
        
        # 停止现有的监控器进程
        pm2 delete go-memory-monitor 2>/dev/null || true
        
        # 启动新的监控器进程
        pm2 start start-with-memory-monitor.js --name "go-memory-monitor"
        
        echo ""
        echo -e "${GREEN}✅ 监控器已在后台启动${NC}"
        echo -e "${BLUE}管理命令:${NC}"
        echo "  pm2 status                    # 查看状态"
        echo "  pm2 logs go-memory-monitor    # 查看日志"
        echo "  pm2 restart go-memory-monitor # 重启监控器"
        echo "  pm2 stop go-memory-monitor    # 停止监控器"
        echo "  pm2 delete go-memory-monitor  # 删除监控器"
        ;;
    5)
        echo -e "${BLUE}📋 配置信息:${NC}"
        echo ""
        echo "监控器配置:"
        echo "  - 监控间隔: 30秒"
        echo "  - 内存警告阈值: 500MB"
        echo "  - 内存重启阈值: 1000MB"
        echo "  - 最小重启间隔: 5分钟"
        echo "  - 最大重启次数: 10次/小时"
        echo "  - 优雅关闭超时: 10秒"
        echo ""
        echo "日志配置:"
        echo "  - 日志文件: logs/go-memory-monitor.log"
        echo "  - 测试报告: logs/test-report.json"
        echo ""
        echo "Go程序检测路径:"
        for program in "${go_programs[@]}"; do
            if [ -f "$program" ]; then
                echo -e "  - ${GREEN}✅ $program${NC}"
            else
                echo -e "  - ${RED}❌ $program${NC}"
            fi
        done
        echo ""
        echo "重新运行脚本以启动监控器"
        ;;
    6)
        echo -e "${BLUE}🧪 运行测试套件...${NC}"
        echo ""
        node test-memory-monitor.js
        
        if [ $? -eq 0 ]; then
            echo ""
            echo -e "${GREEN}🎉 所有测试通过！${NC}"
            echo -e "${BLUE}测试报告已保存到: logs/test-report.json${NC}"
        else
            echo -e "${RED}❌ 测试失败，请检查问题${NC}"
            exit 1
        fi
        ;;
    *)
        echo -e "${RED}❌ 无效选择，使用默认标准模式...${NC}"
        echo -e "${YELLOW}按 Ctrl+C 停止监控${NC}"
        echo ""
        node start-with-memory-monitor.js
        ;;
esac

echo ""
echo -e "${BLUE}程序已退出${NC}"
