#!/bin/bash

# 构建脚本 - 修复RocketMQ问题后的myapp-linux重构版本

set -e

echo "🚀 开始构建修复后的myapp-linux..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go 1.19或更高版本"
    exit 1
fi

# 检查Go版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo "✅ 检测到Go版本: $GO_VERSION"

# 设置环境变量
export CGO_ENABLED=0
export GOOS=linux
export GOARCH=amd64

# 清理之前的构建
echo "🧹 清理之前的构建文件..."
rm -f myapp-linux-fixed
rm -f myapp-linux-fixed.tar.gz

# 下载依赖
echo "📦 下载Go模块依赖..."
go mod tidy
go mod download

# 构建应用
echo "🔨 构建应用程序..."
go build -ldflags="-s -w -X main.version=$(date +%Y%m%d%H%M%S)" -o myapp-linux-fixed .

# 检查构建结果
if [ -f "myapp-linux-fixed" ]; then
    echo "✅ 构建成功！"
    
    # 显示文件信息
    ls -lh myapp-linux-fixed
    
    # 创建部署包
    echo "📦 创建部署包..."
    tar -czf myapp-linux-fixed.tar.gz myapp-linux-fixed config.json
    
    echo "🎉 部署包创建完成: myapp-linux-fixed.tar.gz"
    
    # 显示修复说明
    echo ""
    echo "🔧 RocketMQ修复说明:"
    echo "   ✅ 修复了goroutine泄漏问题"
    echo "   ✅ 修复了重连时崩溃问题"
    echo "   ✅ 修复了内存无限增长问题"
    echo "   ✅ 增加了健康检查机制"
    echo "   ✅ 增加了优雅关闭机制"
    echo "   ✅ 增加了并发控制机制"
    echo "   ✅ 增加了panic恢复机制"
    echo ""
    echo "📋 部署说明:"
    echo "   1. 将 myapp-linux-fixed.tar.gz 上传到服务器"
    echo "   2. 解压: tar -xzf myapp-linux-fixed.tar.gz"
    echo "   3. 修改 config.json 配置文件"
    echo "   4. 运行: ./myapp-linux-fixed"
    echo ""
    
else
    echo "❌ 构建失败！"
    exit 1
fi
