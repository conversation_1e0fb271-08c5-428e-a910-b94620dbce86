{"timestamp": "2025-08-04T01:55:35.904Z", "summary": {"total": 8, "passed": 5, "failed": 3}, "results": [{"name": "监控器创建", "success": true, "message": "成功创建监控器实例", "timestamp": "2025-08-04T01:55:35.883Z"}, {"name": "状态获取", "success": true, "message": "状态对象: {\n  \"isMonitoring\": false,\n  \"goProcessRunning\": null,\n  \"goProcessPid\": null,\n  \"restartCount\": 0,\n  \"lastRestartTime\": 0,\n  \"recentRestarts\": 0,\n  \"config\": {\n    \"goProgram\": \"./test-dummy-program.sh\",\n    \"monitorInterval\": 5000,\n    \"memoryWarningThreshold\": 100,\n    \"memoryRestartThreshold\": 200,\n    \"minRestartInterval\": 300000,\n    \"maxRestartCount\": 10,\n    \"gracefulShutdownTimeout\": 10000,\n    \"logFile\": \"logs/test-monitor.log\",\n    \"enableConsoleLog\": false\n  }\n}", "timestamp": "2025-08-04T01:55:35.883Z"}, {"name": "默认配置", "success": true, "message": "默认配置正确", "timestamp": "2025-08-04T01:55:35.884Z"}, {"name": "自定义配置", "success": true, "message": "自定义配置正确应用", "timestamp": "2025-08-04T01:55:35.885Z"}, {"name": "内存监控测试", "success": false, "message": "spawn UNKNOWN", "timestamp": "2025-08-04T01:55:35.898Z"}, {"name": "重启功能测试", "success": false, "message": "spawn UNKNOWN", "timestamp": "2025-08-04T01:55:35.899Z"}, {"name": "错误处理", "success": true, "message": "正确处理不存在的程序文件", "timestamp": "2025-08-04T01:55:35.901Z"}, {"name": "错误处理测试", "success": false, "message": "spawn UNKNOWN", "timestamp": "2025-08-04T01:55:35.903Z"}]}