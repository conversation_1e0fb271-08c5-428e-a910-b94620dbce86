{"timestamp": "2025-08-04T02:08:10.307Z", "testType": "complete-system-test", "duration": 36, "summary": {"total": 27, "passed": 26, "failed": 1}, "results": [{"name": "文件存在: start.js", "success": true, "message": "文件存在", "timestamp": "2025-08-04T02:08:10.275Z"}, {"name": "文件存在: go-memory-monitor.js", "success": true, "message": "文件存在", "timestamp": "2025-08-04T02:08:10.275Z"}, {"name": "文件存在: start-unified.js", "success": true, "message": "文件存在", "timestamp": "2025-08-04T02:08:10.276Z"}, {"name": "文件存在: start-optimized.js", "success": true, "message": "文件存在", "timestamp": "2025-08-04T02:08:10.276Z"}, {"name": "文件存在: config/starter-config.json", "success": true, "message": "文件存在", "timestamp": "2025-08-04T02:08:10.276Z"}, {"name": "冗余文件已删除: start-with-memory-monitor.js", "success": true, "message": "已删除", "timestamp": "2025-08-04T02:08:10.276Z"}, {"name": "冗余文件已删除: start-unified-with-go-monitor.js", "success": true, "message": "已删除", "timestamp": "2025-08-04T02:08:10.276Z"}, {"name": "冗余文件已删除: run-memory-monitor.sh", "success": true, "message": "已删除", "timestamp": "2025-08-04T02:08:10.276Z"}, {"name": "冗余文件已删除: run-memory-monitor.bat", "success": true, "message": "已删除", "timestamp": "2025-08-04T02:08:10.276Z"}, {"name": "配置文件解析", "success": true, "message": "配置文件格式正确", "timestamp": "2025-08-04T02:08:10.278Z"}, {"name": "配置项: enableGoMonitor", "success": true, "message": "值: true", "timestamp": "2025-08-04T02:08:10.278Z"}, {"name": "配置项: autoDetectGoProgram", "success": true, "message": "值: true", "timestamp": "2025-08-04T02:08:10.278Z"}, {"name": "配置项: goMonitor.memoryWarningThreshold", "success": true, "message": "值: 800", "timestamp": "2025-08-04T02:08:10.278Z"}, {"name": "配置项: goMonitor.memoryRestartThreshold", "success": true, "message": "值: 1200", "timestamp": "2025-08-04T02:08:10.278Z"}, {"name": "配置项: goMonitor.monitorInterval", "success": true, "message": "值: 60000", "timestamp": "2025-08-04T02:08:10.279Z"}, {"name": "内存警告阈值优化", "success": true, "message": "800MB", "timestamp": "2025-08-04T02:08:10.279Z"}, {"name": "内存重启阈值优化", "success": true, "message": "1200MB", "timestamp": "2025-08-04T02:08:10.279Z"}, {"name": "监控间隔优化", "success": true, "message": "60秒", "timestamp": "2025-08-04T02:08:10.279Z"}, {"name": "Go程序检测: ./逆向内容/myapp-linux", "success": true, "message": "程序存在", "timestamp": "2025-08-04T02:08:10.279Z"}, {"name": "Go程序可执行权限", "success": false, "message": "无执行权限", "timestamp": "2025-08-04T02:08:10.280Z"}, {"name": "启动器类导入", "success": true, "message": "MasterStarter类可正常导入", "timestamp": "2025-08-04T02:08:10.303Z"}, {"name": "配置加载", "success": true, "message": "配置对象加载成功", "timestamp": "2025-08-04T02:08:10.304Z"}, {"name": "Go程序检测方法", "success": true, "message": "检测到: ./逆向内容/myapp-linux", "timestamp": "2025-08-04T02:08:10.304Z"}, {"name": "内存监控类导入", "success": true, "message": "GoMemoryMonitor类可正常导入", "timestamp": "2025-08-04T02:08:10.304Z"}, {"name": "监控器创建", "success": true, "message": "监控器实例创建成功", "timestamp": "2025-08-04T02:08:10.305Z"}, {"name": "状态获取", "success": true, "message": "状态对象结构正确", "timestamp": "2025-08-04T02:08:10.305Z"}, {"name": "配置应用", "success": true, "message": "自定义配置正确应用", "timestamp": "2025-08-04T02:08:10.305Z"}]}