/**
 * Go程序内存监控和自动重启管理器
 * 用于解决myapp_linux的RocketMQ消息队列无限增长问题
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

class GoMemoryMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    
    // 配置参数
    this.config = {
      // Go程序路径
      goProgram: options.goProgram || './逆向内容/myapp-linux',
      // 监控间隔（毫秒）
      monitorInterval: options.monitorInterval || 30000, // 30秒
      // 内存阈值（MB）
      memoryWarningThreshold: options.memoryWarningThreshold || 500, // 500MB警告
      memoryRestartThreshold: options.memoryRestartThreshold || 1000, // 1GB重启
      // 重启保护
      minRestartInterval: options.minRestartInterval || 300000, // 5分钟最小重启间隔
      maxRestartCount: options.maxRestartCount || 10, // 1小时内最大重启次数
      // 优雅关闭超时
      gracefulShutdownTimeout: options.gracefulShutdownTimeout || 10000, // 10秒
      // 日志配置
      logFile: options.logFile || 'logs/go-memory-monitor.log',
      enableConsoleLog: options.enableConsoleLog !== false,
      ...options
    };

    // 运行状态
    this.goProcess = null;
    this.isMonitoring = false;
    this.monitorTimer = null;
    this.lastRestartTime = 0;
    this.restartCount = 0;
    this.restartHistory = [];

    // 确保日志目录存在
    this.ensureLogDirectory();
    
    // 绑定事件处理
    this.setupEventHandlers();
  }

  /**
   * 确保日志目录存在
   */
  ensureLogDirectory() {
    const logDir = path.dirname(this.config.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    // 进程退出处理
    process.on('SIGINT', () => this.shutdown('SIGINT'));
    process.on('SIGTERM', () => this.shutdown('SIGTERM'));
    process.on('uncaughtException', (error) => {
      this.log(`未捕获异常: ${error.message}`, 'ERROR');
      this.shutdown('EXCEPTION');
    });
  }

  /**
   * 启动Go程序和监控
   */
  async start() {
    try {
      this.log('启动Go程序内存监控器...', 'INFO');
      
      // 启动Go程序
      await this.startGoProgram();
      
      // 启动内存监控
      this.startMemoryMonitoring();
      
      this.log('Go程序内存监控器已启动', 'INFO');
      this.emit('started');
      
    } catch (error) {
      this.log(`启动失败: ${error.message}`, 'ERROR');
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 启动Go程序
   */
  async startGoProgram() {
    return new Promise((resolve, reject) => {
      this.log(`启动Go程序: ${this.config.goProgram}`, 'INFO');
      
      // 检查程序文件是否存在
      if (!fs.existsSync(this.config.goProgram)) {
        const error = new Error(`Go程序文件不存在: ${this.config.goProgram}`);
        return reject(error);
      }

      // 启动Go程序
      this.goProcess = spawn(this.config.goProgram, [], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: path.dirname(this.config.goProgram)
      });

      // 处理Go程序输出
      this.goProcess.stdout.on('data', (data) => {
        this.log(`[Go程序] ${data.toString().trim()}`, 'DEBUG');
      });

      this.goProcess.stderr.on('data', (data) => {
        this.log(`[Go程序错误] ${data.toString().trim()}`, 'WARN');
      });

      // 处理Go程序退出
      this.goProcess.on('exit', (code, signal) => {
        this.log(`Go程序退出: code=${code}, signal=${signal}`, 'INFO');
        this.goProcess = null;
        this.emit('goProcessExit', { code, signal });
      });

      this.goProcess.on('error', (error) => {
        this.log(`Go程序启动失败: ${error.message}`, 'ERROR');
        this.goProcess = null;
        reject(error);
      });

      // 等待程序启动
      setTimeout(() => {
        if (this.goProcess && !this.goProcess.killed) {
          this.log('Go程序启动成功', 'INFO');
          resolve();
        } else {
          reject(new Error('Go程序启动超时或失败'));
        }
      }, 3000);
    });
  }

  /**
   * 启动内存监控
   */
  startMemoryMonitoring() {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.log('开始内存监控...', 'INFO');

    // 立即执行一次检查
    this.checkMemoryUsage();

    // 设置定时监控
    this.monitorTimer = setInterval(() => {
      this.checkMemoryUsage();
    }, this.config.monitorInterval);
  }

  /**
   * 检查内存使用情况
   */
  async checkMemoryUsage() {
    if (!this.goProcess || this.goProcess.killed) {
      this.log('Go程序未运行，跳过内存检查', 'WARN');
      return;
    }

    try {
      const memoryUsage = await this.getGoProcessMemoryUsage();
      
      if (memoryUsage === null) {
        this.log('无法获取内存使用情况', 'WARN');
        return;
      }

      const memoryMB = Math.round(memoryUsage / 1024 / 1024);
      this.log(`Go程序内存使用: ${memoryMB}MB`, 'DEBUG');

      // 发出内存使用事件
      this.emit('memoryCheck', { memoryMB, memoryBytes: memoryUsage });

      // 检查是否需要重启
      if (memoryMB >= this.config.memoryRestartThreshold) {
        this.log(`内存使用超过重启阈值 (${memoryMB}MB >= ${this.config.memoryRestartThreshold}MB)`, 'WARN');
        await this.restartGoProgram('内存超限');
      } else if (memoryMB >= this.config.memoryWarningThreshold) {
        this.log(`内存使用警告 (${memoryMB}MB >= ${this.config.memoryWarningThreshold}MB)`, 'WARN');
        this.emit('memoryWarning', { memoryMB });
      }

    } catch (error) {
      this.log(`内存检查失败: ${error.message}`, 'ERROR');
    }
  }

  /**
   * 获取Go程序的内存使用情况
   */
  async getGoProcessMemoryUsage() {
    return new Promise((resolve) => {
      if (!this.goProcess || !this.goProcess.pid) {
        resolve(null);
        return;
      }

      // 使用ps命令获取内存使用情况（RSS）
      exec(`ps -p ${this.goProcess.pid} -o rss=`, (error, stdout, stderr) => {
        if (error) {
          this.log(`获取内存使用失败: ${error.message}`, 'DEBUG');
          resolve(null);
          return;
        }

        const rssKB = parseInt(stdout.trim());
        if (isNaN(rssKB)) {
          resolve(null);
          return;
        }

        // RSS是以KB为单位，转换为字节
        resolve(rssKB * 1024);
      });
    });
  }

  /**
   * 重启Go程序
   */
  async restartGoProgram(reason = '手动重启') {
    // 检查重启保护
    const now = Date.now();
    if (now - this.lastRestartTime < this.config.minRestartInterval) {
      this.log(`重启间隔过短，跳过重启 (${Math.round((now - this.lastRestartTime) / 1000)}秒)`, 'WARN');
      return false;
    }

    // 检查重启次数限制
    this.cleanupRestartHistory();
    if (this.restartHistory.length >= this.config.maxRestartCount) {
      this.log(`重启次数过多，停止自动重启 (${this.restartHistory.length}次)`, 'ERROR');
      this.emit('tooManyRestarts', { count: this.restartHistory.length });
      return false;
    }

    try {
      this.log(`开始重启Go程序，原因: ${reason}`, 'INFO');
      this.emit('restartStarted', { reason });

      // 停止Go程序
      await this.stopGoProgram();

      // 等待一下确保完全停止
      await this.sleep(1000);

      // 启动新的Go程序
      await this.startGoProgram();

      // 记录重启
      this.lastRestartTime = now;
      this.restartCount++;
      this.restartHistory.push({ time: now, reason });

      this.log(`Go程序重启成功 (第${this.restartCount}次)`, 'INFO');
      this.emit('restartCompleted', { count: this.restartCount, reason });

      return true;

    } catch (error) {
      this.log(`重启失败: ${error.message}`, 'ERROR');
      this.emit('restartFailed', { error, reason });
      return false;
    }
  }

  /**
   * 停止Go程序
   */
  async stopGoProgram() {
    if (!this.goProcess || this.goProcess.killed) {
      return;
    }

    return new Promise((resolve) => {
      this.log('正在停止Go程序...', 'INFO');

      // 设置超时强制终止
      const forceKillTimeout = setTimeout(() => {
        if (this.goProcess && !this.goProcess.killed) {
          this.log('优雅关闭超时，强制终止Go程序', 'WARN');
          this.goProcess.kill('SIGKILL');
        }
      }, this.config.gracefulShutdownTimeout);

      // 监听进程退出
      const onExit = () => {
        clearTimeout(forceKillTimeout);
        this.log('Go程序已停止', 'INFO');
        resolve();
      };

      if (this.goProcess.killed) {
        onExit();
        return;
      }

      this.goProcess.once('exit', onExit);

      // 发送优雅关闭信号
      try {
        this.goProcess.kill('SIGTERM');
      } catch (error) {
        this.log(`发送关闭信号失败: ${error.message}`, 'WARN');
        clearTimeout(forceKillTimeout);
        resolve();
      }
    });
  }

  /**
   * 清理重启历史记录
   */
  cleanupRestartHistory() {
    const oneHourAgo = Date.now() - 3600000; // 1小时前
    this.restartHistory = this.restartHistory.filter(restart => restart.time > oneHourAgo);
  }

  /**
   * 停止监控
   */
  stopMemoryMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
      this.monitorTimer = null;
    }

    this.log('内存监控已停止', 'INFO');
  }

  /**
   * 关闭监控器
   */
  async shutdown(signal = 'SHUTDOWN') {
    this.log(`收到关闭信号: ${signal}`, 'INFO');
    
    // 停止监控
    this.stopMemoryMonitoring();
    
    // 停止Go程序
    if (this.goProcess) {
      await this.stopGoProgram();
    }
    
    this.log('Go程序内存监控器已关闭', 'INFO');
    this.emit('shutdown', { signal });
  }

  /**
   * 获取监控状态
   */
  getStatus() {
    return {
      isMonitoring: this.isMonitoring,
      goProcessRunning: this.goProcess && !this.goProcess.killed,
      goProcessPid: this.goProcess ? this.goProcess.pid : null,
      restartCount: this.restartCount,
      lastRestartTime: this.lastRestartTime,
      recentRestarts: this.restartHistory.length,
      config: this.config
    };
  }

  /**
   * 手动触发重启
   */
  async manualRestart() {
    return await this.restartGoProgram('手动触发');
  }

  /**
   * 日志记录
   */
  log(message, level = 'INFO') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] ${message}`;
    
    // 控制台输出
    if (this.config.enableConsoleLog) {
      console.log(logMessage);
    }
    
    // 文件输出
    try {
      fs.appendFileSync(this.config.logFile, logMessage + '\n');
    } catch (error) {
      console.error(`写入日志文件失败: ${error.message}`);
    }
  }

  /**
   * 工具方法：睡眠
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = GoMemoryMonitor;
