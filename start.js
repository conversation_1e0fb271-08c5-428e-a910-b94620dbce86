/**
 * 统一启动脚本 - 集成Go内存监控
 * 自动处理登录、启动优化版Bot和Go程序内存监控的完整流程
 */

const fs = require('fs');
const path = require('path');

// 导入启动器
const UnifiedStarter = require('./start-unified');
const GoMemoryMonitor = require('./go-memory-monitor');

class MasterStarter {
  constructor() {
    this.config = this.loadConfig();
  }

  /**
   * 加载配置
   */
  loadConfig() {
    const defaultConfig = {
      // 启动模式
      enableGoMonitor: true,        // 是否启用Go程序监控
      autoDetectGoProgram: true,    // 是否自动检测Go程序

      // Go监控配置
      goMonitor: {
        memoryWarningThreshold: 500,   // 500MB警告
        memoryRestartThreshold: 1000,  // 1GB重启
        monitorInterval: 30000,        // 30秒检查间隔
        minRestartInterval: 300000,    // 5分钟最小重启间隔
        maxRestartCount: 10,           // 1小时内最大重启次数
        gracefulShutdownTimeout: 10000 // 10秒优雅关闭超时
      },

      // 日志配置
      logging: {
        enableConsoleLog: true,
        logFile: 'logs/master-starter.log'
      }
    };

    // 尝试加载自定义配置
    const configFile = 'config/starter-config.json';
    if (fs.existsSync(configFile)) {
      try {
        const customConfig = JSON.parse(fs.readFileSync(configFile, 'utf8'));
        return { ...defaultConfig, ...customConfig };
      } catch (error) {
        console.log(`⚠️ 配置文件加载失败，使用默认配置: ${error.message}`);
      }
    }

    return defaultConfig;
  }

  /**
   * 检测Go程序是否存在
   */
  detectGoProgram() {
    if (!this.config.autoDetectGoProgram) {
      return false;
    }

    const possiblePaths = [
      './逆向内容/myapp-linux',
      './逆向内容/myapp-linux-fixed',
      './逆向内容/myapp-linux-bot3',
      './逆向内容/myapp-linux-bot3-complete',
      './逆向内容/myapp-linux-bot3-real'
    ];

    for (const programPath of possiblePaths) {
      if (fs.existsSync(programPath)) {
        console.log(`✅ 检测到Go程序: ${programPath}`);
        return programPath;
      }
    }

    return false;
  }

  /**
   * 显示启动信息
   */
  showStartupInfo() {
    console.log('🤖 微信机器人统一启动器');
    console.log('='.repeat(50));
    console.log('');

    const goProgram = this.detectGoProgram();
    if (goProgram && this.config.enableGoMonitor) {
      console.log('🧠 智能启动模式 (Go内存监控已启用)');
      console.log(`📊 监控配置: 警告${this.config.goMonitor.memoryWarningThreshold}MB, 重启${this.config.goMonitor.memoryRestartThreshold}MB`);
      console.log(`✅ 检测到Go程序: ${goProgram}`);
    } else {
      console.log('📱 标准启动模式 (仅Node.js Bot)');
      if (!goProgram) {
        console.log('⚠️ 未检测到Go程序');
      } else {
        console.log('⚠️ Go监控已禁用');
      }
    }
    console.log('');
  }

  /**
   * 直接启动（智能模式）
   */
  async directStart() {
    const goProgram = this.detectGoProgram();

    if (goProgram && this.config.enableGoMonitor) {
      // 启用Go程序内存监控
      await this.startWithGoMonitor(goProgram);
    } else {
      // 使用标准模式
      const starter = new UnifiedStarter();
      await starter.start();
    }
  }

  /**
   * 启动带Go监控的统一启动器
   */
  async startWithGoMonitor(goProgram) {
    console.log('📊 启动Go程序内存监控...');

    // 创建Go监控器
    const goMonitor = new GoMemoryMonitor({
      goProgram: goProgram,
      ...this.config.goMonitor,
      logFile: 'logs/go-memory-monitor.log',
      enableConsoleLog: true
    });

    // 设置Go监控器事件
    this.setupGoMonitorEvents(goMonitor);

    try {
      // 启动Go监控
      await goMonitor.start();
      console.log('✅ Go程序内存监控已启动\n');

      // 启动Node.js Bot
      const starter = new UnifiedStarter();

      // 处理关闭信号
      const shutdown = async () => {
        console.log('\n🛑 正在关闭所有服务...');
        try {
          if (starter.stop) starter.stop();
          await goMonitor.shutdown();
          console.log('✅ 所有服务已关闭');
          process.exit(0);
        } catch (error) {
          console.error(`❌ 关闭时出错: ${error.message}`);
          process.exit(1);
        }
      };

      process.on('SIGINT', shutdown);
      process.on('SIGTERM', shutdown);

      await starter.start();

    } catch (error) {
      console.error(`❌ Go监控启动失败: ${error.message}`);
      console.log('⚠️ 继续启动标准模式...\n');

      const starter = new UnifiedStarter();
      await starter.start();
    }
  }

  /**
   * 设置Go监控器事件
   */
  setupGoMonitorEvents(goMonitor) {
    goMonitor.on('memoryWarning', (data) => {
      console.log(`⚠️ [Go监控] 内存警告: ${data.memoryMB}MB`);
    });

    goMonitor.on('restartStarted', (data) => {
      console.log(`🔄 [Go监控] 开始重启Go程序，原因: ${data.reason}`);
    });

    goMonitor.on('restartCompleted', (data) => {
      console.log(`✅ [Go监控] Go程序重启成功 (第${data.count}次)`);
    });

    goMonitor.on('restartFailed', (data) => {
      console.log(`❌ [Go监控] Go程序重启失败: ${data.error.message}`);
    });

    goMonitor.on('tooManyRestarts', (data) => {
      console.log(`🚨 [Go监控] 重启次数过多 (${data.count}次)，请检查Go程序`);
    });

    goMonitor.on('error', (error) => {
      console.error(`❌ [Go监控] 监控器错误: ${error.message}`);
    });
  }



  /**
   * 主启动方法
   */
  async start() {
    try {
      this.showStartupInfo();
      await this.directStart();
    } catch (error) {
      console.error(`❌ 启动失败: ${error.message}`);
      process.exit(1);
    }
  }
}

// 主程序入口
if (require.main === module) {
  const masterStarter = new MasterStarter();
  masterStarter.start().catch(console.error);
}

// 导出类供测试使用
module.exports = MasterStarter;
