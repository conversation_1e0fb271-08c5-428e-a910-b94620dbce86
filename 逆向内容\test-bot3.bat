@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ==========================================
echo    第三个微信机器人配置测试
echo ==========================================
echo.

echo 🔍 检查第三个机器人配置...

REM 检查配置文件
if not exist "assets\setting.json" (
    echo ❌ 配置文件 assets\setting.json 不存在
    pause
    exit /b 1
)

echo ✅ 配置文件存在

REM 检查端口配置
findstr "8057" "assets\setting.json" >nul
if %errorlevel% neq 0 (
    echo ❌ 端口配置错误，应该是8057
) else (
    echo ✅ 端口配置正确: 8057
)

REM 检查Redis数据库配置
findstr "\"Db\": 3" "assets\setting.json" >nul
if %errorlevel% neq 0 (
    echo ❌ Redis数据库配置错误，应该是Db: 3
) else (
    echo ✅ Redis数据库配置正确: Db 3
)

REM 检查MySQL数据库配置
findstr "wechat_bot3" "assets\setting.json" >nul
if %errorlevel% neq 0 (
    echo ❌ MySQL数据库配置错误，应该是wechat_bot3
) else (
    echo ✅ MySQL数据库配置正确: wechat_bot3
)

REM 检查RocketMQ配置
findstr "\"rocketMq\": true" "assets\setting.json" >nul
if %errorlevel% neq 0 (
    echo ❌ RocketMQ未启用
) else (
    echo ✅ RocketMQ已启用
)

REM 检查Topic配置
findstr "wx_sync_msg_topic_bot3" "assets\setting.json" >nul
if %errorlevel% neq 0 (
    echo ❌ Topic配置错误，应该是wx_sync_msg_topic_bot3
) else (
    echo ✅ Topic配置正确: wx_sync_msg_topic_bot3
)

REM 检查机器人标识
findstr "wechat_bot_3" "assets\setting.json" >nul
if %errorlevel% neq 0 (
    echo ❌ 机器人标识配置错误
) else (
    echo ✅ 机器人标识配置正确: wechat_bot_3
)

echo.
echo ==========================================
echo 📋 第三个机器人配置总结
echo ==========================================
echo.

echo 🔧 配置信息:
echo    端口: 8057
echo    Redis数据库: Db 3
echo    MySQL数据库: wechat_bot3
echo    RocketMQ Topic: wx_sync_msg_topic_bot3
echo    机器人标识: wechat_bot_3
echo    管理密钥: 3rd_bot_admin_key_2025_secure
echo.

echo 📋 部署前检查清单:
echo    ✅ 确保端口8057未被占用
echo    ✅ 确保MySQL中存在wechat_bot3数据库
echo    ✅ 确保Redis服务正常运行
echo    ✅ 确保RocketMQ服务正常运行
echo    ✅ 确保创建了wx_sync_msg_topic_bot3主题
echo.

echo 🚀 编译命令:
echo    go mod tidy
echo    set CGO_ENABLED=0
echo    set GOOS=linux
echo    set GOARCH=amd64
echo    go build -o myapp-linux-bot3 .
echo.

echo 🏃 运行命令:
echo    ./myapp-linux-bot3
echo.

echo 🔍 验证命令:
echo    curl http://localhost:8057/health
echo    wscat -c ws://localhost:8057/ws
echo.

echo ==========================================
echo 🎉 第三个机器人配置检查完成！
echo ==========================================

pause
