/**
 * 带内存监控的Go程序启动器
 * 集成内存监控和自动重启功能
 */

const GoMemoryMonitor = require('./go-memory-monitor');
const path = require('path');
const fs = require('fs');

class GoMemoryManagerApp {
  constructor() {
    this.monitor = null;
    this.isRunning = false;
    
    // 配置参数
    this.config = {
      // Go程序路径（自动检测）
      goProgram: this.detectGoProgram(),
      // 监控间隔：30秒
      monitorInterval: 30000,
      // 内存阈值
      memoryWarningThreshold: 500,  // 500MB警告
      memoryRestartThreshold: 1000, // 1GB重启
      // 重启保护
      minRestartInterval: 300000,   // 5分钟最小重启间隔
      maxRestartCount: 10,          // 1小时内最大重启次数
      // 优雅关闭超时
      gracefulShutdownTimeout: 10000,
      // 日志配置
      logFile: 'logs/go-memory-monitor.log',
      enableConsoleLog: true
    };
  }

  /**
   * 自动检测Go程序路径
   */
  detectGoProgram() {
    const possiblePaths = [
      './逆向内容/myapp-linux',
      './逆向内容/myapp-linux-fixed',
      './逆向内容/myapp-linux-bot3',
      './逆向内容/myapp-linux-bot3-complete',
      './逆向内容/myapp-linux-bot3-real'
    ];

    for (const programPath of possiblePaths) {
      if (fs.existsSync(programPath)) {
        console.log(`✅ 检测到Go程序: ${programPath}`);
        return programPath;
      }
    }

    console.log('⚠️ 未找到Go程序，使用默认路径: ./逆向内容/myapp-linux');
    return './逆向内容/myapp-linux';
  }

  /**
   * 启动应用
   */
  async start() {
    try {
      console.log('🚀 启动Go程序内存管理器...');
      console.log('='.repeat(60));
      
      // 显示配置信息
      this.displayConfig();
      
      // 创建监控器
      this.monitor = new GoMemoryMonitor(this.config);
      
      // 设置事件监听
      this.setupEventListeners();
      
      // 启动监控
      await this.monitor.start();
      
      this.isRunning = true;
      console.log('✅ Go程序内存管理器启动成功！');
      console.log('📝 按 Ctrl+C 停止监控');
      console.log('='.repeat(60));
      
    } catch (error) {
      console.error(`❌ 启动失败: ${error.message}`);
      process.exit(1);
    }
  }

  /**
   * 显示配置信息
   */
  displayConfig() {
    console.log('📋 配置信息:');
    console.log(`   Go程序路径: ${this.config.goProgram}`);
    console.log(`   监控间隔: ${this.config.monitorInterval / 1000}秒`);
    console.log(`   内存警告阈值: ${this.config.memoryWarningThreshold}MB`);
    console.log(`   内存重启阈值: ${this.config.memoryRestartThreshold}MB`);
    console.log(`   最小重启间隔: ${this.config.minRestartInterval / 60000}分钟`);
    console.log(`   日志文件: ${this.config.logFile}`);
    console.log('');
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监控器事件
    this.monitor.on('started', () => {
      console.log('🎯 内存监控已启动');
    });

    this.monitor.on('memoryCheck', (data) => {
      // 只在内存使用较高时显示
      if (data.memoryMB > 100) {
        console.log(`📊 内存检查: ${data.memoryMB}MB`);
      }
    });

    this.monitor.on('memoryWarning', (data) => {
      console.log(`⚠️ 内存警告: ${data.memoryMB}MB (阈值: ${this.config.memoryWarningThreshold}MB)`);
    });

    this.monitor.on('restartStarted', (data) => {
      console.log(`🔄 开始重启Go程序，原因: ${data.reason}`);
    });

    this.monitor.on('restartCompleted', (data) => {
      console.log(`✅ Go程序重启成功 (第${data.count}次)`);
    });

    this.monitor.on('restartFailed', (data) => {
      console.log(`❌ Go程序重启失败: ${data.error.message}`);
    });

    this.monitor.on('tooManyRestarts', (data) => {
      console.log(`🚨 重启次数过多 (${data.count}次)，停止自动重启`);
      console.log('请检查Go程序是否存在严重问题');
    });

    this.monitor.on('goProcessExit', (data) => {
      console.log(`📴 Go程序退出: code=${data.code}, signal=${data.signal}`);
      
      // 如果不是正常关闭，尝试重启
      if (this.isRunning && data.code !== 0 && data.signal !== 'SIGTERM') {
        console.log('🔄 检测到异常退出，尝试重启...');
        setTimeout(() => {
          if (this.isRunning) {
            this.monitor.restartGoProgram('异常退出');
          }
        }, 5000);
      }
    });

    this.monitor.on('error', (error) => {
      console.error(`❌ 监控器错误: ${error.message}`);
    });

    this.monitor.on('shutdown', () => {
      console.log('📴 监控器已关闭');
      this.isRunning = false;
    });

    // 进程信号处理
    process.on('SIGINT', () => {
      console.log('\n🛑 收到停止信号，正在关闭...');
      this.shutdown();
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 收到终止信号，正在关闭...');
      this.shutdown();
    });

    // 未捕获异常处理
    process.on('uncaughtException', (error) => {
      console.error(`💥 未捕获异常: ${error.message}`);
      console.error(error.stack);
      this.shutdown();
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('💥 未处理的Promise拒绝:', reason);
      this.shutdown();
    });
  }

  /**
   * 关闭应用
   */
  async shutdown() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    console.log('🔄 正在关闭Go程序内存管理器...');

    try {
      if (this.monitor) {
        await this.monitor.shutdown();
      }
      console.log('✅ 关闭完成');
      process.exit(0);
    } catch (error) {
      console.error(`❌ 关闭时出错: ${error.message}`);
      process.exit(1);
    }
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    if (!this.monitor) {
      return { status: 'not_started' };
    }

    return {
      status: 'running',
      ...this.monitor.getStatus()
    };
  }

  /**
   * 手动重启Go程序
   */
  async manualRestart() {
    if (!this.monitor) {
      throw new Error('监控器未启动');
    }

    console.log('🔄 手动触发重启...');
    const success = await this.monitor.manualRestart();
    
    if (success) {
      console.log('✅ 手动重启成功');
    } else {
      console.log('❌ 手动重启失败');
    }

    return success;
  }
}

// 如果直接运行此文件，启动应用
if (require.main === module) {
  const app = new GoMemoryManagerApp();
  
  // 显示启动信息
  console.log('🤖 Go程序内存管理器');
  console.log('用于监控和管理myapp_linux的内存使用');
  console.log('自动重启功能可防止RocketMQ消息队列无限增长');
  console.log('');

  // 启动应用
  app.start().catch(error => {
    console.error(`启动失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = GoMemoryManagerApp;
