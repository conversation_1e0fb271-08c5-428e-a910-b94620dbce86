{"whitelist": {"users": {"ai": ["wxid_lmqilgrqxhjd12"], "aiBlacklist": ["wxid_n637xqge4pp312", "wxid_wfzb66tjbuqf22", "wxid_lmqilgrqxhjd12", "wxid_bdwwi912xw7812"], "imageDownload": []}, "groups": {"aiEnabled": ["52962727129@chatroom"], "aiDisabled": ["21788734691@chatroom", "44080827516@chatroom", "51593636835@chatroom", "48326155652@chatroom", "19003715224@chatroom", "57586876664@chatroom"], "scanOnlyGroups": ["19003715224@chatroom", "51593636835@chatroom"], "imageDownload": ["19003715224@chatroom", "43732735175@chatroom"], "whitelist": ["52962727129@chatroom", "21788734691@chatroom", "44080827516@chatroom", "51593636835@chatroom", "48326155652@chatroom", "19003715224@chatroom", "57586876664@chatroom"]}}, "chatRecord": {"enabled": true, "targetWxids": ["wxid_l5t38z675v7v22"], "storageDir": "chat_records"}, "github": {"enabled": true, "interval": 300000, "repositories": [{"owner": "qitoqito", "name": "psyduck", "html_url": "https://github.com/qitoqito/psyduck", "enabled": true, "bindWxid": ""}], "lastCheckTime": 1754274600013, "userAgent": "GitHub-Monitor-Bot", "maxEventsPerRepo": 10, "token": "*********************************************************************************************"}, "dianping": {"enabled": false, "interval": 600000, "merchants": [{"id": "**********", "name": "商家**********", "url": "http://www.dianping.com/shop/**********", "cookie": "", "enabled": true, "bindWxid": ""}], "lastCheckTime": *************, "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36", "admins": ["wxid_lmqilgrqxhjd12", "wxid_admin123456", "filehelper"]}, "douyin": {"enabled": true, "interval": 300000, "accounts": [{"name": "账号信息已移至douyin-config.json", "note": "账号信息已存储在专用配置文件"}], "maxCacheSize": 100, "lastCheckTime": *************, "phoneDecrypt": {"decryptApiUrl": "https://leads.cluerich.com/bff/sales/clue/decrypt-phone-number", "detailApiUrl": "https://leads.cluerich.com/bff/sales/clue/detail", "userId": "****************"}, "requestRetry": {"maxRetries": 3, "retryDelay": 2000}, "globalGroupIds": [], "pushSettings": {"enableGroupPush": true, "enablePersonalPush": true, "groupPushDelay": 1000, "personalPushDelay": 500}, "timeBuffer": 10, "cacheSettings": {"enableTimeBasedExpiry": true, "maxCacheAgeHours": 168, "autoCleanupInterval": ********}}, "redPacket": {"enabled": true, "mode": "hybrid", "permissions": {"enabled": true, "adminOnly": false, "adminUsers": ["wxid_lmqilgrqxhjd12"], "allowedUsers": [], "blockedUsers": [], "allowedGroups": [], "blockedGroups": []}, "timeRestrictions": {"enabled": true, "silentHours": {"start": "01:00", "end": "08:00"}, "allowedHours": null}, "antiDetection": {"randomDelay": {"min": 1000, "max": 3000}, "maxGrabsPerMinute": 10, "cooldownPeriod": 60000, "enableRandomDelay": true}, "monitoring": {"enableStats": true, "logLevel": "info", "notifyAdmins": true, "statsRetentionDays": 30}, "scheduler": {"enabled": false, "scanInterval": 30000, "maxRetries": 3, "retryDelay": 5000}, "advanced": {"enableDetailedLogging": false, "maxProcessedPacketsCache": 10000, "autoCleanupInterval": 3600000, "enablePerformanceMonitoring": false}}}