#!/bin/bash

# 运行完整API版本的第三个微信机器人

echo "🤖 启动完整API版本的第三个微信机器人..."

# 检查程序文件是否存在
if [ ! -f "myapp-linux-bot3-complete" ]; then
    echo "❌ 程序文件 myapp-linux-bot3-complete 不存在"
    echo "请先运行编译脚本: ./build-complete-api.sh"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "assets/setting.json" ]; then
    echo "❌ 配置文件 assets/setting.json 不存在"
    exit 1
fi

# 设置执行权限
chmod +x myapp-linux-bot3-complete

echo "✅ 程序文件检查通过"

# 检查端口是否被占用
if netstat -tlnp 2>/dev/null | grep -q ":8057 "; then
    echo "❌ 端口8057已被占用，请先停止占用该端口的程序"
    echo "占用情况:"
    netstat -tlnp 2>/dev/null | grep ":8057 "
    exit 1
else
    echo "✅ 端口8057可用"
fi

# 创建日志目录
mkdir -p logs

echo ""
echo "🚀 启动完整API版本的第三个微信机器人..."
echo "   端口: 8057"
echo "   配置文件: assets/setting.json"
echo "   日志目录: logs/"
echo ""
echo "🔧 支持的API接口:"
echo "   📡 /health - 健康检查"
echo "   🔐 /login/WakeUpLogin - 唤醒登录"
echo "   📊 /login/GetLoginStatus - 获取登录状态"
echo "   🔑 /login/GenAuthKey - 生成授权码"
echo "   🔑 /admin/GenAuthKey - 管理员生成授权码"
echo "   🔑 /admin/GenWxAuthKey - 生成微信授权码"
echo "   📋 /api/status - API状态"
echo "   🌐 /ws - WebSocket端点"
echo ""

# 启动程序并记录日志
echo "$(date): Starting WeChat Bot 3 (Complete API)..." >> logs/bot3-complete.log

# 前台运行（可以看到实时日志）
echo "🔄 程序启动中，按 Ctrl+C 停止..."
echo "🔍 在另一个终端中可以运行测试: ./test-complete-api.sh"
echo ""

./myapp-linux-bot3-complete 2>&1 | tee -a logs/bot3-complete.log

# 如果程序退出，记录日志
echo "$(date): WeChat Bot 3 (Complete API) stopped" >> logs/bot3-complete.log
