@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🐳 使用Docker编译MyApp-Linux修复版本...

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或未启动，请先安装Docker Desktop
    echo 📥 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker环境检查通过

REM 设置镜像名称和标签
set IMAGE_NAME=myapp-linux-fixed
set IMAGE_TAG=latest
set CONTAINER_NAME=myapp-build-temp

echo 🔨 开始构建Docker镜像...
docker build -t %IMAGE_NAME%:%IMAGE_TAG% .

if %errorlevel% neq 0 (
    echo ❌ Docker镜像构建失败！
    pause
    exit /b 1
)

echo ✅ Docker镜像构建成功！

echo 📦 提取编译好的二进制文件...

REM 创建临时容器
docker create --name %CONTAINER_NAME% %IMAGE_NAME%:%IMAGE_TAG%

REM 从容器中复制二进制文件
docker cp %CONTAINER_NAME%:/app/myapp-linux-fixed ./myapp-linux-fixed
docker cp %CONTAINER_NAME%:/app/config.json ./config-docker.json

REM 删除临时容器
docker rm %CONTAINER_NAME%

if exist myapp-linux-fixed (
    echo ✅ 二进制文件提取成功！
    
    REM 显示文件信息
    dir myapp-linux-fixed
    
    echo.
    echo 🎉 编译完成！生成的文件：
    echo    📄 myapp-linux-fixed     - Linux可执行文件
    echo    📄 config-docker.json    - 配置文件
    echo.
    echo 📋 部署说明：
    echo    1. 将 myapp-linux-fixed 上传到Linux服务器
    echo    2. 设置执行权限: chmod +x myapp-linux-fixed
    echo    3. 修改配置文件并重命名为 config.json
    echo    4. 运行: ./myapp-linux-fixed
    echo.
    echo 🐳 或者直接使用Docker运行：
    echo    docker run -d -p 8080:8080 --name myapp %IMAGE_NAME%:%IMAGE_TAG%
    echo.
    
) else (
    echo ❌ 二进制文件提取失败！
    pause
    exit /b 1
)

echo 🧹 清理Docker镜像？
choice /c YN /m "是否删除构建的Docker镜像以节省空间"
if %errorlevel%==1 (
    docker rmi %IMAGE_NAME%:%IMAGE_TAG%
    echo ✅ Docker镜像已删除
)

pause
