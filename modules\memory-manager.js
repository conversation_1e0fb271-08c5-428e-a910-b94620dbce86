/**
 * 统一内存管理模块
 * 提供统一的内存清理、监控和优化功能
 */

const EventEmitter = require('events');

/**
 * 统一内存管理器
 */
class MemoryManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      // 内存清理配置
      cleanupInterval: options.cleanupInterval || 300000, // 5分钟
      maxCacheSize: options.maxCacheSize || 1000,
      cacheExpiryTime: options.cacheExpiryTime || 300000, // 5分钟
      
      // {{ EMERGENCY-FIX: 调整内存阈值为更合理的值 }}
      // 内存监控配置（优化版）
      memoryCheckInterval: options.memoryCheckInterval || 60000, // 1分钟检查一次
      memoryWarningThreshold: options.memoryWarningThreshold || 120 * 1024 * 1024, // 120MB警告
      memoryCriticalThreshold: options.memoryCriticalThreshold || 180 * 1024 * 1024, // 180MB临界
      memoryEmergencyThreshold: options.memoryEmergencyThreshold || 250 * 1024 * 1024, // 250MB紧急

      // GC配置（平衡的清理策略）
      enableAutoGC: options.enableAutoGC !== false,
      gcThreshold: options.gcThreshold || 150 * 1024 * 1024, // 150MB触发GC
      
      logger: options.logger || console
    };

    // 注册的缓存管理器
    this.cacheManagers = new Map();

    // {{ AURA-X: Modify - 使用ResourceManager管理定时器. Approval: 寸止(ID:1751501600). }}
    // 资源管理器引用（由外部注入）
    this.resourceManager = null;
    this.resourceIds = {
      cleanupTimer: null,
      monitorTimer: null
    };

    // 内存统计
    this.memoryStats = {
      lastCleanup: Date.now(),
      cleanupCount: 0,
      gcCount: 0,
      peakMemory: 0,
      averageMemory: 0,
      memoryHistory: []
    };

    // 启动定时器
    this.startTimers();
  }

  /**
   * {{ AURA-X: Add - 设置资源管理器. Approval: 寸止(ID:1751501600). }}
   * 设置资源管理器
   */
  setResourceManager(resourceManager) {
    this.resourceManager = resourceManager;
    this.options.logger.log('[内存管理器] 资源管理器已设置');
  }

  /**
   * 注册缓存管理器
   * @param {string} name 管理器名称
   * @param {Object} manager 缓存管理器对象
   */
  registerCacheManager(name, manager) {
    if (!manager || typeof manager !== 'object') {
      throw new Error('缓存管理器必须是一个对象');
    }

    this.cacheManagers.set(name, {
      manager,
      lastCleanup: Date.now(),
      cleanupCount: 0
    });

    this.options.logger.log(`[内存管理器] 注册缓存管理器: ${name}`);
  }

  /**
   * 取消注册缓存管理器
   * @param {string} name 管理器名称
   */
  unregisterCacheManager(name) {
    if (this.cacheManagers.delete(name)) {
      this.options.logger.log(`[内存管理器] 取消注册缓存管理器: ${name}`);
    }
  }

  /**
   * {{ AURA-X: Modify - 使用ResourceManager管理定时器. Approval: 寸止(ID:1751501600). }}
   * 启动定时器
   */
  startTimers() {
    // 内存清理定时器
    if (this.resourceManager) {
      const cleanupTimer = setInterval(() => {
        this.performCleanup();
      }, this.options.cleanupInterval);

      this.resourceIds.cleanupTimer = this.resourceManager.registerInterval(cleanupTimer, {
        priority: 'normal',
        metadata: { component: 'memory_manager', type: 'cleanup' }
      });
    } else {
      this.cleanupTimer = setInterval(() => {
        this.performCleanup();
      }, this.options.cleanupInterval);
    }

    // 内存监控定时器
    if (this.resourceManager) {
      const monitorTimer = setInterval(() => {
        this.monitorMemory();
      }, this.options.memoryCheckInterval);

      this.resourceIds.monitorTimer = this.resourceManager.registerInterval(monitorTimer, {
        priority: 'high',
        metadata: { component: 'memory_manager', type: 'monitor' }
      });
    } else {
      this.monitorTimer = setInterval(() => {
        this.monitorMemory();
      }, this.options.memoryCheckInterval);
    }

    this.options.logger.log('[内存管理器] 定时器已启动');
  }

  /**
   * {{ AURA-X: Modify - 使用ResourceManager清理定时器. Approval: 寸止(ID:1751501600). }}
   * 停止定时器
   */
  stopTimers() {
    if (this.resourceManager) {
      if (this.resourceIds.cleanupTimer) {
        this.resourceManager.releaseResource(this.resourceIds.cleanupTimer);
        this.resourceIds.cleanupTimer = null;
      }

      if (this.resourceIds.monitorTimer) {
        this.resourceManager.releaseResource(this.resourceIds.monitorTimer);
        this.resourceIds.monitorTimer = null;
      }
    } else {
      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
        this.cleanupTimer = null;
      }

      if (this.monitorTimer) {
        clearInterval(this.monitorTimer);
        this.monitorTimer = null;
      }
    }

    this.options.logger.log('[内存管理器] 定时器已停止');
  }

  /**
   * 执行内存清理
   */
  async performCleanup() {
    try {
      const startTime = Date.now();
      let totalCleaned = 0;

      this.options.logger.log('[内存管理器] 开始执行内存清理...');

      // 清理所有注册的缓存管理器
      for (const [name, info] of this.cacheManagers.entries()) {
        try {
          const cleaned = await this.cleanupCacheManager(name, info);
          totalCleaned += cleaned;
          info.lastCleanup = Date.now();
          info.cleanupCount++;
        } catch (error) {
          this.options.logger.error(`[内存管理器] 清理缓存管理器 ${name} 失败: ${error.message}`);
        }
      }

      // 检查是否需要强制GC
      if (this.options.enableAutoGC) {
        await this.checkAndPerformGC();
      }

      const duration = Date.now() - startTime;
      this.memoryStats.lastCleanup = Date.now();
      this.memoryStats.cleanupCount++;

      this.options.logger.log(`[内存管理器] 内存清理完成，清理项目: ${totalCleaned}, 耗时: ${duration}ms`);
      
      // 发出清理完成事件
      this.emit('cleanup', { totalCleaned, duration });

    } catch (error) {
      this.options.logger.error(`[内存管理器] 内存清理失败: ${error.message}`);
      this.emit('error', error);
    }
  }

  /**
   * 清理单个缓存管理器
   * @param {string} name 管理器名称
   * @param {Object} info 管理器信息
   * @returns {number} 清理的项目数量
   */
  async cleanupCacheManager(name, info) {
    const manager = info.manager;
    let cleaned = 0;

    // 标准的清理方法
    if (typeof manager.cleanup === 'function') {
      const result = await manager.cleanup();
      cleaned = typeof result === 'number' ? result : 0;
    }
    // Map类型的缓存
    else if (manager instanceof Map) {
      cleaned = this.cleanupMap(manager);
    }
    // Set类型的缓存
    else if (manager instanceof Set) {
      cleaned = this.cleanupSet(manager);
    }
    // 数组类型的缓存
    else if (Array.isArray(manager)) {
      cleaned = this.cleanupArray(manager);
    }
    // 对象类型的缓存
    else if (typeof manager === 'object') {
      cleaned = this.cleanupObject(manager);
    }

    if (cleaned > 0) {
      this.options.logger.log(`[内存管理器] 清理缓存管理器 ${name}: ${cleaned} 项`);
    }

    return cleaned;
  }

  /**
   * 清理Map类型缓存
   * @param {Map} map Map对象
   * @returns {number} 清理的项目数量
   */
  cleanupMap(map) {
    const originalSize = map.size;
    const now = Date.now();
    let cleaned = 0;

    // 如果Map过大，清理最旧的项目
    if (map.size > this.options.maxCacheSize) {
      const entries = Array.from(map.entries());
      const toRemove = map.size - this.options.maxCacheSize;
      
      for (let i = 0; i < toRemove; i++) {
        map.delete(entries[i][0]);
        cleaned++;
      }
    }

    // 清理过期项目（如果值包含时间戳）
    for (const [key, value] of map.entries()) {
      if (typeof value === 'object' && value !== null && value.timestamp) {
        if (now - value.timestamp > this.options.cacheExpiryTime) {
          map.delete(key);
          cleaned++;
        }
      }
    }

    return cleaned;
  }

  /**
   * 清理Set类型缓存
   * @param {Set} set Set对象
   * @returns {number} 清理的项目数量
   */
  cleanupSet(set) {
    const originalSize = set.size;
    
    // 如果Set过大，清理一半
    if (set.size > this.options.maxCacheSize) {
      const values = Array.from(set.values());
      const toRemove = Math.floor(set.size / 2);
      
      for (let i = 0; i < toRemove; i++) {
        set.delete(values[i]);
      }
      
      return toRemove;
    }

    return 0;
  }

  /**
   * 清理数组类型缓存
   * @param {Array} array 数组对象
   * @returns {number} 清理的项目数量
   */
  cleanupArray(array) {
    const originalLength = array.length;
    
    // 如果数组过大，保留最新的项目
    if (array.length > this.options.maxCacheSize) {
      const toKeep = this.options.maxCacheSize;
      array.splice(0, array.length - toKeep);
      return originalLength - array.length;
    }

    return 0;
  }

  /**
   * 清理对象类型缓存
   * @param {Object} obj 对象
   * @returns {number} 清理的项目数量
   */
  cleanupObject(obj) {
    let cleaned = 0;
    const now = Date.now();

    // 清理过期属性
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const value = obj[key];
        if (typeof value === 'object' && value !== null && value.timestamp) {
          if (now - value.timestamp > this.options.cacheExpiryTime) {
            delete obj[key];
            cleaned++;
          }
        }
      }
    }

    return cleaned;
  }

  /**
   * 监控内存使用情况
   */
  monitorMemory() {
    const memUsage = process.memoryUsage();
    const rss = memUsage.rss;

    // 更新统计信息
    this.memoryStats.peakMemory = Math.max(this.memoryStats.peakMemory, rss);
    this.memoryStats.memoryHistory.push({
      timestamp: Date.now(),
      rss: rss,
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal
    });

    // 保持历史记录在合理范围内
    if (this.memoryStats.memoryHistory.length > 100) {
      this.memoryStats.memoryHistory.shift();
    }

    // 计算平均内存使用
    const recentHistory = this.memoryStats.memoryHistory.slice(-10);
    this.memoryStats.averageMemory = recentHistory.reduce((sum, item) => sum + item.rss, 0) / recentHistory.length;

    // 检查内存阈值
    if (rss >= this.options.memoryCriticalThreshold) {
      this.options.logger.error(`[内存管理器] 内存使用严重: ${Math.round(rss / 1024 / 1024)}MB`);
      this.emit('memoryAlert', { level: 'critical', usage: rss });
      
      // 立即执行清理
      this.performCleanup();
    } else if (rss >= this.options.memoryWarningThreshold) {
      this.options.logger.warn(`[内存管理器] 内存使用警告: ${Math.round(rss / 1024 / 1024)}MB`);
      this.emit('memoryAlert', { level: 'warning', usage: rss });
    }
  }

  /**
   * 检查并执行垃圾回收
   */
  async checkAndPerformGC() {
    const memUsage = process.memoryUsage();
    
    if (memUsage.rss >= this.options.gcThreshold) {
      try {
        if (global.gc) {
          global.gc();
          this.memoryStats.gcCount++;
          this.options.logger.log(`[内存管理器] 执行垃圾回收，内存使用: ${Math.round(memUsage.rss / 1024 / 1024)}MB`);
        } else {
          this.options.logger.warn('[内存管理器] 垃圾回收不可用，请使用 --expose-gc 启动参数');
        }
      } catch (error) {
        this.options.logger.error(`[内存管理器] 垃圾回收失败: ${error.message}`);
      }
    }
  }

  /**
   * 获取内存统计信息
   * @returns {Object} 内存统计信息
   */
  getMemoryStats() {
    const memUsage = process.memoryUsage();
    
    return {
      current: {
        rss: memUsage.rss,
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external
      },
      stats: { ...this.memoryStats },
      cacheManagers: Object.fromEntries(
        Array.from(this.cacheManagers.entries()).map(([name, info]) => [
          name,
          {
            lastCleanup: info.lastCleanup,
            cleanupCount: info.cleanupCount
          }
        ])
      )
    };
  }

  /**
   * 强制执行内存清理
   */
  async forceCleanup() {
    await this.performCleanup();
  }

  /**
   * 销毁内存管理器
   */
  destroy() {
    this.stopTimers();
    this.cacheManagers.clear();
    this.removeAllListeners();
    this.options.logger.log('[内存管理器] 已销毁');
  }
}

// 创建全局内存管理器实例
const globalMemoryManager = new MemoryManager();

module.exports = {
  MemoryManager,
  globalMemoryManager
};
