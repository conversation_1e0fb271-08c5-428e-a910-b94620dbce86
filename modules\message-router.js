/**
 * 消息路由器模块
 * 负责接收所有消息并根据类型分发到对应的处理器
 */

const { Logger } = require('./logger');

// 创建日志实例
const logger = new Logger({
  enableConsole: true,
  enableFile: false
});

/**
 * 消息路由器类
 * 负责将消息分发到对应的处理器
 */
class MessageRouter {
  constructor(authManager, serviceLoader, config = {}) {
    this.authManager = authManager;
    this.serviceLoader = serviceLoader;
    this.config = config;

    // 处理器映射
    this.handlers = {};

    // 消息处理统计
    this.stats = {
      total: 0,
      routed: 0,
      skipped: 0,
      timeout: 0,
      errors: 0,
      byType: {}
    };

    // 消息时间过滤配置
    this.timeoutMinutes = config.messageProcessing?.timeoutMinutes || 5;
    this.enableTimeFilter = config.messageProcessing?.enableTimeFilter !== false;

    // 移除特殊命令路由机制，让所有管理员命令都走统一的处理流程
    // 这样可以确保所有命令都能被管理员命令服务正确处理
    this.commandRoutes = {};

    // 初始化内存管理
    this.processedMessages = new Map();
    // {{ EMERGENCY-FIX: 添加更严格的缓存管理 }}
    this.maxCacheSize = config.maxCacheSize || 500; // 降低最大缓存大小
    this.cacheExpiryTime = config.cacheExpiryTime || 180000; // 3分钟过期
    this.lastCleanupTime = Date.now();
    this.cleanupInterval = 60000; // 1分钟清理一次

    // 启动内存清理定时器
    this.startMemoryCleanup();

    console.log('消息路由器初始化完成');
  }

  /**
   * 启动内存清理定时器
   */
  startMemoryCleanup() {
    // 每分钟清理一次过期消息
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredMessages();
    }, 60000);
  }

  /**
   * 清理过期消息
   */
  cleanupExpiredMessages() {
    if (!this.processedMessages || this.processedMessages.size === 0) {
      return;
    }

    const now = Date.now();

    // {{ EMERGENCY-FIX: 如果距离上次清理时间不足1分钟，跳过 }}
    if (now - this.lastCleanupTime < this.cleanupInterval) {
      return;
    }

    const initialSize = this.processedMessages.size;
    let cleanedCount = 0;

    // 清理过期消息
    for (const [key, timestamp] of this.processedMessages.entries()) {
      if (now - timestamp > this.cacheExpiryTime) {
        this.processedMessages.delete(key);
        cleanedCount++;
      }
    }

    // {{ EMERGENCY-FIX: 如果缓存仍然过大，强制清理最旧的一半 }}
    if (this.processedMessages.size > this.maxCacheSize) {
      console.warn(`[消息路由器] 缓存过大(${this.processedMessages.size})，执行强制清理`);

      const entries = Array.from(this.processedMessages.entries());
      entries.sort((a, b) => a[1] - b[1]); // 按时间戳排序

      const keepCount = Math.floor(this.maxCacheSize / 2);
      const removeCount = entries.length - keepCount;

      // 清空并重新填充最新的消息
      this.processedMessages.clear();
      entries.slice(-keepCount).forEach(([key, value]) => {
        this.processedMessages.set(key, value);
      });

      cleanedCount += removeCount;
      console.warn(`[消息路由器] 强制清理完成，移除 ${removeCount} 条记录，保留 ${keepCount} 条`);
    }

    // 更新统计信息
    this.stats.cacheSize = this.processedMessages.size;
    this.lastCleanupTime = now;

    if (cleanedCount > 0 || initialSize !== this.processedMessages.size) {
      console.log(`[消息路由器] 清理完成 - 清理: ${cleanedCount}, 当前大小: ${this.processedMessages.size}/${this.maxCacheSize}`);
    }
  }

  /**
   * 清空缓存
   */
  clearCache() {
    if (this.processedMessages) {
      const size = this.processedMessages.size;
      this.processedMessages.clear();
      console.log(`[消息路由器] 已清空消息缓存，清理了 ${size} 条记录`);
    }
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      total: 0,
      routed: 0,
      skipped: 0,
      timeout: 0,
      errors: 0,
      byType: {},
      cacheSize: this.processedMessages ? this.processedMessages.size : 0,
      memoryUsage: 0
    };

    // 重置处理器统计
    Object.values(this.handlers).forEach(handlerList => {
      handlerList.forEach(handlerInfo => {
        if (handlerInfo.stats) {
          handlerInfo.stats = {
            calls: 0,
            success: 0,
            errors: 0,
            totalTime: 0,
            avgTime: 0
          };
        }
      });
    });

    console.log('[消息路由器] 统计信息已重置');
  }

  /**
   * 注册消息处理器
   * @param {number} msgType 消息类型
   * @param {Object} handler 处理器对象
   * @param {Function} handler.handleMessages 处理消息的方法
   * @param {String} handlerName 处理器名称（可选）
   * @param {Object} options 处理器选项
   * @param {number} options.priority 优先级（数字越大优先级越高，默认50）
   * @param {boolean} options.exclusive 是否独占处理（默认false）
   * @param {Array} options.conditions 处理条件
   * @returns {boolean} 注册是否成功
   */
  registerHandler(msgType, handler, handlerName = '', options = {}) {
    if (!handler || typeof handler.handleMessages !== 'function') {
      console.error(`[消息路由器] 注册处理器失败：处理器不符合接口规范`);
      return false;
    }

    // 确保msgType是数字
    const msgTypeNumber = parseInt(msgType);
    if (isNaN(msgTypeNumber)) {
      console.error(`[消息路由器] 注册处理器失败：消息类型必须是数字，收到的是 ${msgType} (${typeof msgType})`);
      return false;
    }

    // 初始化当前类型的处理器列表
    if (!this.handlers[msgTypeNumber]) {
      this.handlers[msgTypeNumber] = [];
    }

    // 添加处理器
    const handlerInfo = {
      handler,
      name: handlerName || `handler_${msgTypeNumber}_${this.handlers[msgTypeNumber].length}`,
      priority: options.priority || 50, // 默认优先级50
      exclusive: options.exclusive || false, // 默认非独占
      conditions: options.conditions || [], // 处理条件
      stats: {
        calls: 0,
        success: 0,
        errors: 0
      }
    };

    this.handlers[msgTypeNumber].push(handlerInfo);

    // 按优先级排序（高优先级在前）
    this.handlers[msgTypeNumber].sort((a, b) => b.priority - a.priority);

    console.log(`[消息路由器] 已注册处理器 "${handlerInfo.name}" 用于处理类型 ${msgTypeNumber}(${this.getMessageTypeName(msgTypeNumber)}) 的消息`);
    console.log(`[消息路由器] - 优先级: ${handlerInfo.priority}, 独占: ${handlerInfo.exclusive ? '是' : '否'}`);
    return true;
  }

  /**
   * 取消注册消息处理器
   * @param {number} msgType 消息类型
   * @param {String} handlerName 处理器名称
   * @returns {boolean} 取消注册是否成功
   */
  unregisterHandler(msgType, handlerName) {
    msgType = parseInt(msgType);

    if (!this.handlers[msgType]) {
      console.log(`[消息路由器] 没有找到类型 ${msgType} 的处理器`);
      return false;
    }

    const initialLength = this.handlers[msgType].length;
    this.handlers[msgType] = this.handlers[msgType].filter(h => h.name !== handlerName);

    if (this.handlers[msgType].length < initialLength) {
      console.log(`[消息路由器] 已取消注册处理器 "${handlerName}" (类型 ${msgType})`);
      return true;
    } else {
      console.log(`[消息路由器] 未找到名为 "${handlerName}" 的处理器 (类型 ${msgType})`);
      return false;
    }
  }

  /**
   * 识别消息类型
   * @param {Object} message 消息对象
   * @returns {number} 消息类型
   */
  identifyMessageType(message) {
    // 尝试从不同格式的消息中获取类型
    let msgType = 0; // 默认消息类型为0（未知）

    if (message.msg_type !== undefined) {
      msgType = parseInt(message.msg_type);
      console.log(`[消息路由器] 从msg_type字段获取消息类型: ${message.msg_type} -> ${msgType}`);
    } else if (message.MsgType !== undefined) {
      msgType = parseInt(message.MsgType);
      console.log(`[消息路由器] 从MsgType字段获取消息类型: ${message.MsgType} -> ${msgType}`);
    } else if (message.msgType !== undefined) {
      msgType = parseInt(message.msgType);
      console.log(`[消息路由器] 从msgType字段获取消息类型: ${message.msgType} -> ${msgType}`);
    }

    // 处理消息类型是对象的情况
    if (isNaN(msgType) && message.msg_type && typeof message.msg_type === 'object' && message.msg_type.val) {
      msgType = parseInt(message.msg_type.val);
      console.log(`[消息路由器] 从msg_type.val获取消息类型: ${message.msg_type.val} -> ${msgType}`);
    }

    if (isNaN(msgType) && message.MsgType && typeof message.MsgType === 'object' && message.MsgType.val) {
      msgType = parseInt(message.MsgType.val);
      console.log(`[消息路由器] 从MsgType.val获取消息类型: ${message.MsgType.val} -> ${msgType}`);
    }

    // 如果依然无法识别类型，尝试从消息内容推断
    if (isNaN(msgType) || msgType === 0) {
      msgType = this.identifyMessageTypeByContent(message);
    }

    // 确保返回数字类型
    const result = isNaN(msgType) ? 0 : Number(msgType);
    console.log(`[消息路由器] 最终识别到的消息类型: ${result} (${this.getMessageTypeName(result)})`);
    return result;
  }

  /**
   * 根据内容识别消息类型
   * @param {Object} message 消息对象
   * @returns {number} 消息类型
   */
  identifyMessageTypeByContent(message) {
    const content = message.content || message.Content || '';

    if (typeof content !== 'string') {
      console.log(`[消息路由器] 消息内容不是字符串类型，无法推断类型`);
      return 0;
    }

    // 图片消息检测
    if (content.includes('<img>') || content.includes('<IMG>') || content.includes('cdnurl')) {
      console.log(`[消息路由器] 根据内容判断为图片消息(类型3)`);
      return 3;
    }

    // 文件消息检测
    if (content.includes('<file>') || content.includes('<FILE>') ||
        content.includes('filename=') || content.includes('filesize=')) {
      console.log(`[消息路由器] 根据内容判断为文件消息(类型6)`);
      return 6;
    }

    // 语音消息检测
    if (content.includes('<voice>') || content.includes('<VOICE>') ||
        content.includes('voicelength=') || content.includes('.amr')) {
      console.log(`[消息路由器] 根据内容判断为语音消息(类型34)`);
      return 34;
    }

    // 视频消息检测
    if (content.includes('<video>') || content.includes('<VIDEO>') ||
        content.includes('videolength=') || content.includes('.mp4')) {
      console.log(`[消息路由器] 根据内容判断为视频消息(类型43)`);
      return 43;
    }

    // 位置消息检测
    if (content.includes('<location>') || content.includes('<LOCATION>') ||
        content.includes('latitude=') || content.includes('longitude=')) {
      console.log(`[消息路由器] 根据内容判断为位置消息(类型48)`);
      return 48;
    }

    // 名片消息检测
    if (content.includes('<contact>') || content.includes('<CONTACT>') ||
        content.includes('nickname=') || content.includes('username=')) {
      console.log(`[消息路由器] 根据内容判断为名片消息(类型42)`);
      return 42;
    }

    // 应用消息检测
    if (content.includes('<appmsg>') || content.includes('<APPMSG>') ||
        content.includes('<msg>') || content.includes('appid=')) {
      console.log(`[消息路由器] 根据内容判断为应用消息(类型49)`);
      return 49;
    }

    // 系统消息检测
    if (content.includes('加入了群聊') || content.includes('退出了群聊') ||
        content.includes('修改群名为') || content.includes('邀请') ||
        content.includes('撤回了一条消息')) {
      console.log(`[消息路由器] 根据内容判断为系统消息(类型10000)`);
      return 10000;
    }

    // 文本消息检测（最后检查）
    if (content.trim() !== '') {
      console.log(`[消息路由器] 根据内容判断为文本消息(类型1)`);
      return 1;
    }

    console.log(`[消息路由器] 无法从内容推断消息类型，返回未知类型(0)`);
    return 0;
  }

  /**
   * 获取消息类型名称
   * @param {number} msgType 消息类型
   * @returns {string} 类型名称
   */
  getMessageTypeName(msgType) {
    const typeNames = {
      0: '未知消息',
      1: '文本消息',
      3: '图片消息',
      6: '文件消息',
      34: '语音消息',
      42: '名片消息',
      43: '视频消息',
      47: '表情消息',
      48: '位置消息',
      49: '应用消息',
      51: '打开消息',
      10000: '系统消息',
      10002: '撤回消息'
    };
    return typeNames[msgType] || `类型${msgType}`;
  }

  /**
   * 获取消息ID
   * @param {Object} message 消息对象
   * @returns {string} 消息ID
   */
  getMessageId(message) {
    let msgId = message.msg_id || message.MsgId || message.msgId || '';

    // 如果ID是对象，尝试获取其值
    if (typeof msgId === 'object' && msgId !== null) {
      msgId = msgId.val || msgId.str || msgId.toString() || '';
    }

    // 如果仍未获取到ID，生成一个临时ID
    if (!msgId) {
      msgId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
    }

    return String(msgId);
  }

  /**
   * 检查消息是否超时
   * @param {Object} message 消息对象
   * @returns {boolean} 是否超时
   */
  isMessageTimeout(message) {
    if (!this.enableTimeFilter) {
      return false; // 如果禁用时间过滤，则不检查超时
    }

    const createTime = message.CreateTime || message.create_time;
    if (!createTime) {
      console.log(`[消息路由器] 消息缺少时间戳，不过滤`);
      return false; // 如果没有时间戳，不过滤
    }

    // 统一时间戳处理：将所有时间戳转换为毫秒进行比较
    let messageTimestamp;
    if (typeof createTime === 'number') {
      // 判断是秒时间戳还是毫秒时间戳
      messageTimestamp = createTime > 9999999999 ? createTime : createTime * 1000;
    } else if (typeof createTime === 'string') {
      const numericTime = parseInt(createTime);
      if (!isNaN(numericTime)) {
        messageTimestamp = numericTime > 9999999999 ? numericTime : numericTime * 1000;
      } else {
        const dateTime = new Date(createTime).getTime();
        messageTimestamp = isNaN(dateTime) ? Date.now() : dateTime;
      }
    } else {
      const dateTime = new Date(createTime).getTime();
      messageTimestamp = isNaN(dateTime) ? Date.now() : dateTime;
    }

    const currentTime = Date.now();
    const messageAge = currentTime - messageTimestamp;
    const timeoutMilliseconds = this.timeoutMinutes * 60 * 1000;

    const isTimeout = messageAge > timeoutMilliseconds;

    if (isTimeout) {
      const ageMinutes = Math.round(messageAge / (60 * 1000));
      console.log(`[消息路由器] 消息超时: 消息年龄=${ageMinutes}分钟, 超时阈值=${this.timeoutMinutes}分钟`);
      console.info(`Message discarded because its TOO OLD(than ${this.timeoutMinutes} minutes)`);
    }

    return isTimeout;
  }

  /**
   * 检查特殊功能权限（如小红书转发等）
   * @param {string} fromUser 发送者
   * @param {string} content 消息内容
   * @returns {Object} 特殊权限检查结果
   */
  async checkSpecialFunctionPermissions(fromUser, content) {
    try {
      // 检查小红书转发权限
      const xiaohongshuPermission = await this.checkXiaohongshuForwardPermission(fromUser, content);
      if (xiaohongshuPermission.hasPermission) {
        return xiaohongshuPermission;
      }

      // 未来可以在这里添加其他特殊功能的权限检查
      // 例如：图片转发、文件转发等

      return { hasPermission: false, reason: '无特殊功能权限' };
    } catch (error) {
      console.error(`[消息路由器] 特殊功能权限检查失败: ${error.message}`);
      return { hasPermission: false, reason: '权限检查异常' };
    }
  }

  /**
   * 检查小红书转发权限
   * @param {string} fromUser 发送者群组
   * @param {string} content 消息内容
   * @returns {Object} 权限检查结果
   */
  async checkXiaohongshuForwardPermission(fromUser, content) {
    try {
      // 检查是否包含小红书链接
      const hasXiaohongshuContent = content.includes('xiaohongshu.com') ||
                                   content.includes('xhslink.com') ||
                                   content.includes('小红书');

      if (!hasXiaohongshuContent) {
        return { hasPermission: false, reason: '不包含小红书内容' };
      }

      // 获取小红书转发服务
      const xiaohongshuForwarder = this.serviceLoader ?
                                  this.serviceLoader.getService('xiaohongshuForwarder') : null;

      if (!xiaohongshuForwarder) {
        return { hasPermission: false, reason: '小红书转发服务未启用' };
      }

      // 检查是否为小红书转发源群组
      const isSourceGroup = xiaohongshuForwarder.isSourceGroup(fromUser);

      if (isSourceGroup) {
        console.log(`[消息路由器] 🔍 检测到小红书转发场景: 群组=${fromUser}, 内容包含小红书链接`);
        return {
          hasPermission: true,
          reason: '小红书转发功能权限',
          functionName: 'xiaohongshuForward'
        };
      }

      return { hasPermission: false, reason: '非小红书转发源群组' };
    } catch (error) {
      console.error(`[消息路由器] 小红书转发权限检查失败: ${error.message}`);
      return { hasPermission: false, reason: '权限检查异常' };
    }
  }

  /**
   * 全局权限检查 - 在所有处理器之前执行
   * @param {Object} message 消息对象
   * @returns {Object} 权限检查结果
   */
  async checkGlobalPermissions(message) {
    console.log(`[消息路由器] ==================== 全局权限检查开始 ====================`);

    // 标准化消息对象以获取必要字段
    const fromUserName = message.from_user_name || message.FromUserName || '';
    const toUserName = message.to_user_name || message.ToUserName || '';
    const content = message.content || message.Content || '';
    const msgId = message.msg_id || message.MsgId || 'unknown';

    // 提取字符串值（处理对象格式）
    const fromUserStr = typeof fromUserName === 'object' ?
                       (fromUserName.str || JSON.stringify(fromUserName)) :
                       String(fromUserName);
    const toUserStr = typeof toUserName === 'object' ?
                     (toUserName.str || JSON.stringify(toUserName)) :
                     String(toUserName);

    // 更好地处理消息内容
    let contentStr = '';
    if (typeof content === 'object' && content !== null) {
      if (content.str) {
        contentStr = content.str;
      } else if (content.text) {
        contentStr = content.text;
      } else {
        try {
          contentStr = JSON.stringify(content);
        } catch (e) {
          contentStr = '[无法解析的对象内容]';
        }
      }
    } else {
      contentStr = String(content || '');
    }

    console.log(`[消息路由器] 权限检查消息: ID=${msgId}`);
    console.log(`[消息路由器] 发送者: ${fromUserStr}`);
    console.log(`[消息路由器] 接收者: ${toUserStr}`);
    console.log(`[消息路由器] 内容预览: ${contentStr.substring(0, 50)}${contentStr.length > 50 ? '...' : ''}`);

    // 🧧 特殊处理：检查是否为红包消息
    const msgType = message.msg_type || message.MsgType;
    console.log(`[消息路由器] 🧧 开始红包消息检测: 消息类型=${msgType}`);

    if (msgType === 49) {
      console.log(`[消息路由器] 🧧 消息类型49，开始详细红包检测`);
      const isRedPacket = this.isRedPacketMessage(message);
      console.log(`[消息路由器] 🧧 红包检测结果: ${isRedPacket ? '是红包消息' : '不是红包消息'}`);

      if (isRedPacket) {
        console.log(`[消息路由器] 🧧 检测到红包消息，使用红包专用权限检查`);

      try {
        // 获取红包服务
        const redPacketService = this.serviceLoader ? this.serviceLoader.getService('redPacket') : null;

        if (redPacketService && redPacketService.isEnabled && redPacketService.permissionChecker) {
          // 验证权限检查器接口
          if (typeof redPacketService.permissionChecker.checkAllPermissions !== 'function') {
            console.error(`[消息路由器] 🧧 红包权限检查器接口不正确，回退到普通权限检查`);
          } else {
            // 使用红包服务的权限检查器
            const permissionResult = redPacketService.permissionChecker.checkAllPermissions(
              fromUserStr,
              fromUserStr.endsWith('@chatroom') ? fromUserStr : null
            );

            // 验证返回结果格式
            if (permissionResult && typeof permissionResult.allowed === 'boolean') {
              console.log(`[消息路由器] 🧧 红包权限检查结果: ${permissionResult.allowed ? '✅ 允许' : '❌ 拒绝'}`);
              console.log(`[消息路由器] 🧧 红包权限检查原因: ${permissionResult.reason || '默认允许'}`);
              console.log(`[消息路由器] ==================== 全局权限检查结束 ====================`);

              return {
                hasPermission: permissionResult.allowed,
                reason: permissionResult.allowed ? '红包权限检查通过' : `红包权限检查失败: ${permissionResult.reason}`,
                fromUser: fromUserStr,
                isGroup: fromUserStr.endsWith('@chatroom'),
                isRedPacket: true
              };
            } else {
              console.error(`[消息路由器] 🧧 红包权限检查返回格式错误:`, permissionResult);
              console.log(`[消息路由器] 🧧 回退到普通权限检查`);
            }
          }
        } else {
          console.log(`[消息路由器] 🧧 红包服务未启用或权限检查器不可用，按普通消息处理`);
        }
      } catch (error) {
        console.error(`[消息路由器] 🧧 红包权限检查异常: ${error.message}`);
        console.log(`[消息路由器] 🧧 回退到普通权限检查`);
      }
      } else {
        console.log(`[消息路由器] 🧧 不是红包消息，继续普通权限检查`);
      }
    } else {
      console.log(`[消息路由器] 🧧 消息类型不是49，跳过红包检测`);
    }

    const isGroupMsg = fromUserStr.endsWith('@chatroom');
    const botWxid = this.authManager?.getAuthInfo()?.wxid || '';
    const isSentByBot = fromUserStr === botWxid;
    const isReceivedByBot = toUserStr === botWxid;

    // 🔍 增强调试信息 - 详细分析机器人WXID匹配
    console.log(`[消息路由器] ==================== 机器人身份检查 ====================`);
    console.log(`[消息路由器] 机器人WXID: "${botWxid}"`);
    console.log(`[消息路由器] 发送者WXID: "${fromUserStr}"`);
    console.log(`[消息路由器] 接收者WXID: "${toUserStr}"`);
    console.log(`[消息路由器] WXID长度比较: 机器人=${botWxid.length}, 发送者=${fromUserStr.length}, 接收者=${toUserStr.length}`);
    console.log(`[消息路由器] 严格相等检查: fromUserStr === botWxid = ${fromUserStr === botWxid}`);
    console.log(`[消息路由器] 严格相等检查: toUserStr === botWxid = ${toUserStr === botWxid}`);
    console.log(`[消息路由器] ==================== 机器人身份检查结束 ====================`);

    console.log(`[消息路由器] 消息类型: ${isGroupMsg ? '群聊' : '私聊'}`);
    console.log(`[消息路由器] 机器人发送: ${isSentByBot ? '是' : '否'}`);
    console.log(`[消息路由器] 机器人接收: ${isReceivedByBot ? '是' : '否'}`);

    // 🚨 特别检查：如果机器人WXID为空，这是一个严重问题
    if (!botWxid) {
      console.log(`[消息路由器] ⚠️ 严重警告: 机器人WXID为空！这会导致无法正确识别机器人自己发送的消息`);
      console.log(`[消息路由器] AuthManager状态:`, this.authManager ? '存在' : '不存在');
      if (this.authManager) {
        const authInfo = this.authManager.getAuthInfo();
        console.log(`[消息路由器] AuthInfo:`, authInfo);
      }
    }

    // 获取权限服务
    const permissionsService = this.serviceLoader ? this.serviceLoader.getService('permissions') : null;

    if (!permissionsService) {
      console.log(`[消息路由器] ❌ 权限服务未找到，拒绝所有消息处理`);
      console.log(`[消息路由器] ==================== 全局权限检查结束 ====================`);
      return {
        hasPermission: false,
        reason: '权限服务未初始化',
        fromUser: fromUserStr,
        isGroup: isGroupMsg
      };
    }

    // 检查是否为机器人自己发送的消息（跳过处理）
    if (isSentByBot) {
      console.log(`[消息路由器] ✅ 检测到机器人自己发送的消息，跳过处理`);
      console.log(`[消息路由器] 🔍 详细信息:`);
      console.log(`[消息路由器]   - 消息ID: ${msgId}`);
      console.log(`[消息路由器]   - 发送者: ${fromUserStr}`);
      console.log(`[消息路由器]   - 机器人: ${botWxid}`);
      console.log(`[消息路由器]   - 内容: ${contentStr.substring(0, 100)}${contentStr.length > 100 ? '...' : ''}`);
      console.log(`[消息路由器] 🚫 这条消息将被完全忽略，不会触发任何处理逻辑`);
      console.log(`[消息路由器] ==================== 全局权限检查结束 ====================`);
      return {
        hasPermission: false,
        reason: '机器人自己发送的消息',
        fromUser: fromUserStr,
        isGroup: isGroupMsg,
        skipProcessing: true
      };
    }

    // 检查管理员权限
    const isAdmin = permissionsService.hasAdminPermission(fromUserStr);
    console.log(`[消息路由器] 管理员检查: ${isAdmin ? '✅ 是管理员' : '❌ 非管理员'}`);

    // 如果是管理员，直接通过
    if (isAdmin) {
      console.log(`[消息路由器] ✅ 管理员权限通过，允许所有操作`);
      console.log(`[消息路由器] ==================== 全局权限检查结束 ====================`);
      return {
        hasPermission: true,
        isAdmin: true,
        fromUser: fromUserStr,
        isGroup: isGroupMsg,
        reason: '管理员权限'
      };
    }

    // 对于非管理员，检查白名单权限
    let hasWhitelistPermission = false;

    if (isGroupMsg) {
      // 群聊权限检查
      hasWhitelistPermission = permissionsService.isGroupInWhitelist(fromUserStr);
      console.log(`[消息路由器] 群聊白名单检查: ${hasWhitelistPermission ? '✅ 在白名单中' : '❌ 不在白名单中'}`);
    } else {
      // 私聊权限检查
      hasWhitelistPermission = permissionsService.isUserInWhitelist(fromUserStr);
      console.log(`[消息路由器] 用户白名单检查: ${hasWhitelistPermission ? '✅ 在白名单中' : '❌ 不在白名单中'}`);
    }

    if (!hasWhitelistPermission) {
      // 🔍 特殊功能权限检查：检查是否为小红书转发场景
      if (isGroupMsg) {
        const specialPermission = await this.checkSpecialFunctionPermissions(fromUserStr, contentStr);
        if (specialPermission.hasPermission) {
          console.log(`[消息路由器] ✅ 特殊功能权限通过: ${specialPermission.reason}`);
          console.log(`[消息路由器] ==================== 全局权限检查结束 ====================`);
          return {
            hasPermission: true,
            isAdmin: false,
            fromUser: fromUserStr,
            isGroup: isGroupMsg,
            reason: specialPermission.reason,
            specialFunction: specialPermission.functionName
          };
        }
      }

      console.log(`[消息路由器] ❌ 权限检查失败: ${isGroupMsg ? '群聊' : '用户'} ${fromUserStr} 不在白名单中`);
      console.log(`[消息路由器] 🚫 拒绝处理来自陌生${isGroupMsg ? '群聊' : '用户'}的消息`);
      console.log(`[消息路由器] ==================== 全局权限检查结束 ====================`);
      return {
        hasPermission: false,
        reason: `${isGroupMsg ? '群聊' : '用户'}不在白名单中`,
        fromUser: fromUserStr,
        isGroup: isGroupMsg
      };
    }

    console.log(`[消息路由器] ✅ 权限检查通过: ${isGroupMsg ? '群聊' : '用户'} ${fromUserStr} 在白名单中`);
    console.log(`[消息路由器] ==================== 全局权限检查结束 ====================`);
    return {
      hasPermission: true,
      isAdmin: false,
      fromUser: fromUserStr,
      isGroup: isGroupMsg,
      reason: '白名单权限'
    };
  }

  /**
   * 分发消息到对应的处理器
   * @param {Object|Array} messages 单个消息对象或消息数组
   * @returns {Promise<Object>} 处理结果
   */
  async routeMessages(messages) {
    // 🚨 防止无限循环 - 添加调用堆栈跟踪
    const callStack = new Error().stack;
    const routeMessagesCalls = (callStack.match(/routeMessages/g) || []).length;

    if (routeMessagesCalls > 3) {
      console.error(`[消息路由器] 🚨 检测到可能的无限循环！routeMessages被调用${routeMessagesCalls}次`);
      console.error(`[消息路由器] 调用堆栈:`);
      console.error(callStack.split('\n').slice(1, 10).join('\n'));
      return {
        success: false,
        error: '检测到无限循环，停止处理',
        total: Array.isArray(messages) ? messages.length : 1,
        routed: 0
      };
    }

    // 处理单个消息的情况
    if (!Array.isArray(messages)) {
      if (!messages) {
        console.log(`[消息路由器] 收到空消息，跳过处理`);
        return { success: true, total: 0, routed: 0, processed: 0 };
      }
      messages = [messages];
    }

    if (messages.length === 0) {
      console.log(`[消息路由器] 收到空消息数组，跳过处理`);
      return { success: true, total: 0, routed: 0, processed: 0 };
    }

    console.log(`[消息路由器] 收到 ${messages.length} 条消息，开始路由分发`);

    // 🔍 显示批量消息的ID列表，便于调试
    if (messages.length > 1) {
      const messageIds = messages.map(msg => msg.msg_id || msg.MsgId || 'unknown').slice(0, 5);
      console.log(`[消息路由器] 📋 批量消息ID: ${messageIds.join(', ')}${messages.length > 5 ? '...' : ''}`);
    }

    // 🚨 消息去重机制 - 防止重复处理相同消息
    if (!this.processedMessages) {
      this.processedMessages = new Map(); // 使用Map存储时间戳
      this.maxCacheSize = 1000;
      this.cacheExpiryTime = 300000; // 5分钟过期
    }

    const uniqueMessages = [];
    const now = Date.now();

    for (const message of messages) {
      const msgId = message.msg_id || message.MsgId || 'unknown';
      const messageKey = `${msgId}_${message.create_time || message.CreateTime || Date.now()}`;

      // 检查是否已处理过
      if (this.processedMessages.has(messageKey)) {
        const processTime = this.processedMessages.get(messageKey);
        // 如果消息未过期，跳过
        if (now - processTime < this.cacheExpiryTime) {
          console.log(`[消息路由器] ⚠️ 跳过重复消息: ID=${msgId}`);
          continue;
        } else {
          // 过期消息，移除并重新处理
          this.processedMessages.delete(messageKey);
        }
      }

      this.processedMessages.set(messageKey, now);
      uniqueMessages.push(message);
    }

    // {{ EMERGENCY-FIX: 在处理消息后立即检查是否需要清理 }}
    // 每处理100条消息检查一次清理
    if (uniqueMessages.length % 100 === 0 && uniqueMessages.length > 0) {
      this.cleanupExpiredMessages();
    }

    // 智能缓存清理
    this.cleanupExpiredMessages();

    if (uniqueMessages.length < messages.length) {
      console.log(`[消息路由器] 过滤后剩余 ${uniqueMessages.length} 条唯一消息`);
      messages = uniqueMessages;
    }

    if (messages.length === 0) {
      console.log(`[消息路由器] 所有消息都是重复的，跳过处理`);
      return { success: true, total: 0, routed: 0, processed: 0 };
    }

    // 更新总消息统计
    this.stats.total += messages.length;

    // 📝 聊天记录处理 - 在权限检查之前执行，独立于权限系统
    // 聊天记录功能不受权限限制，只要在目标wxid列表中就记录
    try {
      if (this.serviceLoader && typeof this.serviceLoader.getChatRecorderService === 'function') {
        const chatRecorder = this.serviceLoader.getChatRecorderService();
        if (chatRecorder) {
          console.log(`[消息路由器] 📝 开始处理聊天记录，消息数量: ${messages.length}`);

          // 🛡️ 添加循环防护：为聊天记录处理添加标记，避免重复处理
          const messagesForRecord = messages.map(msg => ({
            ...msg,
            _chatRecordProcessed: true  // 标记已被聊天记录处理
          }));

          const recordResult = await chatRecorder.processMessages(messagesForRecord);
          if (recordResult.recorded > 0) {
            console.log(`[消息路由器] 📝 聊天记录服务成功记录 ${recordResult.recorded} 条消息`);
          } else {
            console.log(`[消息路由器] 📝 聊天记录处理完成，无需记录的消息`);
          }
        } else {
          console.log(`[消息路由器] 📝 聊天记录服务未初始化，跳过记录`);
        }
      } else {
        console.log(`[消息路由器] 📝 聊天记录服务不可用，跳过记录`);
      }
    } catch (recordError) {
      console.error(`[消息路由器] 📝 聊天记录处理失败: ${recordError.message}`);
      // 🛡️ 聊天记录处理失败不应该影响其他功能
      console.log(`[消息路由器] 📝 聊天记录处理失败，继续执行其他处理逻辑`);
    }

    // 🚨 全局权限检查 - 在聊天记录处理之后执行（带去重机制）
    const authorizedMessages = [];
    const rejectedMessages = [];
    const permissionCheckCache = new Map(); // 本次批处理的权限检查缓存

    for (const message of messages) {
      const msgId = message.msg_id || message.MsgId || 'unknown';
      const fromUser = message.from_user_name || message.FromUserName || 'unknown';

      // 创建权限检查缓存键
      const permissionCacheKey = `${fromUser}_${msgId}`;

      let permissionResult;

      // 检查是否已经检查过相同用户的权限
      if (permissionCheckCache.has(permissionCacheKey)) {
        permissionResult = permissionCheckCache.get(permissionCacheKey);
        console.log(`[消息路由器] 🔄 使用批处理权限缓存: ${fromUser}`);
      } else {
        // 执行权限检查
        permissionResult = await this.checkGlobalPermissions(message);

        // 缓存权限检查结果（仅在本次批处理中有效）
        permissionCheckCache.set(permissionCacheKey, permissionResult);
      }

      if (permissionResult.skipProcessing) {
        console.log(`[消息路由器] 跳过处理消息: ${permissionResult.reason}`);
        this.stats.skipped++;
        continue;
      }

      if (permissionResult.hasPermission) {
        // 将权限信息附加到消息对象上，供后续处理器使用
        message._permissionInfo = permissionResult;
        authorizedMessages.push(message);
        console.log(`[消息路由器] ✅ 消息通过权限检查，加入处理队列`);
      } else {
        rejectedMessages.push({
          message,
          reason: permissionResult.reason
        });
        console.log(`[消息路由器] ❌ 消息被拒绝: ${permissionResult.reason}`);
        this.stats.skipped++;
      }
    }

    console.log(`[消息路由器] 权限检查结果: 通过 ${authorizedMessages.length} 条，拒绝 ${rejectedMessages.length} 条`);

    // 如果没有消息通过权限检查，直接返回
    if (authorizedMessages.length === 0) {
      console.log(`[消息路由器] 没有消息通过权限检查，结束处理`);
      return {
        success: true,
        total: messages.length,
        routed: 0,
        processed: 0,
        rejected: rejectedMessages.length,
        reason: '所有消息都被权限检查拒绝'
      };
    }

    // 继续处理通过权限检查的消息
    messages = authorizedMessages;



    // 按消息类型分组
    const messagesByType = {};

    for (const message of messages) {
      // 获取消息ID
      const msgId = this.getMessageId(message);

      // 检查消息是否超时
      if (this.isMessageTimeout(message)) {
        console.log(`[消息路由器] 消息 ${msgId} 超时，跳过`);
        this.stats.timeout++;
        continue;
      }

      // 识别消息类型
      const msgType = this.identifyMessageType(message);

      // 初始化当前类型的消息列表
      if (!messagesByType[msgType]) {
        messagesByType[msgType] = [];
      }

      // 添加消息到对应类型的列表中
      messagesByType[msgType].push(message);

      // 更新类型统计
      if (!this.stats.byType[msgType]) {
        this.stats.byType[msgType] = 0;
      }
      this.stats.byType[msgType]++;
    }

    // 显示分类统计
    console.log(`[消息路由器] 消息分类统计:`);
    Object.entries(messagesByType).forEach(([type, msgs]) => {
      console.log(`[消息路由器] - 类型 ${type}: ${msgs.length} 条消息`);
    });

    // 处理每种类型的消息
    const results = {};

    for (const [msgTypeStr, typeMessages] of Object.entries(messagesByType)) {
      // 将字符串类型转换回数字，确保类型一致性
      const msgType = parseInt(msgTypeStr);

      // 根据数字类型查找处理器
      const typeHandlers = this.handlers[msgType] || [];

      if (typeHandlers.length === 0) {
        console.log(`[消息路由器] 类型 ${msgType} 没有注册处理器，跳过 ${typeMessages.length} 条消息`);
        continue;
      }

      console.log(`[消息路由器] 准备将 ${typeMessages.length} 条类型 ${msgType} 的消息分发给 ${typeHandlers.length} 个处理器`);

      // 显示处理器信息
      if (msgType === 1) {
        console.log(`[消息路由器] 文本消息处理器列表:`);
        typeHandlers.forEach((h, index) => {
          console.log(`[消息路由器] - 处理器 ${index + 1}: ${h.name}, 调用次数=${h.stats.calls}, 成功=${h.stats.success}, 错误=${h.stats.errors}`);
        });
      }

      // 🔧 修复：改为批量处理，避免嵌套循环导致的性能问题
      // 检查是否有特定命令前缀的消息
      const commandMessages = [];
      const regularMessages = [];

      for (const msg of typeMessages) {
        const content = this.extractMessageContent(msg);
        let specificHandlerName = null;

        // 判断是否含有特定命令前缀
        for (const [prefix, handlerName] of Object.entries(this.commandRoutes)) {
          if (content && content.trim().startsWith(prefix)) {
            specificHandlerName = handlerName;
            console.log(`[消息路由器] 检测到特定命令前缀 "${prefix}"，该消息将只路由到 "${handlerName}" 处理器`);
            break;
          }
        }

        if (specificHandlerName) {
          commandMessages.push({ msg, handlerName: specificHandlerName });
        } else {
          regularMessages.push(msg);
        }
      }

      // 处理特定命令消息
      for (const { msg, handlerName } of commandMessages) {
        const targetHandler = typeHandlers.find(h => h.name === handlerName);
        if (targetHandler) {
          try {
            console.log(`[消息路由器] ► 处理特定命令消息，处理器: "${handlerName}"`);
            targetHandler.stats.calls++;
            const result = await targetHandler.handler.handleMessages([msg], this.serviceLoader, this.authManager);

            if (result && result.success) {
              targetHandler.stats.success++;
              this.stats.routed++;
            } else {
              targetHandler.stats.errors++;
            }
          } catch (error) {
            targetHandler.stats.errors++;
            this.stats.errors++;
            console.error(`[消息路由器] 特定命令处理器 "${handlerName}" 异常: ${error.message}`);
          }
        }
      }

      // 批量处理常规消息（支持优先级和独占处理）
      if (regularMessages.length > 0) {
        console.log(`[消息路由器] 开始处理 ${regularMessages.length} 条类型 ${msgType}(${this.getMessageTypeName(msgType)}) 的常规消息`);
        console.log(`[消息路由器] 可用处理器: ${typeHandlers.map(h => `${h.name}(优先级:${h.priority}${h.exclusive ? ',独占' : ''})`).join(', ')}`);

        let exclusiveHandled = false; // 标记是否已被独占处理器处理

        for (const handlerInfo of typeHandlers) {
          // 如果已经被独占处理器处理，跳过后续处理器
          if (exclusiveHandled) {
            console.log(`[消息路由器] 跳过处理器 "${handlerInfo.name}"，消息已被独占处理器处理`);
            continue;
          }

          try {
            // 检查处理器条件
            if (handlerInfo.conditions && handlerInfo.conditions.length > 0) {
              const conditionMet = this.checkHandlerConditions(handlerInfo, regularMessages);
              if (!conditionMet) {
                console.log(`[消息路由器] 处理器 "${handlerInfo.name}" 条件不满足，跳过`);
                continue;
              }
            }

            // 特别记录文本消息处理
            if (msgType === 1) {
              console.log(`[消息路由器] ► 正在调用文本处理器 "${handlerInfo.name}" (优先级:${handlerInfo.priority}) 处理 ${regularMessages.length} 条文本消息`);
              if (regularMessages.length > 0) {
                console.log(`[消息路由器] ► 第一条文本消息ID: ${this.getMessageId(regularMessages[0])}`);
                const content = this.extractMessageContent(regularMessages[0]);
                const preview = content ? content.substring(0, 50) + '...' : '[无内容]';
                console.log(`[消息路由器] ► 文本内容: "${preview}"`);
              }
            } else {
              console.log(`[消息路由器] 调用处理器 "${handlerInfo.name}" (优先级:${handlerInfo.priority}${handlerInfo.exclusive ? ',独占' : ''}) 处理 ${regularMessages.length} 条类型 ${msgType} 的消息`);
            }

            // 更新处理器调用次数
            handlerInfo.stats.calls++;

            // 🔧 批量调用处理器，而不是逐条处理
            const result = await handlerInfo.handler.handleMessages(regularMessages, this.serviceLoader, this.authManager);

            // 获取处理结果
            if (result && result.success) {
              handlerInfo.stats.success++;
              this.stats.routed++;

              // 特别记录文本消息处理成功
              if (msgType === 1) {
                console.log(`[消息路由器] ✓ 文本处理器 "${handlerInfo.name}" 处理成功: 处理=${result.processed || regularMessages.length}`);
              } else {
                // 记录其他类型消息的处理成功，包括跳过机器人消息的情况
                const skippedInfo = result.skippedBotMessages ? `, 跳过机器人消息=${result.skippedBotMessages}` : '';
                console.log(`[消息路由器] ✓ 处理器 "${handlerInfo.name}" 处理类型 ${msgType} 成功: 处理=${result.processed || regularMessages.length}${skippedInfo}`);
              }

              // 保存处理结果
              if (!results[msgType]) {
                results[msgType] = [];
              }
              results[msgType].push({
                handler: handlerInfo.name,
                success: true,
                processed: result.processed || regularMessages.length,
                skippedBotMessages: result.skippedBotMessages || 0,
                details: result
              });

              // 如果是独占处理器且处理成功，标记为已独占处理
              if (handlerInfo.exclusive) {
                exclusiveHandled = true;
                console.log(`[消息路由器] 独占处理器 "${handlerInfo.name}" 处理成功，停止后续处理器`);
              }
            } else {
              handlerInfo.stats.errors++;

              // 特别记录文本消息处理失败
              if (msgType === 1) {
                console.error(`[消息路由器] ✗ 文本处理器 "${handlerInfo.name}" 处理失败: ${result ? (result.error || result.reason || '未知错误') : '未返回结果'}`);
              } else {
                console.error(`[消息路由器] 处理器 "${handlerInfo.name}" 处理类型 ${msgType} 的消息失败: ${result ? (result.error || result.reason || '未知错误') : '未返回结果'}`);
              }

              // 保存错误结果
              if (!results[msgType]) {
                results[msgType] = [];
              }
              results[msgType].push({
                handler: handlerInfo.name,
                success: false,
                error: result ? (result.error || result.reason || '未知错误') : '未返回结果'
              });

              // 独占处理器失败时不阻止后续处理器
              if (handlerInfo.exclusive) {
                console.log(`[消息路由器] 独占处理器 "${handlerInfo.name}" 处理失败，继续尝试后续处理器`);
              }
            }
          } catch (error) {
            handlerInfo.stats.errors++;
            this.stats.errors++;

            console.error(`[消息路由器] 调用处理器 "${handlerInfo.name}" 时发生异常: ${error.message}`);
            if (error.stack) {
              console.error(`[消息路由器] 错误堆栈: ${error.stack}`);
            }

            // 保存错误结果
            if (!results[msgType]) {
              results[msgType] = [];
            }
            results[msgType].push({
              handler: handlerInfo.name,
              success: false,
              error: error.message
            });

            // 独占处理器异常时不阻止后续处理器
            if (handlerInfo.exclusive) {
              console.log(`[消息路由器] 独占处理器 "${handlerInfo.name}" 发生异常，继续尝试后续处理器`);
            }
          }
        }
      }
    }

    // 返回处理结果汇总
    return {
      success: true,
      total: messages.length,
      processed: Object.values(messagesByType).reduce((sum, msgs) => sum + msgs.length, 0),
      routed: this.stats.routed,
      skipped: this.stats.skipped,
      errors: this.stats.errors,
      results
    };
  }

  /**
   * 从消息对象中提取内容
   * @param {Object} message 消息对象
   * @returns {string} 消息内容
   */
  extractMessageContent(message) {
    if (!message) return '';

    // 尝试从不同的属性中获取内容
    let content = message.content || message.Content || message.contentStr || '';

    // 处理content字段
    if (typeof content === 'string') {
      return content;
    } else if (content && typeof content === 'object') {
      // 如果content是对象，尝试获取其字符串表示
      if (content.str) {
        return content.str;
      }
      if (content.text) {
        return content.text;
      }

      // 尝试toString方法
      if (content.toString && typeof content.toString === 'function' && content.toString() !== '[object Object]') {
        return content.toString();
      }

      // 最后尝试JSON.stringify
      try {
        return JSON.stringify(content);
      } catch (e) {
        return '[无法解析的对象内容]';
      }
    }

    return String(content || '');
  }

  /**
   * 检查处理器条件
   * @param {Object} handlerInfo 处理器信息
   * @param {Array} messages 消息列表
   * @returns {boolean} 条件是否满足
   */
  checkHandlerConditions(handlerInfo, messages) {
    if (!handlerInfo.conditions || handlerInfo.conditions.length === 0) {
      return true; // 没有条件则默认满足
    }

    try {
      for (const condition of handlerInfo.conditions) {
        if (typeof condition === 'function') {
          // 函数条件
          if (!condition(messages, this.serviceLoader, this.authManager)) {
            return false;
          }
        } else if (typeof condition === 'object') {
          // 对象条件
          if (condition.type === 'content') {
            // 内容条件
            const hasMatchingContent = messages.some(msg => {
              const content = this.extractMessageContent(msg);
              if (condition.contains) {
                return content.includes(condition.contains);
              }
              if (condition.startsWith) {
                return content.startsWith(condition.startsWith);
              }
              if (condition.regex) {
                return new RegExp(condition.regex).test(content);
              }
              return true;
            });
            if (!hasMatchingContent) {
              return false;
            }
          } else if (condition.type === 'user') {
            // 用户条件
            const hasMatchingUser = messages.some(msg => {
              const fromUser = msg.from_user_name || msg.FromUserName || '';
              if (condition.includes) {
                return condition.includes.includes(fromUser);
              }
              if (condition.excludes) {
                return !condition.excludes.includes(fromUser);
              }
              return true;
            });
            if (!hasMatchingUser) {
              return false;
            }
          }
        }
      }
      return true;
    } catch (error) {
      console.error(`[消息路由器] 检查处理器条件失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 检查消息是否为红包消息
   * @param {Object} message 消息对象
   * @returns {boolean} 是否为红包消息
   */
  isRedPacketMessage(message) {
    try {
      console.log('[消息路由器] 🧧 开始红包消息检测');

      if (!message) {
        console.log('[消息路由器] 🧧 消息对象为空，不是红包消息');
        return false;
      }

      // 检查消息类型是否为应用消息
      const msgType = message.msg_type || message.MsgType;
      console.log(`[消息路由器] 🧧 消息类型检查: ${msgType}`);
      if (msgType !== 49) {
        console.log(`[消息路由器] 🧧 消息类型不是49，不是红包消息`);
        return false;
      }

      // 检查消息内容
      let content = message.content || message.Content || '';
      console.log(`[消息路由器] 🧧 原始消息内容类型: ${typeof content}`);

      // 处理对象类型的内容
      if (typeof content === 'object' && content !== null) {
        console.log(`[消息路由器] 🧧 消息内容是对象，尝试提取字符串`);
        if (content.str) {
          content = content.str;
          console.log(`[消息路由器] 🧧 从content.str提取内容`);
        } else if (content.text) {
          content = content.text;
          console.log(`[消息路由器] 🧧 从content.text提取内容`);
        } else {
          try {
            content = JSON.stringify(content);
            console.log(`[消息路由器] 🧧 将对象转换为JSON字符串`);
          } catch (e) {
            console.log(`[消息路由器] 🧧 无法转换对象为字符串`);
            content = '';
          }
        }
      }

      // 确保content是字符串
      content = String(content || '');
      console.log(`[消息路由器] 🧧 最终消息内容长度: ${content.length}`);
      console.log(`[消息路由器] 🧧 最终消息内容类型: ${typeof content}`);

      if (!content || content.length === 0) {
        console.log(`[消息路由器] 🧧 消息内容为空，不是红包消息`);
        return false;
      }

      // 显示内容片段用于调试
      const contentPreview = content.substring(0, 200) + (content.length > 200 ? '...' : '');
      console.log(`[消息路由器] 🧧 消息内容预览: ${contentPreview}`);

      // 更精确的红包检测逻辑
      const hasWxpayProtocol = content.includes('wxpay://');
      const hasRedPacketXML = content.includes('<wcpayinfo>') && content.includes('</wcpayinfo>');
      const hasSendId = /sendid=\d+/.test(content);
      const hasChannelId = /channelid=\d+/.test(content);
      const hasRedPacketTitle = content.includes('微信红包') || content.includes('恭喜发财');

      console.log(`[消息路由器] 🧧 红包特征检测:`);
      console.log(`[消息路由器] 🧧 - wxpay协议: ${hasWxpayProtocol}`);
      console.log(`[消息路由器] 🧧 - 红包XML: ${hasRedPacketXML}`);
      console.log(`[消息路由器] 🧧 - sendId: ${hasSendId}`);
      console.log(`[消息路由器] 🧧 - channelId: ${hasChannelId}`);
      console.log(`[消息路由器] 🧧 - 红包标题: ${hasRedPacketTitle}`);

      // 需要满足多个条件才认为是红包消息
      const isRedPacket = hasWxpayProtocol && hasRedPacketXML && (hasSendId || hasChannelId);

      console.log(`[消息路由器] 🧧 最终判断: ${isRedPacket ? '是红包消息' : '不是红包消息'}`);

      if (isRedPacket) {
        console.log('[消息路由器] ✅ 检测到红包消息特征');
        return true;
      }

      console.log('[消息路由器] ❌ 不满足红包消息条件');
      return false;

    } catch (error) {
      console.error('[消息路由器] 检测红包消息失败:', error.message);
      return false;
    }
  }

  /**
   * 获取路由器统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      timeFilterEnabled: this.enableTimeFilter,
      timeoutMinutes: this.timeoutMinutes,
      handlerStats: Object.entries(this.handlers).map(([type, handlers]) => ({
        type,
        handlers: handlers.map(h => ({
          name: h.name,
          stats: h.stats
        }))
      }))
    };
  }

  /**
   * 销毁消息路由器，清理资源
   */
  destroy() {
    // 清理内存清理定时器
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // 清空缓存
    this.clearCache();

    // 清空处理器
    this.handlers = {};

    console.log('[消息路由器] 已销毁，资源已清理');
  }
}

module.exports = MessageRouter;