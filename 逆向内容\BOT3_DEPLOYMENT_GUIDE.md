# 🤖 第三个微信机器人部署指南

## 📋 配置总览

已为第三个微信机器人配置了完全独立的运行环境，避免与其他机器人实例冲突。

### 🔧 修改的配置项

| 配置项 | 原值 | 新值 | 说明 |
|--------|------|------|------|
| `port` | 8055 | **8057** | 避免端口冲突 |
| `apiVersion` | "" | **v3** | 标识第三个版本 |
| `ghWxid` | "" | **wechat_bot_3** | 机器人标识 |
| `adminKey` | 原密钥 | **3rd_bot_admin_key_2025_secure** | 独立管理密钥 |
| `redisConfig.Db` | 1 | **3** | 独立Redis数据库 |
| `mySqlConnectStr` | mysql | **wechat_bot3** | 独立MySQL数据库 |
| `rocketMq` | false | **true** | 启用RocketMQ |
| `topic` | wx_sync_msg_topic | **wx_sync_msg_topic_bot3** | 独立消息主题 |
| `clusterName` | "" | **wechat_bot3_cluster** | 集群标识 |

## 🚀 部署步骤

### 1. 数据库准备

#### MySQL数据库创建
```sql
-- 创建第三个机器人专用数据库
CREATE DATABASE wechat_bot3 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权（可选）
CREATE USER 'bot3_user'@'localhost' IDENTIFIED BY 'bot3_password';
GRANT ALL PRIVILEGES ON wechat_bot3.* TO 'bot3_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Redis数据库
```bash
# Redis会自动使用Db 3，无需额外配置
# 确保Redis服务正常运行即可
redis-cli ping
```

### 2. RocketMQ配置

#### 创建专用Topic
```bash
# 连接到RocketMQ管理控制台或使用命令行工具
# 创建第三个机器人专用的消息主题
sh mqadmin updateTopic -c DefaultCluster -t wx_sync_msg_topic_bot3
```

### 3. 程序部署

#### 编译程序
```bash
# 在逆向内容目录下编译
go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o myapp-linux-bot3 .
```

#### 部署文件
```bash
# 创建部署目录
mkdir -p /opt/wechat-bot3

# 复制文件
cp myapp-linux-bot3 /opt/wechat-bot3/
cp -r assets/ /opt/wechat-bot3/

# 设置权限
chmod +x /opt/wechat-bot3/myapp-linux-bot3
chown -R bot3:bot3 /opt/wechat-bot3/
```

### 4. 系统服务配置

#### 创建systemd服务
```bash
# 创建服务文件
sudo nano /etc/systemd/system/wechat-bot3.service
```

```ini
[Unit]
Description=WeChat Bot 3 Service
After=network.target mysql.service redis.service

[Service]
Type=simple
User=bot3
Group=bot3
WorkingDirectory=/opt/wechat-bot3
ExecStart=/opt/wechat-bot3/myapp-linux-bot3
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=BOT_ID=3
Environment=BOT_NAME=wechat_bot_3

[Install]
WantedBy=multi-user.target
```

#### 启动服务
```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable wechat-bot3
sudo systemctl start wechat-bot3

# 检查状态
sudo systemctl status wechat-bot3
```

## 🔍 验证部署

### 1. 服务状态检查
```bash
# 检查端口监听
netstat -tlnp | grep 8057

# 检查进程
ps aux | grep myapp-linux-bot3

# 检查日志
sudo journalctl -u wechat-bot3 -f
```

### 2. API接口测试
```bash
# 健康检查
curl http://localhost:8057/health

# 管理接口测试
curl -H "Admin-Key: 3rd_bot_admin_key_2025_secure" \
     http://localhost:8057/api/status
```

### 3. WebSocket连接测试
```bash
# 使用wscat测试WebSocket
wscat -c ws://localhost:8057/ws
```

### 4. 数据库连接验证
```bash
# 检查MySQL连接
mysql -u root -p -e "SHOW DATABASES;" | grep wechat_bot3

# 检查Redis连接
redis-cli -n 3 ping
```

## 📊 监控配置

### 1. 日志监控
```bash
# 创建日志目录
mkdir -p /var/log/wechat-bot3

# 配置日志轮转
sudo nano /etc/logrotate.d/wechat-bot3
```

```
/var/log/wechat-bot3/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 bot3 bot3
}
```

### 2. 性能监控
```bash
# 创建监控脚本
nano /opt/wechat-bot3/monitor.sh
```

```bash
#!/bin/bash
# 第三个机器人监控脚本

BOT_PID=$(pgrep -f myapp-linux-bot3)
if [ -z "$BOT_PID" ]; then
    echo "$(date): Bot3 is not running, restarting..."
    systemctl restart wechat-bot3
else
    # 检查内存使用
    MEMORY=$(ps -p $BOT_PID -o rss= | awk '{print $1/1024}')
    echo "$(date): Bot3 Memory Usage: ${MEMORY}MB"
    
    # 检查端口
    if ! netstat -tlnp | grep -q 8057; then
        echo "$(date): Port 8057 not listening, restarting..."
        systemctl restart wechat-bot3
    fi
fi
```

## 🔧 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :8057
   # 修改配置文件中的端口号
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库是否存在
   mysql -u root -p -e "SHOW DATABASES;" | grep wechat_bot3
   # 检查用户权限
   ```

3. **Redis连接失败**
   ```bash
   # 检查Redis服务
   systemctl status redis
   # 测试连接
   redis-cli -n 3 ping
   ```

4. **RocketMQ连接问题**
   ```bash
   # 检查RocketMQ服务
   jps | grep Namesrv
   # 检查Topic是否存在
   ```

## 🎯 多机器人管理

### 端口分配建议
- 机器人1: 8055
- 机器人2: 8056  
- 机器人3: 8057
- 机器人4: 8058 (如需要)

### Redis数据库分配
- 机器人1: Db 1
- 机器人2: Db 2
- 机器人3: Db 3
- 机器人4: Db 4 (如需要)

### MySQL数据库命名
- 机器人1: wechat_bot1
- 机器人2: wechat_bot2
- 机器人3: wechat_bot3
- 机器人4: wechat_bot4 (如需要)

## 🎉 部署完成

第三个微信机器人现在已配置完成，具有：

✅ **独立的端口** (8057)  
✅ **独立的数据库** (wechat_bot3)  
✅ **独立的Redis空间** (Db 3)  
✅ **独立的消息队列** (wx_sync_msg_topic_bot3)  
✅ **独立的管理密钥**  
✅ **完整的RocketMQ修复**  

可以与其他机器人实例完全独立运行，互不干扰！
