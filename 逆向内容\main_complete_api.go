package main

import (
    "encoding/json"
    "fmt"
    "net/http"
    "os"
    "time"
    "log"
    "strconv"
    "math/rand"
    "io"
    "strings"
    "regexp"
)

// 微信协议相关常量
const (
    // 微信Web版API端点 - 基于原始程序的真实配置
    WECHAT_LOGIN_URL     = "https://login.wx.qq.com"
    WECHAT_WEB_URL       = "https://wx.qq.com"
    WECHAT_WEBPUSH_URL   = "https://webpush.wx.qq.com"

    // API路径 - 基于原始程序逆向的真实路径
    API_JSLOGIN          = "/jslogin"
    API_LOGIN            = "/cgi-bin/mmwebwx-bin/login"
    API_WEBWXINIT        = "/cgi-bin/mmwebwx-bin/webwxinit"
    API_WEBWXGETCONTACT  = "/cgi-bin/mmwebwx-bin/webwxgetcontact"
    API_WEBWXSYNC        = "/cgi-bin/mmwebwx-bin/webwxsync"
    API_WEBWXSENDMSG     = "/cgi-bin/mmwebwx-bin/webwxsendmsg"

    // 应用信息 - 基于原始程序的配置
    APP_ID = "wx782c26e4c19acffb"
    USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
)

type Config struct {
    Debug    bool   `json:"debug"`
    Host     string `json:"host"`
    Port     string `json:"port"`
    APIVersion string `json:"apiVersion"`
    GhWxid   string `json:"ghWxid"`
    AdminKey string `json:"adminKey"`
    RedisConfig struct {
        Host string `json:"Host"`
        Port int    `json:"Port"`
        Db   int    `json:"Db"`
        Pass string `json:"Pass"`
        OriginalUrl string `json:"OriginalUrl"`
    } `json:"redisConfig"`
    MySQLConnectStr string `json:"mySqlConnectStr"`
    RocketMq        bool   `json:"rocketMq"`
    RocketMqHost    string `json:"rocketMqHost"`
    Topic           string `json:"topic"`
}

// 微信协议相关结构 - 基于原始程序的真实数据结构
type WeChatSession struct {
    UUID        string `json:"uuid"`
    RedirectUri string `json:"redirect_uri"`
    Skey        string `json:"skey"`
    Wxsid       string `json:"wxsid"`
    Wxuin       string `json:"wxuin"`
    PassTicket  string `json:"pass_ticket"`
    DeviceID    string `json:"device_id"`
    IsLogin     bool   `json:"is_login"`
    User        *WeChatUser `json:"user,omitempty"`
}

type WeChatUser struct {
    Uin      int64  `json:"Uin"`
    UserName string `json:"UserName"`
    NickName string `json:"NickName"`
    HeadImg  string `json:"HeadImgUrl"`
}

// 微信登录状态
type LoginStatus struct {
    Status    int    `json:"status"`
    Message   string `json:"message"`
    LoginUrl  string `json:"loginUrl,omitempty"`
    QrCode    string `json:"qrCode,omitempty"`
    WxId      string `json:"wxId,omitempty"`
    NickName  string `json:"nickName,omitempty"`
}

// 授权码响应
type AuthKeyResponse struct {
    Code    int      `json:"Code"`
    Message string   `json:"Message"`
    Data    []string `json:"Data,omitempty"`
}

// 唤醒登录请求
type WakeUpLoginRequest struct {
    Check bool   `json:"Check"`
    Proxy string `json:"Proxy"`
}

// 生成授权码请求
type GenAuthKeyRequest struct {
    Count int `json:"Count"`
    Days  int `json:"Days"`
}

var config Config
var wechatSession *WeChatSession
var httpClient *http.Client

// 兼容性变量 - 保持与原代码的兼容性
var isLoggedIn = false
var currentWxId = ""
var currentNickName = ""

// 初始化微信会话
func initWeChatSession() {
    wechatSession = &WeChatSession{
        DeviceID: generateDeviceID(),
        IsLogin:  false,
    }

    httpClient = &http.Client{
        Timeout: 30 * time.Second,
    }
}

func main() {
    // 初始化随机种子
    rand.Seed(time.Now().UnixNano())

    // 初始化微信会话
    initWeChatSession()

    // 默认配置 - 基于原始程序的真实配置
    config = Config{
        Debug:      false,
        Host:       "127.0.0.1",
        Port:       "8057",
        APIVersion: "v1.0.0",
        GhWxid:     "wechat_bot_3",
        AdminKey:   "3rd_bot_admin_key_2025_secure",
    }
    config.RedisConfig.Host = "127.0.0.1"
    config.RedisConfig.Port = 6379
    config.RedisConfig.Db = 3
    config.RedisConfig.Pass = ""
    config.RedisConfig.OriginalUrl = "redis://127.0.0.1:6379/3"
    config.MySQLConnectStr = "root:123456@tcp(127.0.0.1:3306)/wechat_bot3?charset=utf8mb4&parseTime=true&loc=Local"
    config.RocketMq = true
    config.RocketMqHost = "127.0.0.1:9876"
    config.Topic = "wx_sync_msg_topic_bot3"
    
    // 读取配置文件
    if data, err := os.ReadFile("assets/setting.json"); err == nil {
        if err := json.Unmarshal(data, &config); err != nil {
            log.Printf("Warning: Failed to parse config: %v", err)
        }
    }
    
    // 设置路由
    setupRoutes()
    
    // 启动服务器
    addr := fmt.Sprintf("%s:%s", config.Host, config.Port)
    
    fmt.Printf("🤖 Starting WeChat Bot 3 (Complete API Version)\n")
    fmt.Printf("📡 Listening on: %s\n", addr)
    fmt.Printf("🔑 Bot ID: %s\n", config.GhWxid)
    fmt.Printf("🗄️  Redis DB: %d\n", config.RedisConfig.Db)
    fmt.Printf("🗃️  MySQL DB: wechat_bot3\n")
    fmt.Printf("📨 RocketMQ Topic: %s\n", config.Topic)
    fmt.Printf("🔑 Admin Key: %s\n", config.AdminKey)
    fmt.Printf("\n✅ Third WeChat Bot with Complete API is ready!\n\n")
    
    log.Fatal(http.ListenAndServe(addr, nil))
}

func setupRoutes() {
    // 健康检查
    http.HandleFunc("/health", handleHealth)
    
    // 登录相关API
    http.HandleFunc("/login/WakeUpLogin", handleWakeUpLogin)
    http.HandleFunc("/login/GetLoginStatus", handleGetLoginStatus)
    http.HandleFunc("/login/GenAuthKey", handleGenAuthKey)
    http.HandleFunc("/login/GenAuthKey2", handleGenAuthKey2)
    
    // 管理相关API
    http.HandleFunc("/admin/GenAuthKey", handleAdminGenAuthKey)
    http.HandleFunc("/admin/GenAuthKey1", handleAdminGenAuthKey1)
    http.HandleFunc("/admin/GenWxAuthKey", handleAdminGenWxAuthKey)
    
    // API状态
    http.HandleFunc("/api/status", handleApiStatus)
    
    // WebSocket (简化版本)
    http.HandleFunc("/ws", handleWebSocket)
    
    // 登录二维码相关API
    http.HandleFunc("/login/GetLoginQrCodeNew", handleGetLoginQrCodeNew)

    // 调试接口：直接返回二维码URL
    http.HandleFunc("/login/GetQrCodeUrl", handleGetQrCodeUrl)

    // 其他可能的API
    http.HandleFunc("/", handleDefault)
}

func handleHealth(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    response := map[string]interface{}{
        "status":    "ok",
        "bot_id":    config.GhWxid,
        "port":      config.Port,
        "version":   "v1.0.0-complete-api",
        "timestamp": time.Now().Unix(),
        "config": map[string]interface{}{
            "redis_db":     config.RedisConfig.Db,
            "mysql_db":     "wechat_bot3",
            "rocketmq":     config.RocketMq,
            "topic":        config.Topic,
        },
        "login_status": isLoggedIn,
    }
    json.NewEncoder(w).Encode(response)
}

func handleWakeUpLogin(w http.ResponseWriter, r *http.Request) {
    if r.Method != "POST" {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }
    
    var req WakeUpLoginRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    
    // 模拟唤醒登录逻辑
    if !isLoggedIn {
        // 模拟需要扫码登录
        qrCodeUrl := generateQRCode()
        response := map[string]interface{}{
            "status":    1,
            "message":   "需要扫码登录",
            "loginUrl":  fmt.Sprintf("http://%s:%s/qrcode", config.Host, config.Port),
            "qrCode":    qrCodeUrl,
            "url":       qrCodeUrl,
            "qr_code":   qrCodeUrl,
            "qr_url":    qrCodeUrl,
            "login_url": qrCodeUrl,
            // 可能的嵌套结构
            "data": map[string]interface{}{
                "url":     qrCodeUrl,
                "qrCode":  qrCodeUrl,
                "qr_code": qrCodeUrl,
            },
            "result": map[string]interface{}{
                "url":     qrCodeUrl,
                "qrCode":  qrCodeUrl,
                "qr_code": qrCodeUrl,
            },
        }
        json.NewEncoder(w).Encode(response)
    } else {
        // 已登录状态
        response := LoginStatus{
            Status:   200,
            Message:  "登录成功",
            WxId:     currentWxId,
            NickName: currentNickName,
        }
        json.NewEncoder(w).Encode(response)
    }
}

// 检查真实的微信登录状态
func checkRealLoginStatus() (int, string, error) {
    if wechatSession.UUID == "" {
        return 0, "UUID未生成", fmt.Errorf("UUID not found")
    }

    // 使用真实的微信登录检查API
    timestamp := time.Now().UnixMilli()
    apiUrl := fmt.Sprintf("https://login.wx.qq.com/cgi-bin/mmwebwx-bin/login?loginicon=true&uuid=%s&tip=0&r=%d&_=%d",
        wechatSession.UUID, ^timestamp, timestamp)

    log.Printf("检查登录状态: %s", apiUrl)

    req, err := http.NewRequest("GET", apiUrl, nil)
    if err != nil {
        log.Printf("创建登录状态请求失败: %v", err)
        return 0, "", err
    }

    // 使用与UUID请求相同的配置
    req.Header.Set("User-Agent", "curl/7.81.0")
    req.Header.Set("Accept", "*/*")
    req.Header.Set("Host", "login.wx.qq.com")

    // 创建新的HTTP客户端
    client := &http.Client{
        Timeout: 30 * time.Second,
    }

    resp, err := client.Do(req)
    if err != nil {
        log.Printf("登录状态请求失败: %v", err)
        return 0, "", err
    }
    defer resp.Body.Close()

    body, err := io.ReadAll(resp.Body)
    if err != nil {
        log.Printf("读取登录状态响应失败: %v", err)
        return 0, "", err
    }

    bodyStr := string(body)
    log.Printf("登录状态检查响应: %s", bodyStr)

    // 解析登录状态
    if strings.Contains(bodyStr, "window.code=200") {
        // 登录成功，提取redirect_uri
        re := regexp.MustCompile(`window.redirect_uri="([^"]+)"`)
        matches := re.FindStringSubmatch(bodyStr)
        if len(matches) >= 2 {
            wechatSession.RedirectUri = matches[1]
            wechatSession.IsLogin = true
            // 同步到兼容性变量
            isLoggedIn = true
            return 200, "登录成功", nil
        }
    } else if strings.Contains(bodyStr, "window.code=201") {
        return 201, "已扫码，等待确认", nil
    } else if strings.Contains(bodyStr, "window.code=408") {
        return 408, "登录超时", nil
    }

    return 400, "等待扫码", nil
}

func handleGetLoginStatus(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")

    log.Printf("收到登录状态检查请求")

    // 检查真实的登录状态
    status, message, err := checkRealLoginStatus()
    if err != nil {
        log.Printf("检查登录状态失败: %v", err)
        status = 1
        message = "检查失败"
    }

    response := LoginStatus{
        Status:  status,
        Message: message,
    }

    if wechatSession.IsLogin && wechatSession.User != nil {
        response.WxId = wechatSession.User.UserName
        response.NickName = wechatSession.User.NickName
        // 同步到兼容性变量
        currentWxId = wechatSession.User.UserName
        currentNickName = wechatSession.User.NickName
    }

    log.Printf("返回登录状态: %d - %s", status, message)
    json.NewEncoder(w).Encode(response)
}

func handleGenAuthKey(w http.ResponseWriter, r *http.Request) {
    // 验证管理员密钥
    key := r.URL.Query().Get("key")
    if key != config.AdminKey {
        w.WriteHeader(http.StatusUnauthorized)
        json.NewEncoder(w).Encode(AuthKeyResponse{
            Code:    401,
            Message: "Invalid admin key",
        })
        return
    }
    
    var req GenAuthKeyRequest
    if r.Method == "POST" {
        json.NewDecoder(r.Body).Decode(&req)
    } else {
        // GET请求从URL参数获取
        if countStr := r.URL.Query().Get("count"); countStr != "" {
            req.Count, _ = strconv.Atoi(countStr)
        }
        if daysStr := r.URL.Query().Get("days"); daysStr != "" {
            req.Days, _ = strconv.Atoi(daysStr)
        }
    }
    
    // 默认值
    if req.Count <= 0 {
        req.Count = 1
    }
    if req.Days <= 0 {
        req.Days = 30
    }
    
    // 生成授权码
    authKeys := generateAuthKeys(req.Count, req.Days)
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(AuthKeyResponse{
        Code:    200,
        Message: "授权码生成成功",
        Data:    authKeys,
    })
}

func handleGenAuthKey2(w http.ResponseWriter, r *http.Request) {
    handleGenAuthKey(w, r)
}

func handleAdminGenAuthKey(w http.ResponseWriter, r *http.Request) {
    handleGenAuthKey(w, r)
}

func handleAdminGenAuthKey1(w http.ResponseWriter, r *http.Request) {
    handleGenAuthKey(w, r)
}

func handleAdminGenWxAuthKey(w http.ResponseWriter, r *http.Request) {
    handleGenAuthKey(w, r)
}

func handleApiStatus(w http.ResponseWriter, r *http.Request) {
    adminKey := r.Header.Get("Admin-Key")
    if adminKey != config.AdminKey {
        w.WriteHeader(http.StatusUnauthorized)
        json.NewEncoder(w).Encode(map[string]string{"error": "Invalid Admin-Key"})
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "bot_id":      config.GhWxid,
        "status":      "running",
        "uptime":      time.Now().Unix(),
        "login_status": isLoggedIn,
        "features":    []string{"complete_api", "wechat_protocol", "auth_management"},
    })
}

func handleWebSocket(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "text/plain")
    fmt.Fprintf(w, "WebSocket endpoint for %s (Bot 3 - Complete API)\n", config.GhWxid)
    fmt.Fprintf(w, "Port: %s\n", config.Port)
    fmt.Fprintf(w, "Login Status: %v\n", isLoggedIn)
    fmt.Fprintf(w, "Status: Ready for WebSocket upgrade\n")
}

func handleGetLoginQrCodeNew(w http.ResponseWriter, r *http.Request) {
    if r.Method != "POST" {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    w.Header().Set("Content-Type", "application/json")

    log.Printf("收到获取登录二维码请求")

    // 生成真实的微信二维码
    qrCodeData := generateRealQRCode()

    // 基于原始程序的真实响应格式
    response := map[string]interface{}{
        "Code":    200,
        "Message": "二维码生成成功",
        "Data": map[string]interface{}{
            "qrCode":   qrCodeData,
            "loginUrl": fmt.Sprintf("http://%s:%s/qrcode", config.Host, config.Port),
            "uuid":     wechatSession.UUID,
            "url":      qrCodeData,
            "qr_code":  qrCodeData,
            "qr_url":   qrCodeData,
            "login_url": qrCodeData,
        },
        // 根级别字段 - 覆盖所有可能的字段名
        "url":       qrCodeData,
        "qrCode":    qrCodeData,
        "qr_code":   qrCodeData,
        "qr_url":    qrCodeData,
        "login_url": qrCodeData,
        "qrcode":    qrCodeData,
        "qrcode_url": qrCodeData,
        "login_qr_code": qrCodeData,
        "wx_qr_code": qrCodeData,
        "qr_code_url": qrCodeData,
        // 可能的原始API格式
        "result": map[string]interface{}{
            "url":     qrCodeData,
            "qrCode":  qrCodeData,
            "qr_code": qrCodeData,
        },
        // 可能的嵌套路径
        "data": map[string]interface{}{
            "qr_code": qrCodeData,
            "url": qrCodeData,
            "qrCode": qrCodeData,
        },
        "response": map[string]interface{}{
            "qr_code": qrCodeData,
            "url": qrCodeData,
            "qrCode": qrCodeData,
        },
        // 尝试原始微信API可能使用的字段
        "qr": qrCodeData,
        "code": qrCodeData,
        "login": qrCodeData,
        "wx_url": qrCodeData,
        "weixin_url": qrCodeData,
        // 可能的数组格式
        "urls": []string{qrCodeData},
        "qr_codes": []string{qrCodeData},
    }

    log.Printf("返回二维码响应，UUID: %s, URL: %s", wechatSession.UUID, qrCodeData)
    json.NewEncoder(w).Encode(response)
}

func handleGetQrCodeUrl(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "text/plain")

    log.Printf("收到调试接口请求：直接获取二维码URL")

    // 直接返回真实的微信二维码URL字符串
    qrCodeUrl := generateRealQRCode()
    log.Printf("调试接口返回二维码URL: %s", qrCodeUrl)
    fmt.Fprint(w, qrCodeUrl)
}

func handleDefault(w http.ResponseWriter, r *http.Request) {
    // 记录未知请求
    log.Printf("Unknown request: %s %s", r.Method, r.URL.Path)

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "error":   "API not found",
        "path":    r.URL.Path,
        "method":  r.Method,
        "bot_id":  config.GhWxid,
        "message": "This API endpoint is not implemented yet",
    })
}

// 生成设备ID - 基于原始程序的算法
func generateDeviceID() string {
    rand.Seed(time.Now().UnixNano())
    return fmt.Sprintf("e%d", rand.Int63n(1000000000000000))
}

// 获取真实的微信UUID
func getRealWeChatUUID() (string, error) {
    // 使用与手动测试完全相同的URL和参数
    timestamp := time.Now().UnixMilli()
    apiUrl := fmt.Sprintf("https://login.wx.qq.com/jslogin?appid=%s&fun=new&lang=zh_CN&_=%d", APP_ID, timestamp)

    log.Printf("正在请求真实微信UUID: %s", apiUrl)

    req, err := http.NewRequest("GET", apiUrl, nil)
    if err != nil {
        log.Printf("创建UUID请求失败: %v", err)
        return "", err
    }

    // 使用与curl测试相同的User-Agent
    req.Header.Set("User-Agent", "curl/7.81.0")
    req.Header.Set("Accept", "*/*")
    req.Header.Set("Host", "login.wx.qq.com")

    // 创建新的HTTP客户端，使用更宽松的配置
    client := &http.Client{
        Timeout: 30 * time.Second,
    }

    resp, err := client.Do(req)
    if err != nil {
        log.Printf("UUID请求失败: %v", err)
        return "", err
    }
    defer resp.Body.Close()

    log.Printf("微信API响应状态: %s", resp.Status)

    body, err := io.ReadAll(resp.Body)
    if err != nil {
        log.Printf("读取UUID响应失败: %v", err)
        return "", err
    }

    bodyStr := string(body)
    log.Printf("微信API响应内容: %s", bodyStr)

    // 解析UUID - 使用与手动测试相同的正则表达式
    re := regexp.MustCompile(`window\.QRLogin\.code = 200; window\.QRLogin\.uuid = "([^"]+)";`)
    matches := re.FindStringSubmatch(bodyStr)
    if len(matches) < 2 {
        log.Printf("解析UUID失败，响应: %s", bodyStr)
        return "", fmt.Errorf("failed to parse UUID from response")
    }

    uuid := matches[1]
    wechatSession.UUID = uuid
    log.Printf("✅ 成功获取真实微信UUID: %s", uuid)
    return uuid, nil
}

// 生成真实的微信二维码URL
func generateRealQRCode() string {
    // 首先尝试获取真实的UUID
    uuid, err := getRealWeChatUUID()
    if err != nil {
        log.Printf("获取真实UUID失败，使用备用方案: %v", err)
        // 备用方案：生成模拟UUID
        uuid = fmt.Sprintf("uuid_%d", time.Now().Unix())
        wechatSession.UUID = uuid
    }

    // 返回真实的微信二维码URL
    qrUrl := fmt.Sprintf("%s/qrcode/%s", WECHAT_LOGIN_URL, uuid)
    log.Printf("生成二维码URL: %s", qrUrl)
    return qrUrl
}

// 兼容性函数 - 保持与原代码的兼容性
func generateQRCode() string {
    return generateRealQRCode()
}

func generateAuthKeys(count, days int) []string {
    keys := make([]string, count)
    for i := 0; i < count; i++ {
        keys[i] = fmt.Sprintf("auth_%d_%d_%d", time.Now().Unix(), days, rand.Intn(999999))
    }
    return keys
}

// 模拟登录成功（可以通过API调用触发）
func simulateLogin() {
    isLoggedIn = true
    currentWxId = fmt.Sprintf("wx_%s_%d", config.GhWxid, time.Now().Unix())
    currentNickName = fmt.Sprintf("Bot3_%d", time.Now().Unix()%1000)
    log.Printf("Simulated login: WxId=%s, NickName=%s", currentWxId, currentNickName)
}
