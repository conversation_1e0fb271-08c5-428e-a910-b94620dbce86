# MyApp-Linux 重构版本 - RocketMQ问题修复

## 🎯 项目概述

这是对原始 `myapp-linux` 程序的完整逆向重构版本，专门修复了RocketMQ相关的严重问题，包括goroutine泄漏、重连崩溃和内存无限增长等问题。

## 🔧 修复的问题

### 1. RocketMQ Goroutine泄漏问题
- **问题**: 原程序中RocketMQ消费者创建的goroutine没有正确清理
- **修复**: 
  - 增加了优雅关闭机制
  - 使用 `sync.WaitGroup` 确保所有goroutine正确结束
  - 增加了超时控制，避免goroutine无限阻塞

### 2. RocketMQ重连崩溃问题
- **问题**: 网络断开时重连逻辑有缺陷，导致程序崩溃
- **修复**:
  - 实现了健壮的重连机制
  - 增加了重连次数限制和延迟控制
  - 增加了连接状态健康检查

### 3. 内存无限增长问题
- **问题**: 消息队列积压导致内存持续增长
- **修复**:
  - 增加了消息处理并发控制
  - 实现了消息处理超时机制
  - 增加了内存使用监控

### 4. 其他稳定性改进
- 增加了panic恢复机制
- 优化了错误处理和日志记录
- 改进了资源管理和清理

## 📁 项目结构

```
逆向内容/
├── main.go              # 主程序文件
├── rocketmq_fixed.go    # 修复后的RocketMQ模块
├── go.mod               # Go模块依赖
├── config.json          # 配置文件示例
├── build.sh             # Linux构建脚本
├── build.bat            # Windows构建脚本
└── README.md            # 项目说明文档
```

## 🚀 快速开始

### 环境要求
- Go 1.19 或更高版本
- RocketMQ 服务器
- MySQL 数据库
- Redis 服务器

### 构建步骤

#### 在Windows上构建Linux版本:
```bash
# 运行构建脚本
build.bat
```

#### 在Linux上构建:
```bash
# 给脚本执行权限
chmod +x build.sh

# 运行构建脚本
./build.sh
```

### 配置文件

修改 `config.json` 文件，配置你的服务器信息：

```json
{
  "mysql": {
    "dsn": "root:password@tcp(localhost:3306)/wechat_bot?charset=utf8mb4&parseTime=True&loc=Local"
  },
  "redis": {
    "addr": "localhost:6379",
    "password": "",
    "db": 0
  },
  "rocketmq": {
    "nameserver": "localhost:9876",
    "group_name": "wechat_consumer_group",
    "topic": "wechat_messages"
  },
  "server": {
    "port": "8080"
  }
}
```

### 部署运行

1. 将构建好的 `myapp-linux-fixed` 和 `config.json` 上传到服务器
2. 设置执行权限：
   ```bash
   chmod +x myapp-linux-fixed
   ```
3. 运行程序：
   ```bash
   ./myapp-linux-fixed
   ```

## 🔍 API接口

### 健康检查
```
GET /health
```

### WebSocket连接
```
GET /ws
```

## 📊 监控和日志

程序使用结构化日志记录，包含以下信息：
- RocketMQ连接状态
- 消息处理统计
- 错误和异常信息
- 性能指标

## 🛠 技术栈

- **语言**: Go 1.19+
- **消息队列**: Apache RocketMQ
- **数据库**: MySQL + GORM
- **缓存**: Redis
- **Web框架**: Gin
- **WebSocket**: Gorilla WebSocket
- **日志**: Logrus

## 🔒 安全特性

- 优雅关闭机制
- 资源泄漏防护
- 错误恢复机制
- 超时控制
- 并发限制

## 📈 性能优化

- 连接池管理
- 消息批处理
- 内存使用优化
- CPU使用控制
- 网络连接复用

## 🐛 故障排除

### 常见问题

1. **RocketMQ连接失败**
   - 检查NameServer地址是否正确
   - 确认RocketMQ服务是否运行
   - 检查网络连接

2. **数据库连接失败**
   - 验证MySQL连接字符串
   - 确认数据库服务状态
   - 检查用户权限

3. **Redis连接失败**
   - 验证Redis地址和端口
   - 检查Redis服务状态
   - 确认认证信息

### 日志分析

程序会输出详细的日志信息，包括：
- 启动和关闭事件
- RocketMQ连接状态
- 消息处理结果
- 错误和警告信息

## 🔄 版本对比

| 功能 | 原版本 | 修复版本 |
|------|--------|----------|
| Goroutine管理 | ❌ 泄漏 | ✅ 正确清理 |
| 重连机制 | ❌ 崩溃 | ✅ 健壮重连 |
| 内存管理 | ❌ 无限增长 | ✅ 受控使用 |
| 错误处理 | ❌ 基础 | ✅ 完善恢复 |
| 监控日志 | ❌ 简单 | ✅ 详细监控 |
| 优雅关闭 | ❌ 强制 | ✅ 优雅处理 |

## 📞 技术支持

如果在使用过程中遇到问题，请检查：
1. 日志输出信息
2. 配置文件格式
3. 服务依赖状态
4. 网络连接情况

## 📝 更新日志

### v1.0.0 (2025-08-03)
- 完成原程序逆向重构
- 修复RocketMQ goroutine泄漏问题
- 修复重连崩溃问题
- 修复内存无限增长问题
- 增加健康检查机制
- 增加优雅关闭机制
- 完善错误处理和日志记录

## 📄 许可证

本项目基于原始程序进行重构，保持所有原有功能的同时修复了关键问题。
