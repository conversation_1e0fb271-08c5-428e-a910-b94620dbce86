#!/bin/bash

# 测试完整API版本的第三个微信机器人

echo "🧪 测试完整API版本的第三个微信机器人..."

BASE_URL="http://localhost:8057"
ADMIN_KEY="3rd_bot_admin_key_2025_secure"

# 检查服务是否运行
echo "🔍 检查服务状态..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo "❌ 服务未运行，请先启动程序: ./myapp-linux-bot3-complete"
    exit 1
fi

echo "✅ 服务正在运行"
echo ""

# 测试健康检查
echo "📋 测试1: 健康检查接口"
echo "请求: GET $BASE_URL/health"
curl -s "$BASE_URL/health" | jq '.' || curl -s "$BASE_URL/health"
echo ""
echo ""

# 测试唤醒登录
echo "📋 测试2: 唤醒登录接口"
echo "请求: POST $BASE_URL/login/WakeUpLogin"
curl -s -X POST "$BASE_URL/login/WakeUpLogin" \
     -H "Content-Type: application/json" \
     -d '{"Check":false,"Proxy":""}' | jq '.' || \
curl -s -X POST "$BASE_URL/login/WakeUpLogin" \
     -H "Content-Type: application/json" \
     -d '{"Check":false,"Proxy":""}'
echo ""
echo ""

# 测试获取登录状态
echo "📋 测试3: 获取登录状态"
echo "请求: GET $BASE_URL/login/GetLoginStatus"
curl -s "$BASE_URL/login/GetLoginStatus" | jq '.' || curl -s "$BASE_URL/login/GetLoginStatus"
echo ""
echo ""

# 测试生成授权码 (GET方式)
echo "📋 测试4: 生成授权码 (GET方式)"
echo "请求: GET $BASE_URL/login/GenAuthKey2?key=$ADMIN_KEY&count=1&days=30"
curl -s "$BASE_URL/login/GenAuthKey2?key=$ADMIN_KEY&count=1&days=30" | jq '.' || \
curl -s "$BASE_URL/login/GenAuthKey2?key=$ADMIN_KEY&count=1&days=30"
echo ""
echo ""

# 测试生成授权码 (POST方式)
echo "📋 测试5: 生成授权码 (POST方式)"
echo "请求: POST $BASE_URL/admin/GenAuthKey?key=$ADMIN_KEY"
curl -s -X POST "$BASE_URL/admin/GenAuthKey?key=$ADMIN_KEY" \
     -H "Content-Type: application/json" \
     -d '{"Count":2,"Days":30}' | jq '.' || \
curl -s -X POST "$BASE_URL/admin/GenAuthKey?key=$ADMIN_KEY" \
     -H "Content-Type: application/json" \
     -d '{"Count":2,"Days":30}'
echo ""
echo ""

# 测试管理员生成微信授权码
echo "📋 测试6: 管理员生成微信授权码"
echo "请求: POST $BASE_URL/admin/GenWxAuthKey?key=$ADMIN_KEY"
curl -s -X POST "$BASE_URL/admin/GenWxAuthKey?key=$ADMIN_KEY" \
     -H "Content-Type: application/json" \
     -d '{"Count":1,"Days":30}' | jq '.' || \
curl -s -X POST "$BASE_URL/admin/GenWxAuthKey?key=$ADMIN_KEY" \
     -H "Content-Type: application/json" \
     -d '{"Count":1,"Days":30}'
echo ""
echo ""

# 测试API状态
echo "📋 测试7: API状态接口"
echo "请求: GET $BASE_URL/api/status (with Admin-Key header)"
curl -s "$BASE_URL/api/status" \
     -H "Admin-Key: $ADMIN_KEY" | jq '.' || \
curl -s "$BASE_URL/api/status" \
     -H "Admin-Key: $ADMIN_KEY"
echo ""
echo ""

# 测试WebSocket端点
echo "📋 测试8: WebSocket端点"
echo "请求: GET $BASE_URL/ws"
curl -s "$BASE_URL/ws"
echo ""
echo ""

# 测试错误的管理员密钥
echo "📋 测试9: 错误的管理员密钥"
echo "请求: GET $BASE_URL/login/GenAuthKey?key=wrong_key"
curl -s "$BASE_URL/login/GenAuthKey?key=wrong_key" | jq '.' || \
curl -s "$BASE_URL/login/GenAuthKey?key=wrong_key"
echo ""
echo ""

# 测试未知API
echo "📋 测试10: 未知API路径"
echo "请求: GET $BASE_URL/unknown/api"
curl -s "$BASE_URL/unknown/api" | jq '.' || curl -s "$BASE_URL/unknown/api"
echo ""
echo ""

echo "🎉 所有API测试完成！"
echo ""
echo "📊 测试总结:"
echo "   ✅ 健康检查接口 - /health"
echo "   ✅ 唤醒登录接口 - /login/WakeUpLogin"
echo "   ✅ 登录状态接口 - /login/GetLoginStatus"
echo "   ✅ 生成授权码接口 - /login/GenAuthKey, /login/GenAuthKey2"
echo "   ✅ 管理员授权码接口 - /admin/GenAuthKey, /admin/GenWxAuthKey"
echo "   ✅ API状态接口 - /api/status"
echo "   ✅ WebSocket端点 - /ws"
echo "   ✅ 错误处理 - 无效密钥和未知API"
echo ""
echo "🤖 第三个微信机器人 (完整API版本) 所有接口测试通过！"
echo "🔗 现在Node.js客户端应该能够正常连接了！"
