package main

import (
    "bytes"
    "crypto/md5"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "net/url"
    "regexp"
    "strconv"
    "strings"
    "time"
    "math/rand"
)

// 微信协议相关常量
const (
    // 微信Web版API端点
    WECHAT_LOGIN_URL     = "https://login.wx.qq.com"
    WECHAT_WEB_URL       = "https://wx.qq.com"
    WECHAT_WEBPUSH_URL   = "https://webpush.wx.qq.com"
    WECHAT_FILE_URL      = "https://file.wx.qq.com"
    
    // API路径
    API_JSLOGIN          = "/jslogin"
    API_LOGIN            = "/cgi-bin/mmwebwx-bin/login"
    API_WEBWXINIT        = "/cgi-bin/mmwebwx-bin/webwxinit"
    API_WEBWXGETCONTACT  = "/cgi-bin/mmwebwx-bin/webwxgetcontact"
    API_WEBWXSYNC        = "/cgi-bin/mmwebwx-bin/webwxsync"
    API_WEBWXSENDMSG     = "/cgi-bin/mmwebwx-bin/webwxsendmsg"
    
    // 应用信息
    APP_ID = "wx782c26e4c19acffb"
    USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
)

// 微信协议客户端
type WeChatClient struct {
    httpClient   *http.Client
    cookies      []*http.Cookie
    uuid         string
    redirectUri  string
    skey         string
    wxsid        string
    wxuin        string
    passTicket   string
    deviceID     string
    baseRequest  *BaseRequest
    user         *User
    syncKey      *SyncKey
}

// 基础请求结构
type BaseRequest struct {
    Uin      string `json:"Uin"`
    Sid      string `json:"Sid"`
    Skey     string `json:"Skey"`
    DeviceID string `json:"DeviceID"`
}

// 用户信息
type User struct {
    Uin      int64  `json:"Uin"`
    UserName string `json:"UserName"`
    NickName string `json:"NickName"`
    HeadImg  string `json:"HeadImgUrl"`
}

// 同步密钥
type SyncKey struct {
    Count int `json:"Count"`
    List  []struct {
        Key int `json:"Key"`
        Val int `json:"Val"`
    } `json:"List"`
}

// 登录响应
type LoginResponse struct {
    Code int    `json:"code"`
    Msg  string `json:"msg"`
    Data struct {
        QRCode   string `json:"qrcode"`
        UUID     string `json:"uuid"`
        LoginUrl string `json:"login_url"`
    } `json:"data"`
}

// 初始化微信客户端
func NewWeChatClient() *WeChatClient {
    client := &WeChatClient{
        httpClient: &http.Client{
            Timeout: 30 * time.Second,
        },
        deviceID: generateDeviceID(),
    }
    return client
}

// 生成设备ID
func generateDeviceID() string {
    rand.Seed(time.Now().UnixNano())
    return fmt.Sprintf("e%d", rand.Int63n(1000000000000000))
}

// 获取UUID
func (wc *WeChatClient) GetUUID() (string, error) {
    apiUrl := fmt.Sprintf("%s%s", WECHAT_LOGIN_URL, API_JSLOGIN)
    
    params := url.Values{}
    params.Set("appid", APP_ID)
    params.Set("fun", "new")
    params.Set("lang", "zh_CN")
    params.Set("_", strconv.FormatInt(time.Now().UnixMilli(), 10))
    
    req, err := http.NewRequest("GET", apiUrl+"?"+params.Encode(), nil)
    if err != nil {
        return "", err
    }
    
    req.Header.Set("User-Agent", USER_AGENT)
    req.Header.Set("Referer", "https://wx.qq.com/")
    
    resp, err := wc.httpClient.Do(req)
    if err != nil {
        return "", err
    }
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return "", err
    }
    
    // 解析UUID
    re := regexp.MustCompile(`window.QRLogin.code = 200; window.QRLogin.uuid = "([^"]+)"`)
    matches := re.FindStringSubmatch(string(body))
    if len(matches) < 2 {
        return "", fmt.Errorf("failed to get UUID")
    }
    
    wc.uuid = matches[1]
    return wc.uuid, nil
}

// 获取二维码
func (wc *WeChatClient) GetQRCode() (string, error) {
    if wc.uuid == "" {
        _, err := wc.GetUUID()
        if err != nil {
            return "", err
        }
    }
    
    qrUrl := fmt.Sprintf("%s/qrcode/%s", WECHAT_LOGIN_URL, wc.uuid)
    return qrUrl, nil
}

// 检查登录状态
func (wc *WeChatClient) CheckLogin() (int, string, error) {
    if wc.uuid == "" {
        return 0, "", fmt.Errorf("UUID not found")
    }
    
    apiUrl := fmt.Sprintf("%s/cgi-bin/mmwebwx-bin/login", WECHAT_LOGIN_URL)
    
    params := url.Values{}
    params.Set("loginicon", "true")
    params.Set("uuid", wc.uuid)
    params.Set("tip", "0")
    params.Set("r", strconv.FormatInt(^time.Now().UnixMilli(), 10))
    params.Set("_", strconv.FormatInt(time.Now().UnixMilli(), 10))
    
    req, err := http.NewRequest("GET", apiUrl+"?"+params.Encode(), nil)
    if err != nil {
        return 0, "", err
    }
    
    req.Header.Set("User-Agent", USER_AGENT)
    req.Header.Set("Referer", "https://wx.qq.com/")
    
    resp, err := wc.httpClient.Do(req)
    if err != nil {
        return 0, "", err
    }
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return 0, "", err
    }
    
    bodyStr := string(body)
    
    // 解析登录状态
    if strings.Contains(bodyStr, "window.code=200") {
        // 登录成功，提取redirect_uri
        re := regexp.MustCompile(`window.redirect_uri="([^"]+)"`)
        matches := re.FindStringSubmatch(bodyStr)
        if len(matches) >= 2 {
            wc.redirectUri = matches[1]
            return 200, "登录成功", nil
        }
    } else if strings.Contains(bodyStr, "window.code=201") {
        return 201, "已扫码，等待确认", nil
    } else if strings.Contains(bodyStr, "window.code=408") {
        return 408, "登录超时", nil
    }
    
    return 400, "等待扫码", nil
}

// 执行登录
func (wc *WeChatClient) DoLogin() error {
    if wc.redirectUri == "" {
        return fmt.Errorf("redirect URI not found")
    }
    
    req, err := http.NewRequest("GET", wc.redirectUri, nil)
    if err != nil {
        return err
    }
    
    req.Header.Set("User-Agent", USER_AGENT)
    req.Header.Set("Referer", "https://wx.qq.com/")
    
    resp, err := wc.httpClient.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    // 保存cookies
    wc.cookies = resp.Cookies()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return err
    }
    
    bodyStr := string(body)
    
    // 解析登录信息
    re := regexp.MustCompile(`<skey>([^<]+)</skey>`)
    matches := re.FindStringSubmatch(bodyStr)
    if len(matches) >= 2 {
        wc.skey = matches[1]
    }
    
    re = regexp.MustCompile(`<wxsid>([^<]+)</wxsid>`)
    matches = re.FindStringSubmatch(bodyStr)
    if len(matches) >= 2 {
        wc.wxsid = matches[1]
    }
    
    re = regexp.MustCompile(`<wxuin>([^<]+)</wxuin>`)
    matches = re.FindStringSubmatch(bodyStr)
    if len(matches) >= 2 {
        wc.wxuin = matches[1]
    }
    
    re = regexp.MustCompile(`<pass_ticket>([^<]+)</pass_ticket>`)
    matches = re.FindStringSubmatch(bodyStr)
    if len(matches) >= 2 {
        wc.passTicket = matches[1]
    }
    
    // 设置基础请求
    wc.baseRequest = &BaseRequest{
        Uin:      wc.wxuin,
        Sid:      wc.wxsid,
        Skey:     wc.skey,
        DeviceID: wc.deviceID,
    }
    
    return nil
}

// 初始化微信
func (wc *WeChatClient) WebWxInit() error {
    apiUrl := fmt.Sprintf("%s%s", WECHAT_WEB_URL, API_WEBWXINIT)
    
    params := url.Values{}
    params.Set("pass_ticket", wc.passTicket)
    params.Set("skey", wc.skey)
    params.Set("r", strconv.FormatInt(time.Now().UnixMilli(), 10))
    
    requestData := map[string]interface{}{
        "BaseRequest": wc.baseRequest,
    }
    
    jsonData, err := json.Marshal(requestData)
    if err != nil {
        return err
    }
    
    req, err := http.NewRequest("POST", apiUrl+"?"+params.Encode(), bytes.NewBuffer(jsonData))
    if err != nil {
        return err
    }
    
    req.Header.Set("Content-Type", "application/json; charset=UTF-8")
    req.Header.Set("User-Agent", USER_AGENT)
    req.Header.Set("Referer", "https://wx.qq.com/")
    
    // 添加cookies
    for _, cookie := range wc.cookies {
        req.AddCookie(cookie)
    }
    
    resp, err := wc.httpClient.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return err
    }
    
    var initResp struct {
        BaseResponse struct {
            Ret int    `json:"Ret"`
            ErrMsg string `json:"ErrMsg"`
        } `json:"BaseResponse"`
        User    User    `json:"User"`
        SyncKey SyncKey `json:"SyncKey"`
    }
    
    err = json.Unmarshal(body, &initResp)
    if err != nil {
        return err
    }
    
    if initResp.BaseResponse.Ret != 0 {
        return fmt.Errorf("init failed: %s", initResp.BaseResponse.ErrMsg)
    }
    
    wc.user = &initResp.User
    wc.syncKey = &initResp.SyncKey
    
    return nil
}

// 生成MD5哈希
func md5Hash(text string) string {
    hash := md5.Sum([]byte(text))
    return fmt.Sprintf("%x", hash)
}

// 获取当前时间戳（毫秒）
func getCurrentTimestamp() int64 {
    return time.Now().UnixMilli()
}
