/**
 * 集成Go内存监控的统一启动脚本
 * 在原有start-unified.js基础上集成Go程序内存监控功能
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const GoMemoryMonitor = require('./go-memory-monitor');

// 导入登录状态检查功能
const { checkLoginStatus, checkWebSocketConnection } = require('./tools/check-login-status');

// 获取当前 node 可执行文件的完整路径
const getNodePath = () => {
  return process.execPath || 'node';
};

class UnifiedStarterWithGoMonitor {
  constructor() {
    this.isRunning = false;
    this.currentProcess = null;
    this.goMonitor = null;
    
    // Go监控配置
    this.goMonitorConfig = {
      // 自动检测Go程序路径
      goProgram: this.detectGoProgram(),
      // 监控间隔：30秒
      monitorInterval: 30000,
      // 内存阈值
      memoryWarningThreshold: 500,  // 500MB警告
      memoryRestartThreshold: 1000, // 1GB重启
      // 重启保护
      minRestartInterval: 300000,   // 5分钟最小重启间隔
      maxRestartCount: 10,          // 1小时内最大重启次数
      // 优雅关闭超时
      gracefulShutdownTimeout: 10000,
      // 日志配置
      logFile: 'logs/go-memory-monitor.log',
      enableConsoleLog: true
    };
  }

  /**
   * 自动检测Go程序路径
   */
  detectGoProgram() {
    const possiblePaths = [
      './逆向内容/myapp-linux',
      './逆向内容/myapp-linux-fixed',
      './逆向内容/myapp-linux-bot3',
      './逆向内容/myapp-linux-bot3-complete',
      './逆向内容/myapp-linux-bot3-real'
    ];

    for (const programPath of possiblePaths) {
      if (fs.existsSync(programPath)) {
        console.log(`✅ 检测到Go程序: ${programPath}`);
        return programPath;
      }
    }

    console.log('⚠️ 未找到Go程序，Go内存监控将被禁用');
    return null;
  }

  /**
   * 主启动流程
   */
  async start() {
    console.log('🚀 统一Bot启动器 (集成Go内存监控)');
    console.log('='.repeat(60));
    
    try {
      // 0. 启动Go程序内存监控（如果可用）
      if (this.goMonitorConfig.goProgram) {
        console.log('📊 步骤0: 启动Go程序内存监控...');
        await this.startGoMemoryMonitor();
        console.log('✅ Go程序内存监控已启动\n');
      } else {
        console.log('⚠️ 跳过Go程序内存监控（未找到Go程序）\n');
      }
      
      // 1. 检查登录状态
      console.log('📋 步骤1: 检查当前登录状态...');
      const loginStatus = await this.checkCurrentLoginStatus();
      
      if (loginStatus) {
        // 登录状态正常，直接启动优化版
        console.log('✅ 登录状态正常，直接启动优化版Bot...\n');
        await this.startOptimizedBot();
      } else {
        // 需要重新登录
        console.log('❌ 需要重新登录，启动登录流程...\n');
        await this.performLogin();
        
        // 登录完成后启动优化版
        console.log('✅ 登录完成，启动优化版Bot...\n');
        await this.startOptimizedBot();
      }
      
    } catch (error) {
      console.error('❌ 启动失败:', error.message);
      
      // 清理Go监控器
      if (this.goMonitor) {
        await this.goMonitor.shutdown();
      }
      
      process.exit(1);
    }
  }

  /**
   * 启动Go程序内存监控
   */
  async startGoMemoryMonitor() {
    try {
      this.goMonitor = new GoMemoryMonitor(this.goMonitorConfig);
      
      // 设置事件监听
      this.setupGoMonitorEvents();
      
      // 启动监控
      await this.goMonitor.start();
      
      console.log('📊 Go程序内存监控配置:');
      console.log(`   - 程序路径: ${this.goMonitorConfig.goProgram}`);
      console.log(`   - 监控间隔: ${this.goMonitorConfig.monitorInterval / 1000}秒`);
      console.log(`   - 警告阈值: ${this.goMonitorConfig.memoryWarningThreshold}MB`);
      console.log(`   - 重启阈值: ${this.goMonitorConfig.memoryRestartThreshold}MB`);
      
    } catch (error) {
      console.error(`❌ Go程序内存监控启动失败: ${error.message}`);
      console.log('⚠️ 继续启动Node.js Bot，但Go程序内存监控不可用');
      this.goMonitor = null;
    }
  }

  /**
   * 设置Go监控器事件监听
   */
  setupGoMonitorEvents() {
    if (!this.goMonitor) return;

    this.goMonitor.on('memoryWarning', (data) => {
      console.log(`⚠️ [Go监控] 内存警告: ${data.memoryMB}MB`);
    });

    this.goMonitor.on('restartStarted', (data) => {
      console.log(`🔄 [Go监控] 开始重启Go程序，原因: ${data.reason}`);
    });

    this.goMonitor.on('restartCompleted', (data) => {
      console.log(`✅ [Go监控] Go程序重启成功 (第${data.count}次)`);
    });

    this.goMonitor.on('restartFailed', (data) => {
      console.log(`❌ [Go监控] Go程序重启失败: ${data.error.message}`);
    });

    this.goMonitor.on('tooManyRestarts', (data) => {
      console.log(`🚨 [Go监控] 重启次数过多 (${data.count}次)，请检查Go程序`);
    });

    this.goMonitor.on('error', (error) => {
      console.error(`❌ [Go监控] 监控器错误: ${error.message}`);
    });
  }

  /**
   * 检查当前登录状态
   */
  async checkCurrentLoginStatus() {
    try {
      // 检查登录状态文件
      const statusResult = await checkLoginStatus();
      if (!statusResult.success) {
        console.log(`❌ 登录状态检查失败: ${statusResult.message}`);
        return false;
      }

      // 检查WebSocket连接
      const wsResult = await checkWebSocketConnection();
      if (!wsResult.success) {
        console.log(`❌ WebSocket连接检查失败: ${wsResult.message}`);
        return false;
      }

      console.log('✅ 登录状态和WebSocket连接正常');
      return true;

    } catch (error) {
      console.log(`❌ 状态检查异常: ${error.message}`);
      return false;
    }
  }

  /**
   * 执行登录流程
   */
  async performLogin() {
    console.log('🔐 启动登录流程...');
    console.log('请在弹出的窗口中完成微信登录');
    console.log('登录完成后程序将自动继续...');
    console.log('='.repeat(50));

    return new Promise((resolve, reject) => {
      // 启动原始bot.js进行登录
      const nodePath = getNodePath();
      console.log(`🔧 使用 Node.js 路径: ${nodePath}`);
      const loginProcess = spawn(nodePath, ['bot.js'], {
        stdio: 'inherit',
        cwd: process.cwd()
      });

      this.currentProcess = loginProcess;

      // 监听登录过程
      let loginTimeout = setTimeout(() => {
        console.log('\n⏰ 登录超时，请重新尝试');
        loginProcess.kill();
        reject(new Error('登录超时'));
      }, 300000); // 5分钟超时

      loginProcess.on('exit', (code) => {
        clearTimeout(loginTimeout);
        this.currentProcess = null;

        if (code === 0) {
          console.log('\n✅ 登录流程完成');
          resolve();
        } else {
          console.log(`\n❌ 登录流程异常退出，代码: ${code}`);
          reject(new Error(`登录失败，退出代码: ${code}`));
        }
      });

      loginProcess.on('error', (error) => {
        clearTimeout(loginTimeout);
        this.currentProcess = null;
        reject(new Error(`登录进程启动失败: ${error.message}`));
      });

      // 处理中断信号
      process.on('SIGINT', () => {
        if (loginProcess && !loginProcess.killed) {
          console.log('\n🛑 中断登录流程...');
          loginProcess.kill();
          clearTimeout(loginTimeout);
          reject(new Error('用户中断登录'));
        }
      });
    });
  }

  /**
   * 启动优化版Bot
   */
  async startOptimizedBot() {
    console.log('🎯 启动优化版Bot...');
    console.log('='.repeat(50));

    return new Promise((resolve, reject) => {
      // 启动优化版Bot（V2版本）
      const nodePath = getNodePath();
      console.log(`🔧 使用 Node.js 路径: ${nodePath}`);
      const args = ['--expose-gc', 'start-optimized.js'];
      const botProcess = spawn(nodePath, args, {
        stdio: 'inherit',
        cwd: process.cwd()
      });

      this.currentProcess = botProcess;
      this.isRunning = true;

      console.log('✅ 优化版Bot已启动');
      if (this.goMonitor) {
        console.log('📊 Go程序内存监控正在运行');
      }
      console.log('📝 按 Ctrl+C 停止所有服务');
      console.log('='.repeat(50));

      botProcess.on('exit', (code) => {
        this.isRunning = false;
        console.log(`\n📴 Bot已停止，退出代码: ${code}`);
        resolve();
      });

      botProcess.on('error', (error) => {
        this.isRunning = false;
        reject(new Error(`Bot启动失败: ${error.message}`));
      });

      // 处理进程信号
      process.on('SIGINT', () => {
        console.log('\n🛑 收到停止信号，正在关闭所有服务...');
        this.shutdown();
      });

      process.on('SIGTERM', () => {
        console.log('\n🛑 收到终止信号，正在关闭所有服务...');
        this.shutdown();
      });
    });
  }

  /**
   * 停止当前运行的进程
   */
  stop() {
    if (this.currentProcess && !this.currentProcess.killed) {
      console.log('🛑 停止Node.js进程...');
      this.currentProcess.kill();
      this.isRunning = false;
    }
  }

  /**
   * 完整关闭流程
   */
  async shutdown() {
    console.log('🔄 开始关闭流程...');
    
    // 停止Node.js进程
    this.stop();
    
    // 停止Go监控器
    if (this.goMonitor) {
      console.log('📊 关闭Go程序内存监控...');
      try {
        await this.goMonitor.shutdown();
        console.log('✅ Go程序内存监控已关闭');
      } catch (error) {
        console.error(`❌ 关闭Go监控器时出错: ${error.message}`);
      }
    }
    
    console.log('✅ 所有服务已关闭');
    process.exit(0);
  }

  /**
   * 获取运行状态
   */
  getStatus() {
    return {
      nodeJsRunning: this.isRunning,
      currentProcess: this.currentProcess ? this.currentProcess.pid : null,
      goMonitorRunning: this.goMonitor ? this.goMonitor.getStatus().isMonitoring : false,
      goMonitorStatus: this.goMonitor ? this.goMonitor.getStatus() : null
    };
  }

  /**
   * 手动重启Go程序
   */
  async restartGoProgram() {
    if (!this.goMonitor) {
      throw new Error('Go程序监控器未启动');
    }

    console.log('🔄 手动触发Go程序重启...');
    const success = await this.goMonitor.manualRestart();
    
    if (success) {
      console.log('✅ Go程序手动重启成功');
    } else {
      console.log('❌ Go程序手动重启失败');
    }

    return success;
  }
}

// 如果直接运行此文件，启动应用
if (require.main === module) {
  const starter = new UnifiedStarterWithGoMonitor();
  
  // 显示启动信息
  console.log('🤖 统一Bot启动器 (集成Go内存监控版本)');
  console.log('功能: 自动登录检测 + 优化版Bot + Go程序内存监控');
  console.log('');

  // 启动应用
  starter.start().catch(error => {
    console.error(`启动失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = UnifiedStarterWithGoMonitor;
