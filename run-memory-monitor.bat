@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM Go程序内存监控器启动脚本 (Windows版本)
REM 用于启动和管理Go程序的内存监控

echo 🤖 Go程序内存监控器
echo 用于监控和管理myapp_linux的内存使用
echo 自动重启功能可防止RocketMQ消息队列无限增长
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装，请先安装Node.js
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js已安装: %NODE_VERSION%

REM 检查必要文件是否存在
set "missing_files="
if not exist "go-memory-monitor.js" set "missing_files=!missing_files! go-memory-monitor.js"
if not exist "start-with-memory-monitor.js" set "missing_files=!missing_files! start-with-memory-monitor.js"

if not "!missing_files!"=="" (
    echo ❌ 缺少必要文件:!missing_files!
    pause
    exit /b 1
)

echo ✅ 所有必要文件存在

REM 检查Go程序文件
set "found_program="
set "go_programs=.\逆向内容\myapp-linux.exe .\逆向内容\myapp-linux .\逆向内容\myapp-linux-fixed.exe .\逆向内容\myapp-linux-bot3.exe .\逆向内容\myapp-linux-bot3-complete.exe"

for %%p in (%go_programs%) do (
    if exist "%%p" (
        set "found_program=%%p"
        echo ✅ 检测到Go程序: %%p
        goto :found
    )
)

:found
if "!found_program!"=="" (
    echo ⚠️ 未找到Go程序文件，将使用默认路径
    echo    请确保Go程序文件存在于 .\逆向内容\ 目录中
)

REM 创建日志目录
if not exist "logs" mkdir logs
echo ✅ 日志目录已创建: logs\

REM 检查端口占用（Windows版本）
netstat -an | findstr ":8057 " >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ 端口8057已被占用
    echo 占用情况:
    netstat -an | findstr ":8057 "
    echo.
    echo 监控器将尝试管理现有的Go程序进程
) else (
    echo ✅ 端口8057可用
)

echo.
echo 📋 启动选项:
echo 1. 标准模式启动 (推荐)
echo 2. 测试模式启动 (先运行测试)
echo 3. 调试模式启动 (显示详细日志)
echo 4. 查看配置信息
echo 5. 运行测试套件
echo.

set /p choice="请选择启动模式 (1-5，默认1): "

REM 如果用户直接按回车，设置默认值为1
if "!choice!"=="" set choice=1

if "!choice!"=="1" (
    echo 🚀 启动标准模式...
    echo 按 Ctrl+C 停止监控
    echo.
    node start-with-memory-monitor.js
) else if "!choice!"=="2" (
    echo 🧪 启动测试模式...
    echo 先运行测试套件，然后启动监控器
    echo.
    
    REM 运行测试
    echo 运行测试套件...
    node test-memory-monitor.js
    
    if errorlevel 1 (
        echo ❌ 测试失败，请检查问题后重试
        pause
        exit /b 1
    ) else (
        echo.
        echo ✅ 测试通过，启动监控器...
        echo 按 Ctrl+C 停止监控
        echo.
        node start-with-memory-monitor.js
    )
) else if "!choice!"=="3" (
    echo 🔍 启动调试模式...
    echo 将显示详细的调试信息
    echo 按 Ctrl+C 停止监控
    echo.
    set DEBUG=*
    node start-with-memory-monitor.js
) else if "!choice!"=="4" (
    echo 📋 配置信息:
    echo.
    echo 监控器配置:
    echo   - 监控间隔: 30秒
    echo   - 内存警告阈值: 500MB
    echo   - 内存重启阈值: 1000MB
    echo   - 最小重启间隔: 5分钟
    echo   - 最大重启次数: 10次/小时
    echo   - 优雅关闭超时: 10秒
    echo.
    echo 日志配置:
    echo   - 日志文件: logs\go-memory-monitor.log
    echo   - 测试报告: logs\test-report.json
    echo.
    echo Go程序检测路径:
    for %%p in (%go_programs%) do (
        if exist "%%p" (
            echo   - ✅ %%p
        ) else (
            echo   - ❌ %%p
        )
    )
    echo.
    echo 重新运行脚本以启动监控器
    pause
) else if "!choice!"=="5" (
    echo 🧪 运行测试套件...
    echo.
    node test-memory-monitor.js
    
    if errorlevel 1 (
        echo ❌ 测试失败，请检查问题
        pause
        exit /b 1
    ) else (
        echo.
        echo 🎉 所有测试通过！
        echo 测试报告已保存到: logs\test-report.json
        pause
    )
) else (
    echo ❌ 无效选择，使用默认标准模式...
    echo 按 Ctrl+C 停止监控
    echo.
    node start-with-memory-monitor.js
)

echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul
