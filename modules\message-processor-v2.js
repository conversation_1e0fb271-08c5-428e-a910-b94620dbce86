/**
 * 消息处理器 V2 - 集成并发管理架构
 * 使用ConcurrencyManager、ResourceManager和StateManager提供高性能消息处理
 */

const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

// 导入监控器模块
let imageMonitor = null;
let textMonitor = null;

// {{ AURA-X: Fix - 修复图片监控器导入路径. Approval: 寸止(ID:1753081807). }}
try {
  // 尝试从services目录导入图片监控器
  imageMonitor = require('../services/image-message-monitor');
  console.log('成功导入图片监控器模块(services路径)');
} catch (servicesError) {
  console.log(`从services路径导入图片监控器失败: ${servicesError.message}`);

  // 尝试从根目录导入
  try {
    imageMonitor = require('../image-message-monitor');
    console.log('成功导入图片监控器模块(根目录路径)');
  } catch (rootError) {
    console.log(`从根目录导入图片监控器失败: ${rootError.message}，将使用内置图片服务`);
  }
}

try {
  textMonitor = require('../text-message-monitor');
} catch (error) {
  console.log('文本监控器模块不可用');
}

class MessageProcessorV2 extends EventEmitter {
  constructor(authManager, config = {}, serviceLoader) {
    super();
    
    this.authManager = authManager;
    this.serviceLoader = serviceLoader;
    this.config = config;
    
    // {{ AURA-X: Modify - 添加洪水控制器支持和事件监听器管理. Approval: 寸止(ID:1751501600). }}
    // 外部依赖（由WebSocketManager注入）
    this.concurrencyManager = null;
    this.resourceManager = null;
    this.stateManager = null;
    this.messageState = null; // 消息状态命名空间
    this.floodControl = null; // 洪水控制器V2

    // 事件监听器管理
    this.eventListenerIds = new Set(); // 存储通过ResourceManager注册的监听器ID
    
    // 消息处理配置
    this.processingConfig = {
      maxConcurrentMessages: config.maxConcurrentMessages || 5,
      messageTimeout: config.messageTimeout || 60000, // 60秒消息处理超时
      batchSize: config.batchSize || 10, // 批处理大小
      enableBatching: config.enableBatching !== false,
      enablePrioritization: config.enablePrioritization !== false,
      enableRateLimiting: config.enableRateLimiting !== false,
      rateLimitWindow: config.rateLimitWindow || 60000, // 1分钟窗口
      rateLimitMax: config.rateLimitMax || 30 // 每分钟最多30条消息
    };
    
    // 消息队列和缓存
    this.messageQueue = [];
    this.processingMessages = new Map();
    this.messageCache = new Map();

    // {{ EMERGENCY-FIX: 添加队列大小限制 }}
    this.maxQueueSize = config.maxQueueSize || 1000; // 最大队列大小
    this.droppedMessages = 0; // 丢弃消息计数
    this.rateLimitTracker = new Map();
    
    // 性能指标
    this.metrics = {
      totalMessages: 0,
      processedMessages: 0,
      failedMessages: 0,
      batchedMessages: 0,
      averageProcessingTime: 0,
      queueLength: 0,
      cacheHits: 0,
      rateLimitedMessages: 0
    };
    
    // 日志前缀
    this.LOG_PREFIX = {
      TEXT: '[文本处理器V2]',
      IMAGE: '[图片处理器V2]',
      VOICE: '[语音处理器V2]',
      VIDEO: '[视频处理器V2]',
      EMOJI: '[表情处理器V2]',
      APP: '[应用处理器V2]',
      BATCH: '[批处理器V2]'
    };
    
    console.log(`[消息处理器V2] 初始化完成 - 最大并发: ${this.processingConfig.maxConcurrentMessages}`);
  }
  
  /**
   * 注入依赖管理器
   */
  injectDependencies(concurrencyManager, resourceManager, stateManager) {
    this.concurrencyManager = concurrencyManager;
    this.resourceManager = resourceManager;
    this.stateManager = stateManager;
    
    // 创建消息状态命名空间
    this.messageState = this.stateManager.createNamespace('message', {
      isolated: true,
      persistent: false
    });
    
    // 初始化状态
    this.initializeState();
    
    // 注册缓存到内存守护器
    if (this.resourceManager && this.resourceManager.memoryGuard) {
      this.resourceManager.memoryGuard.registerCache('message_cache', this.messageCache, {
        maxSize: 1000,
        maxAge: 300000, // 5分钟
        priority: 'normal'
      });
    }
    
    console.log(`[消息处理器V2] 依赖注入完成`);
  }

  /**
   * {{ AURA-X: Add - 设置洪水控制器. Approval: 寸止(ID:1751501600). }}
   * 设置洪水控制器
   */
  setFloodControl(floodControl) {
    this.floodControl = floodControl;
    console.log(`[消息处理器V2] 洪水控制器已设置`);
  }

  /**
   * {{ AURA-X: Add - 统一事件监听器管理. Approval: 寸止(ID:1751501600). }}
   * 注册事件监听器（通过ResourceManager）
   */
  registerEventListener(emitter, event, listener, options = {}) {
    if (this.resourceManager) {
      const listenerId = this.resourceManager.registerEventListener(emitter, event, listener, {
        priority: 'normal',
        metadata: { component: 'message_processor_v2', event },
        ...options
      });
      this.eventListenerIds.add(listenerId);
      return listenerId;
    } else {
      // 传统方式（向后兼容）
      emitter.on(event, listener);
      return null;
    }
  }

  /**
   * {{ AURA-X: Add - 清理所有事件监听器. Approval: 寸止(ID:1751501600). }}
   * 清理所有注册的事件监听器
   */
  cleanupEventListeners() {
    if (this.resourceManager) {
      for (const listenerId of this.eventListenerIds) {
        this.resourceManager.releaseResource(listenerId);
      }
      this.eventListenerIds.clear();
      console.log(`[消息处理器V2] 已清理所有事件监听器`);
    } else {
      // 传统清理方式
      this.removeAllListeners();
      console.log(`[消息处理器V2] 已清理所有事件监听器（传统方式）`);
    }
  }
  
  /**
   * 初始化状态
   */
  async initializeState() {
    if (!this.messageState) return;
    
    try {
      await this.messageState.set('isProcessing', false);
      await this.messageState.set('queueLength', 0);
      await this.messageState.set('activeProcessors', 0);
      await this.messageState.set('lastProcessedTime', null);
      await this.messageState.set('processingMode', 'normal'); // normal, batch, priority
      
      console.log(`[消息处理器V2] 状态初始化完成`);
    } catch (error) {
      console.error(`[消息处理器V2] 状态初始化失败: ${error.message}`);
    }
  }
  
  /**
   * 处理消息 - 主入口
   * {{ AURA-X: Add - 添加详细的JSDoc类型注释. Approval: 寸止(ID:1751501600). }}
   * @param {Object} message - 消息对象
   * @param {string} message.msg_id - 消息ID
   * @param {number} message.msg_type - 消息类型 (1=文本, 3=图片, 34=语音, 43=视频, 47=表情, 49=应用消息)
   * @param {string} message.content - 消息内容
   * @param {string} message.from_user - 发送者用户名
   * @param {string} message.to_user - 接收者用户名
   * @param {number} message.timestamp - 消息时间戳
   * @param {Object} [message.metadata] - 消息元数据
   * @returns {Promise<ProcessResult>} 处理结果
   * @typedef {Object} ProcessResult
   * @property {boolean} success - 是否处理成功
   * @property {string} [reason] - 失败原因
   * @property {Object} [data] - 处理结果数据
   * @property {number} [processingTime] - 处理耗时(毫秒)
   */
  async processMessage(message) {
    if (!this.concurrencyManager) {
      console.error(`[消息处理器V2] 并发管理器未注入，回退到传统处理`);
      return this.processMessageLegacy(message);
    }

    this.metrics.totalMessages++;

    // {{ AURA-X: Add - 洪水控制检查. Approval: 寸止(ID:1751501600). }}
    // 洪水控制检查
    if (this.floodControl) {
      const allowedMessages = await this.floodControl.checkMessages([message]);
      if (allowedMessages.length === 0) {
        console.warn(`[消息处理器V2] 消息被洪水控制拦截: ${message.msg_id}`);
        return { success: false, reason: 'flood_control' };
      }
    }

    // 检查速率限制
    if (this.processingConfig.enableRateLimiting && !this.checkRateLimit(message)) {
      this.metrics.rateLimitedMessages++;
      console.warn(`[消息处理器V2] 消息被速率限制拦截: ${message.msg_id}`);
      return { success: false, reason: 'rate_limited' };
    }
    
    // 检查缓存
    const cacheKey = this.generateCacheKey(message);
    if (this.messageCache.has(cacheKey)) {
      this.metrics.cacheHits++;
      console.log(`[消息处理器V2] 缓存命中: ${message.msg_id}`);
      // {{ AURA-X: Fix - 确保缓存返回值格式正确. Approval: 寸止(ID:1751501600). }}
      const cachedResult = this.messageCache.get(cacheKey);
      return {
        success: true,
        cached: true,
        originalResult: cachedResult,
        messageId: message.msg_id
      };
    }
    
    // 添加到队列
    const messageWrapper = {
      id: message.msg_id || this.generateMessageId(),
      message,
      priority: this.calculateMessagePriority(message),
      timestamp: Date.now(),
      retries: 0
    };
    
    // {{ EMERGENCY-FIX: 检查队列大小，防止无限增长 }}
    if (this.messageQueue.length >= this.maxQueueSize) {
      // 移除最旧的消息
      const droppedMessage = this.messageQueue.shift();
      this.droppedMessages++;
      console.warn(`[消息处理器V2] 队列已满(${this.maxQueueSize})，丢弃消息: ${droppedMessage.id}`);

      // 每100条丢弃消息记录一次统计
      if (this.droppedMessages % 100 === 0) {
        console.error(`[消息处理器V2] 已丢弃 ${this.droppedMessages} 条消息，请检查系统负载`);
      }
    }

    this.messageQueue.push(messageWrapper);
    // {{ AURA-X: Fix - 添加messageState安全检查. Approval: 寸止(ID:1751501600). }}
    if (this.messageState) {
      await this.messageState.set('queueLength', this.messageQueue.length);
    }
    
    // 决定处理模式
    const processingMode = await this.determineProcessingMode();
    if (this.messageState) {
      await this.messageState.set('processingMode', processingMode);
    }
    
    switch (processingMode) {
      case 'batch':
        return this.processBatch();
      case 'priority':
        return this.processPriority();
      default:
        return this.processNormal(messageWrapper);
    }
  }
  
  /**
   * 检查速率限制
   */
  checkRateLimit(message) {
    if (!this.processingConfig.enableRateLimiting) {
      return true;
    }
    
    const userId = message.from_user || 'unknown';
    const now = Date.now();
    const windowStart = now - this.processingConfig.rateLimitWindow;
    
    if (!this.rateLimitTracker.has(userId)) {
      this.rateLimitTracker.set(userId, []);
    }
    
    const userMessages = this.rateLimitTracker.get(userId);
    
    // 清理过期记录
    const validMessages = userMessages.filter(timestamp => timestamp > windowStart);
    this.rateLimitTracker.set(userId, validMessages);
    
    // 检查是否超过限制
    if (validMessages.length >= this.processingConfig.rateLimitMax) {
      return false;
    }
    
    // 添加当前消息
    validMessages.push(now);
    
    return true;
  }
  
  /**
   * 生成缓存键
   */
  generateCacheKey(message) {
    // {{ AURA-X: Fix - 修复缓存键生成逻辑，确保不同命令有不同的缓存键. Approval: 寸止(ID:1751501600). }}
    const fromUser = message.from_user_name?.str || message.from_user_name || 'unknown';
    const content = this.extractTextContent(message) || message.msg_id;
    const key = `${message.msg_type}_${fromUser}_${content}`;
    return require('crypto').createHash('md5').update(key).digest('hex');
  }
  
  /**
   * 计算消息优先级
   */
  calculateMessagePriority(message) {
    if (!this.processingConfig.enablePrioritization) {
      return 0;
    }
    
    let priority = 0;
    
    // 根据消息类型设置优先级
    switch (message.msg_type) {
      case 1: // 文本消息
        priority = 5;
        break;
      case 3: // 图片消息
        priority = 3;
        break;
      case 34: // 语音消息
        priority = 4;
        break;
      case 43: // 视频消息
        priority = 2;
        break;
      case 47: // 表情消息
        priority = 1;
        break;
      default:
        priority = 0;
    }
    
    // 根据用户类型调整优先级
    if (message.from_user && message.from_user.includes('admin')) {
      priority += 10;
    }
    
    return priority;
  }
  
  /**
   * 确定处理模式
   */
  async determineProcessingMode() {
    const queueLength = this.messageQueue.length;
    const activeProcessors = await this.messageState.get('activeProcessors') || 0;
    
    // 批处理模式：队列积压且启用批处理
    if (this.processingConfig.enableBatching && 
        queueLength >= this.processingConfig.batchSize && 
        activeProcessors < this.processingConfig.maxConcurrentMessages) {
      return 'batch';
    }
    
    // 优先级模式：启用优先级且有高优先级消息
    if (this.processingConfig.enablePrioritization && 
        this.messageQueue.some(msg => msg.priority > 5)) {
      return 'priority';
    }
    
    return 'normal';
  }
  
  /**
   * 正常处理模式
   */
  async processNormal(messageWrapper) {
    // {{ AURA-X: Fix - 修复并发管理器未注入时的处理逻辑. Approval: 寸止(ID:1751501600). }}
    if (this.concurrencyManager) {
      return this.concurrencyManager.execute(async () => {
        return this.processSingleMessage(messageWrapper);
      }, {
        timeout: this.processingConfig.messageTimeout,
        description: `处理消息 ${messageWrapper.id}`,
        category: 'message_processing',
        useWorkerPool: true
      });
    } else {
      // 回退到直接处理
      console.log(`[消息处理器V2] 并发管理器未注入，使用直接处理模式`);
      return this.processSingleMessage(messageWrapper);
    }
  }
  
  /**
   * 批处理模式
   */
  async processBatch() {
    if (this.messageQueue.length < this.processingConfig.batchSize) {
      return { success: false, reason: 'insufficient_batch_size' };
    }

    const batch = this.messageQueue.splice(0, this.processingConfig.batchSize);
    if (this.messageState) {
      await this.messageState.set('queueLength', this.messageQueue.length);
    }

    console.log(`${this.LOG_PREFIX.BATCH} 开始批处理 ${batch.length} 条消息`);

    // {{ AURA-X: Fix - 修复批处理模式的语法错误. Approval: 寸止(ID:1751501600). }}
    if (this.concurrencyManager && this.concurrencyManager.executeBatch) {
      // 使用并发管理器进行批处理
      const results = await this.concurrencyManager.executeBatch(
        batch.map(wrapper => () => this.processSingleMessage(wrapper)),
        {
          timeout: this.processingConfig.messageTimeout,
          batchTimeout: this.processingConfig.messageTimeout * 2,
          maxConcurrency: this.processingConfig.maxConcurrentMessages,
          failFast: false,
          description: `批处理 ${batch.length} 条消息`
        }
      );

      this.metrics.batchedMessages += batch.length;
      const successCount = results.filter(r => r.success).length;
      console.log(`${this.LOG_PREFIX.BATCH} 批处理完成 - 成功: ${successCount}/${batch.length}`);

      return {
        success: true,
        batchResults: results,
        successCount,
        totalCount: batch.length
      };
    } else {
      // 回退到顺序处理
      console.log(`[消息处理器V2] 并发管理器不支持批处理，回退到顺序处理`);
      const results = [];
      for (const wrapper of batch) {
        try {
          const result = await this.processSingleMessage(wrapper);
          results.push(result);
        } catch (error) {
          results.push({ success: false, error: error.message });
        }
      }

      this.metrics.batchedMessages += batch.length;
      const successCount = results.filter(r => r.success).length;
      console.log(`${this.LOG_PREFIX.BATCH} 批处理完成 - 成功: ${successCount}/${batch.length}`);

      return {
        success: true,
        batchResults: results,
        successCount,
        totalCount: batch.length
      };
    }
  }

  /**
   * 优先级处理模式
   */
  async processPriority() {
    // 按优先级排序
    this.messageQueue.sort((a, b) => b.priority - a.priority);
    
    const highPriorityMessage = this.messageQueue.shift();
    if (this.messageState) {
      await this.messageState.set('queueLength', this.messageQueue.length);
    }
    
    console.log(`${this.LOG_PREFIX.TEXT} 优先处理高优先级消息: ${highPriorityMessage.id} (优先级: ${highPriorityMessage.priority})`);
    
    return this.processNormal(highPriorityMessage);
  }
  
  /**
   * 处理单个消息
   */
  async processSingleMessage(messageWrapper) {
    const startTime = Date.now();
    const { message } = messageWrapper;
    
    try {
      if (this.messageState) {
        await this.messageState.addAndGet('activeProcessors', 1);
      }
      this.processingMessages.set(messageWrapper.id, messageWrapper);
      
      console.log(`[消息处理器V2] 开始处理消息: ${messageWrapper.id}, 类型: ${message.msg_type}`);
      
      let result;
      
      // 根据消息类型分发处理
      switch (message.msg_type) {
        case 1: // 文本消息
          result = await this.processTextMessage(message);
          break;
        case 3: // 图片消息
          result = await this.processImageMessage(message);
          break;
        case 34: // 语音消息
          result = await this.processVoiceMessage(message);
          break;
        case 43: // 视频消息
          result = await this.processVideoMessage(message);
          break;
        case 47: // 表情消息
          result = await this.processEmojiMessage(message);
          break;
        case 49: // 应用消息
          result = await this.processAppMessage(message);
          break;
        default:
          result = await this.processUnknownMessage(message);
      }
      
      const processingTime = Date.now() - startTime;
      this.updateProcessingMetrics(processingTime, true);
      
      // 缓存结果
      const cacheKey = this.generateCacheKey(message);
      this.messageCache.set(cacheKey, result);
      
      console.log(`[消息处理器V2] 消息处理完成: ${messageWrapper.id}, 耗时: ${processingTime}ms`);
      
      this.emit('message-processed', {
        messageId: messageWrapper.id,
        messageType: message.msg_type,
        processingTime,
        result
      });
      
      return result;
      
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.updateProcessingMetrics(processingTime, false);
      
      console.error(`[消息处理器V2] 消息处理失败: ${messageWrapper.id} - ${error.message}`);
      
      // 重试逻辑
      if (messageWrapper.retries < 3) {
        messageWrapper.retries++;
        this.messageQueue.unshift(messageWrapper); // 重新加入队列头部
        console.log(`[消息处理器V2] 消息重试: ${messageWrapper.id} (第${messageWrapper.retries}次)`);
      }
      
      this.emit('message-failed', {
        messageId: messageWrapper.id,
        messageType: message.msg_type,
        error: error.message,
        retries: messageWrapper.retries
      });
      
      return { success: false, error: error.message };
      
    } finally {
      if (this.messageState) {
        await this.messageState.addAndGet('activeProcessors', -1);
        await this.messageState.set('lastProcessedTime', Date.now());
      }
      this.processingMessages.delete(messageWrapper.id);
    }
  }
  
  /**
   * 处理文本消息
   */
  async processTextMessage(message) {
    console.log(`${this.LOG_PREFIX.TEXT} 处理文本消息: ${message.msg_id}`);

    // {{ AURA-X: Fix - 修复文本消息处理的并发管理器问题. Approval: 寸止(ID:1751501600). }}
    const processText = async () => {
      // 检查权限
      if (!this.checkPermissions(message)) {
        return { success: false, reason: 'permission_denied' };
      }

      // 提取文本内容
      const content = this.extractTextContent(message);
      if (!content) {
        return { success: false, reason: 'no_content' };
      }

      // {{ AURA-X: Fix - 添加管理员命令处理逻辑. Approval: 寸止(ID:1751501600). }}
      // 检查是否是管理员命令
      if (content.startsWith('/')) {
        console.log(`${this.LOG_PREFIX.TEXT} 检测到管理员命令: ${content}`);

        // 获取管理员命令服务
        const adminCommandService = this.serviceLoader?.getService('adminCommand');
        if (adminCommandService) {
          console.log(`${this.LOG_PREFIX.TEXT} 委托给管理员命令服务处理...`);

          // 构造标准消息格式
          const cmdMessage = {
            Content: content,
            FromUserName: message.from_user_name?.str || message.from_user_name,
            ToUserName: message.to_user_name?.str || message.to_user_name,
            MsgId: message.msg_id
          };

          const commandResult = await adminCommandService.handleCommand(cmdMessage);

          if (commandResult) {
            console.log(`${this.LOG_PREFIX.TEXT} 管理员命令处理成功`);
            return { success: true, processed: true, type: 'command', isCommand: true };
          } else {
            console.log(`${this.LOG_PREFIX.TEXT} 管理员命令处理失败或未找到命令`);
            return { success: false, reason: 'command_not_found' };
          }
        } else {
          console.log(`${this.LOG_PREFIX.TEXT} 管理员命令服务不可用`);
          return { success: false, reason: 'admin_service_unavailable' };
        }
      }

      // 检查是否需要AI回复
      if (this.shouldReplyWithAI(content, message)) {
        return this.generateAIReply(content, message);
      }

      return { success: true, processed: true, type: 'text' };
    };

    // 使用并发管理器执行文本处理（如果可用）
    if (this.concurrencyManager) {
      return this.concurrencyManager.execute(processText, {
        timeout: 30000,
        description: `文本消息处理 ${message.msg_id}`,
        category: 'text_processing'
      });
    } else {
      // 直接处理
      return processText();
    }
  }
  
  /**
   * 处理图片消息
   */
  async processImageMessage(message) {
    console.log(`${this.LOG_PREFIX.IMAGE} 处理图片消息: ${message.msg_id}`);

    // {{ AURA-X: Fix - 修复图片消息处理的并发管理器问题. Approval: 寸止(ID:1751501600). }}
    // {{ AURA-X: Modify - 增强图片处理逻辑和日志输出. Approval: 寸止(ID:1753081807). }}
    const processImage = async () => {
      // 检查权限
      if (!this.checkPermissions(message)) {
        console.log(`${this.LOG_PREFIX.IMAGE} 权限检查失败，跳过处理`);
        return { success: false, reason: 'permission_denied' };
      }

      console.log(`${this.LOG_PREFIX.IMAGE} 权限检查通过，继续处理`);

      // 使用图片监控器处理（如果可用）
      if (imageMonitor) {
        try {
          console.log(`${this.LOG_PREFIX.IMAGE} 尝试使用图片监控器处理消息...`);

          // 检查是否有handleImageMessage方法
          if (typeof imageMonitor.handleImageMessage === 'function') {
            console.log(`${this.LOG_PREFIX.IMAGE} 使用handleImageMessage方法处理`);
            const result = await imageMonitor.handleImageMessage(message, this.serviceLoader);

            if (result && result.success) {
              console.log(`${this.LOG_PREFIX.IMAGE} 图片监控器处理成功: ${JSON.stringify(result)}`);
              return { success: true, result, type: 'image', method: 'monitor' };
            } else {
              console.log(`${this.LOG_PREFIX.IMAGE} 图片监控器处理失败: ${JSON.stringify(result || {})}`);
            }
          }
          // 检查是否有processImage方法
          else if (typeof imageMonitor.processImage === 'function') {
            console.log(`${this.LOG_PREFIX.IMAGE} 使用processImage方法处理`);
            const result = await imageMonitor.processImage(message);

            if (result && result.success) {
              console.log(`${this.LOG_PREFIX.IMAGE} 图片监控器处理成功: ${JSON.stringify(result)}`);
              return { success: true, result, type: 'image', method: 'monitor' };
            } else {
              console.log(`${this.LOG_PREFIX.IMAGE} 图片监控器处理失败: ${JSON.stringify(result || {})}`);
            }
          } else {
            console.log(`${this.LOG_PREFIX.IMAGE} 图片监控器没有可用的处理方法`);
          }
        } catch (error) {
          console.error(`${this.LOG_PREFIX.IMAGE} 图片监控器处理失败: ${error.message}`);
        }
      } else {
        console.log(`${this.LOG_PREFIX.IMAGE} 图片监控器不可用，将使用基本图片处理`);
      }

      // 回退到基本图片处理
      console.log(`${this.LOG_PREFIX.IMAGE} 开始基本图片处理...`);
      return this.processImageBasic(message);
    };

    if (this.concurrencyManager) {
      return this.concurrencyManager.execute(processImage, {
        timeout: 60000, // 图片处理需要更长时间
        description: `图片消息处理 ${message.msg_id}`,
        category: 'image_processing'
      });
    } else {
      return processImage();
    }
  }
  
  /**
   * 处理语音消息
   */
  async processVoiceMessage(message) {
    console.log(`${this.LOG_PREFIX.VOICE} 处理语音消息: ${message.msg_id}`);

    // {{ AURA-X: Fix - 修复语音消息处理的并发管理器问题. Approval: 寸止(ID:1751501600). }}
    const processVoice = async () => {
      if (!this.checkPermissions(message)) {
        return { success: false, reason: 'permission_denied' };
      }

      // 语音处理逻辑
      return { success: true, processed: true, type: 'voice' };
    };

    if (this.concurrencyManager) {
      return this.concurrencyManager.execute(processVoice, {
        timeout: 45000,
        description: `语音消息处理 ${message.msg_id}`,
        category: 'voice_processing'
      });
    } else {
      return processVoice();
    }
  }
  
  /**
   * 处理视频消息
   */
  async processVideoMessage(message) {
    console.log(`${this.LOG_PREFIX.VIDEO} 处理视频消息: ${message.msg_id}`);

    // {{ AURA-X: Fix - 修复视频消息处理的并发管理器问题. Approval: 寸止(ID:1751501600). }}
    const processVideo = async () => {
      if (!this.checkPermissions(message)) {
        return { success: false, reason: 'permission_denied' };
      }

      // 视频处理逻辑
      return { success: true, processed: true, type: 'video' };
    };

    if (this.concurrencyManager) {
      return this.concurrencyManager.execute(processVideo, {
        timeout: 90000, // 视频处理需要更长时间
        description: `视频消息处理 ${message.msg_id}`,
        category: 'video_processing'
      });
    } else {
      return processVideo();
    }
  }
  
  /**
   * 处理表情消息
   */
  async processEmojiMessage(message) {
    console.log(`${this.LOG_PREFIX.EMOJI} 处理表情消息: ${message.msg_id}`);
    
    return { success: true, processed: true, type: 'emoji' };
  }
  
  /**
   * 处理应用消息
   */
  async processAppMessage(message) {
    console.log(`${this.LOG_PREFIX.APP} 处理应用消息: ${message.msg_id}`);

    try {
      // 检查是否为红包消息
      if (this.isRedPacketMessage(message)) {
        console.log(`${this.LOG_PREFIX.APP} 检测到红包消息，调用红包服务处理`);

        // 获取红包服务
        const redPacketService = this.serviceLoader ? this.serviceLoader.getService('redPacket') : null;

        if (redPacketService && redPacketService.isEnabled) {
          try {
            const result = await redPacketService.handleRedPacketMessage(message);
            console.log(`${this.LOG_PREFIX.APP} 红包处理结果:`, result.success ? '成功' : `失败 - ${result.error}`);
            return { success: true, processed: true, type: 'redpacket', result };
          } catch (redPacketError) {
            console.error(`${this.LOG_PREFIX.APP} 红包处理异常:`, redPacketError.message);
            return { success: true, processed: false, type: 'redpacket', error: redPacketError.message };
          }
        } else {
          console.log(`${this.LOG_PREFIX.APP} 红包服务未启用或不可用`);
          return { success: true, processed: false, type: 'redpacket', error: '红包服务不可用' };
        }
      }

      // 处理其他应用消息
      console.log(`${this.LOG_PREFIX.APP} 处理普通应用消息`);
      return { success: true, processed: true, type: 'app' };

    } catch (error) {
      console.error(`${this.LOG_PREFIX.APP} 处理应用消息失败:`, error.message);
      return { success: false, processed: false, type: 'app', error: error.message };
    }
  }
  
  /**
   * 检测是否为红包消息
   */
  isRedPacketMessage(message) {
    try {
      // 检查消息类型
      if (message.msg_type !== 49) {
        return false;
      }

      // 获取消息内容
      let content = '';
      if (message.content) {
        if (typeof message.content === 'string') {
          content = message.content;
        } else if (message.content.str) {
          content = message.content.str;
        } else {
          content = String(message.content);
        }
      }

      if (!content) {
        return false;
      }

      // 检查红包特征
      const hasWxpayProtocol = content.includes('wxpay://');
      const hasRedPacketXML = content.includes('<wcpayinfo>') && content.includes('</wcpayinfo>');
      const hasSendId = /sendid=\d+/.test(content);
      const hasChannelId = /channelid=\d+/.test(content);
      const hasRedPacketTitle = content.includes('微信红包') || content.includes('恭喜发财');

      // 需要满足多个条件才认为是红包消息
      const isRedPacket = hasWxpayProtocol && hasRedPacketXML && (hasSendId || hasChannelId);

      if (isRedPacket) {
        console.log(`${this.LOG_PREFIX.APP} 红包特征检测通过`);
      }

      return isRedPacket;
    } catch (error) {
      console.error(`${this.LOG_PREFIX.APP} 红包检测失败:`, error.message);
      return false;
    }
  }

  /**
   * 处理未知消息
   */
  async processUnknownMessage(message) {
    console.log(`[消息处理器V2] 处理未知类型消息: ${message.msg_id}, 类型: ${message.msg_type}`);

    return { success: true, processed: false, type: 'unknown' };
  }
  
  /**
   * 检查权限
   */
  checkPermissions(message) {
    // {{ AURA-X: Modify - 实现真正的权限检查逻辑. Approval: 寸止(ID:1753081807). }}
    try {
      // 提取发送者ID
      let senderId = null;
      if (message.from_user_name) {
        if (typeof message.from_user_name === 'object' && message.from_user_name.str) {
          senderId = message.from_user_name.str;
        } else {
          senderId = String(message.from_user_name);
        }
      } else if (message.FromUserName) {
        if (typeof message.FromUserName === 'object' && message.FromUserName.str) {
          senderId = message.FromUserName.str;
        } else {
          senderId = String(message.FromUserName);
        }
      }

      if (!senderId) {
        console.log(`${this.LOG_PREFIX.GENERAL} 权限检查失败: 无法获取发送者ID`);
        return false;
      }

      console.log(`${this.LOG_PREFIX.GENERAL} 检查权限: 发送者=${senderId}`);

      // 尝试获取权限服务
      let permissionsService = null;
      if (this.serviceLoader && typeof this.serviceLoader.getService === 'function') {
        permissionsService = this.serviceLoader.getService('permissions');
      }

      if (permissionsService) {
        console.log(`${this.LOG_PREFIX.GENERAL} 使用权限服务检查权限`);

        // 检查是否是群聊
        const isGroup = senderId.endsWith('@chatroom');

        if (isGroup) {
          // 群聊权限检查
          const hasPermission = permissionsService.isGroupInWhitelist(senderId);
          console.log(`${this.LOG_PREFIX.GENERAL} 群聊 ${senderId} ${hasPermission ? '在' : '不在'}白名单中`);
          return hasPermission;
        } else {
          // 用户权限检查
          const hasPermission = permissionsService.isUserInWhitelist(senderId);
          console.log(`${this.LOG_PREFIX.GENERAL} 用户 ${senderId} ${hasPermission ? '在' : '不在'}白名单中`);
          return hasPermission;
        }
      } else {
        console.log(`${this.LOG_PREFIX.GENERAL} 未找到权限服务，使用基本权限检查`);

        // 基本权限检查：管理员总是有权限
        const adminIds = ['wxid_lmqilgrqxhjd12', 'wxid_admin123456', 'filehelper'];
        if (adminIds.includes(senderId)) {
          console.log(`${this.LOG_PREFIX.GENERAL} 发送者 ${senderId} 是管理员，允许访问`);
          return true;
        }

        // 检查是否是群聊且在基本白名单中
        if (senderId.endsWith('@chatroom')) {
          const basicGroupWhitelist = [
            '52962727129@chatroom',
            '43732735175@chatroom',
            '19003715224@chatroom'
          ];
          const hasPermission = basicGroupWhitelist.includes(senderId);
          console.log(`${this.LOG_PREFIX.GENERAL} 群聊 ${senderId} ${hasPermission ? '在' : '不在'}基本白名单中`);
          return hasPermission;
        }

        // 默认拒绝
        console.log(`${this.LOG_PREFIX.GENERAL} 发送者 ${senderId} 不在基本白名单中，拒绝访问`);
        return false;
      }
    } catch (error) {
      console.error(`${this.LOG_PREFIX.GENERAL} 权限检查过程中出错: ${error.message}`);
      return false;
    }
  }
  
  /**
   * 提取文本内容
   */
  extractTextContent(message) {
    // {{ AURA-X: Fix - 正确处理content对象格式. Approval: 寸止(ID:1751501600). }}
    let content = message.content || message.text || null;

    // 如果content是对象，提取str字段
    if (content && typeof content === 'object' && content.str) {
      content = content.str;
    }

    // 确保返回字符串或null
    return typeof content === 'string' ? content : null;
  }
  
  /**
   * 检查是否需要AI回复
   */
  shouldReplyWithAI(content, message) {
    // AI回复判断逻辑
    return content && content.length > 0;
  }
  
  /**
   * 生成AI回复
   */
  async generateAIReply(content, message) {
    // AI回复生成逻辑
    return { success: true, aiReply: true, type: 'text' };
  }
  
  /**
   * 基本图片处理
   */
  async processImageBasic(message) {
    // {{ AURA-X: Modify - 实现真正的图片下载和二维码识别功能. Approval: 寸止(ID:1753081807). }}
    console.log(`${this.LOG_PREFIX.IMAGE} 开始基本图片处理: ${message.msg_id}`);

    try {
      // 尝试获取图片服务
      let imageService = null;
      if (this.serviceLoader && typeof this.serviceLoader.getService === 'function') {
        imageService = this.serviceLoader.getService('image');
      }

      // 如果找到图片服务，使用它处理图片
      if (imageService && typeof imageService.handleImageMessage === 'function') {
        console.log(`${this.LOG_PREFIX.IMAGE} 使用图片服务处理图片消息`);
        const result = await imageService.handleImageMessage(message);

        if (result && result.success) {
          console.log(`${this.LOG_PREFIX.IMAGE} 图片服务处理成功: ${JSON.stringify(result)}`);
          return {
            success: true,
            processed: true,
            type: 'image',
            method: 'service',
            result
          };
        } else {
          console.log(`${this.LOG_PREFIX.IMAGE} 图片服务处理失败: ${result ? JSON.stringify(result) : '无结果'}`);
        }
      } else {
        console.log(`${this.LOG_PREFIX.IMAGE} 未找到图片服务，尝试使用备用方法`);
      }

      // 尝试使用image-message-monitor模块
      try {
        const imageMonitor = require('../services/image-message-monitor');
        if (imageMonitor && typeof imageMonitor.handleImageMessage === 'function') {
          console.log(`${this.LOG_PREFIX.IMAGE} 使用图片监控器处理图片消息`);
          const result = await imageMonitor.handleImageMessage(message, this.serviceLoader);

          if (result && result.success) {
            console.log(`${this.LOG_PREFIX.IMAGE} 图片监控器处理成功: ${JSON.stringify(result)}`);
            return {
              success: true,
              processed: true,
              type: 'image',
              method: 'monitor',
              result
            };
          } else {
            console.log(`${this.LOG_PREFIX.IMAGE} 图片监控器处理失败: ${result ? JSON.stringify(result) : '无结果'}`);
          }
        }
      } catch (monitorError) {
        console.error(`${this.LOG_PREFIX.IMAGE} 加载图片监控器失败: ${monitorError.message}`);
      }

      // 如果所有方法都失败，返回基本成功状态
      console.log(`${this.LOG_PREFIX.IMAGE} 所有图片处理方法都失败，返回基本成功状态`);
      return {
        success: true,
        processed: false,
        type: 'image',
        method: 'basic',
        message: '图片处理失败，但不影响消息流程'
      };
    } catch (error) {
      console.error(`${this.LOG_PREFIX.IMAGE} 图片处理过程中出错: ${error.message}`);
      return {
        success: false,
        error: error.message,
        type: 'image',
        method: 'basic'
      };
    }
  }
  
  /**
   * 更新处理指标
   */
  updateProcessingMetrics(processingTime, success) {
    if (success) {
      this.metrics.processedMessages++;
      
      // 更新平均处理时间
      const totalProcessed = this.metrics.processedMessages;
      if (totalProcessed === 1) {
        this.metrics.averageProcessingTime = processingTime;
      } else {
        this.metrics.averageProcessingTime = 
          (this.metrics.averageProcessingTime * (totalProcessed - 1) + processingTime) / totalProcessed;
      }
    } else {
      this.metrics.failedMessages++;
    }
  }
  
  /**
   * 生成消息ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * {{ AURA-X: Add - 添加消息路由器兼容接口. Approval: 寸止(ID:1751501600). }}
   * 处理一批消息 - 消息路由器接口适配
   * @param {Array} messages 消息数组
   * @param {Object} serviceLoader 服务加载器
   * @param {Object} authManager 认证管理器
   * @return {Object} 处理结果 {success: boolean, processed: number}
   */
  async handleMessages(messages, serviceLoader, authManager) {
    try {
      console.log(`[消息处理器V2] handleMessages接收到 ${messages.length} 条消息`);
      const results = await this.processMessages(messages);
      return {
        success: true,
        processed: results.filter(r => r.success).length,
        results: results
      };
    } catch (error) {
      console.error(`[消息处理器V2] handleMessages处理失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        processed: 0
      };
    }
  }

  /**
   * {{ AURA-X: Add - 添加批量消息处理方法以兼容V1接口. Approval: 寸止(ID:1751501600). }}
   * 处理一批消息 - 兼容V1接口
   * @param {Array} messages 消息数组
   * @return {Array} 处理结果数组
   */
  async processMessages(messages) {
    if (!Array.isArray(messages)) {
      console.warn(`[消息处理器V2] processMessages接收到非数组参数，转换为数组`);
      messages = [messages];
    }

    console.log(`[消息处理器V2] 开始批量处理 ${messages.length} 条消息`);
    const results = [];

    for (const message of messages) {
      try {
        const result = await this.processMessage(message);
        results.push({
          msgId: message.msg_id || message.MsgId || 'unknown',
          msgType: message.msg_type || message.MsgType || 'unknown',
          success: result.success || false,
          result: result
        });
      } catch (error) {
        console.error(`[消息处理器V2] 批量处理中单个消息失败: ${error.message}`);
        results.push({
          msgId: message.msg_id || message.MsgId || 'unknown',
          msgType: message.msg_type || message.MsgType || 'unknown',
          success: false,
          error: error.message
        });
      }
    }

    console.log(`[消息处理器V2] 批量处理完成，成功: ${results.filter(r => r.success).length}/${results.length}`);

    // {{ AURA-X: Fix - 返回消息路由器期望的格式. Approval: 寸止(ID:1751501600). }}
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    return {
      success: successCount === totalCount,
      processed: successCount,
      total: totalCount,
      results: results,
      allSuccess: successCount === totalCount
    };
  }

  /**
   * 传统处理方法（回退）
   */
  async processMessageLegacy(message) {
    console.warn(`[消息处理器V2] 使用传统处理方法`);
    // 这里可以调用原始的处理逻辑
    return { success: true, method: 'legacy' };
  }
  
  /**
   * 获取状态信息
   */
  async getStatus() {
    if (!this.messageState) {
      return { error: '状态管理器未初始化' };
    }
    
    return {
      state: this.messageState.getSnapshot(),
      metrics: { ...this.metrics },
      config: { ...this.processingConfig },
      queueLength: this.messageQueue.length,
      processingMessages: this.processingMessages.size,
      cacheSize: this.messageCache.size
    };
  }
  
  /**
   * {{ AURA-X: Add - 设置并发管理器方法. Approval: 寸止(ID:1751501600). }}
   * 设置并发管理器
   * @param {Object} concurrencyManager - 并发管理器实例
   */
  setConcurrencyManager(concurrencyManager) {
    this.concurrencyManager = concurrencyManager;
    console.log('[消息处理器V2] ✅ 并发管理器已设置');
  }

  /**
   * 清理资源
   */
  cleanup() {
    // {{ AURA-X: Fix - 增强资源清理，确保事件监听器正确清理. Approval: 寸止(ID:1751501600). }}
    this.messageQueue = [];
    this.processingMessages.clear();
    this.messageCache.clear();
    this.rateLimitTracker.clear();

    // 清理通过ResourceManager注册的事件监听器
    if (this.resourceManager && this.eventListenerIds.size > 0) {
      for (const listenerId of this.eventListenerIds) {
        try {
          this.resourceManager.releaseResource(listenerId);
        } catch (error) {
          console.warn(`[消息处理器V2] 清理事件监听器失败: ${listenerId} - ${error.message}`);
        }
      }
      this.eventListenerIds.clear();
    }

    this.removeAllListeners();
    console.log(`[消息处理器V2] 资源清理完成`);
  }
  
  /**
   * 优雅关闭
   */
  async shutdown() {
    console.log(`[消息处理器V2] 开始关闭`);
    
    // 等待所有处理中的消息完成
    while (this.processingMessages.size > 0) {
      console.log(`[消息处理器V2] 等待 ${this.processingMessages.size} 个消息处理完成...`);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    this.cleanup();
    console.log(`[消息处理器V2] 关闭完成`);
  }
}

module.exports = MessageProcessorV2;
