/**
 * 紧急监控模块 - 监控系统关键指标
 * 用于实时监控内存使用、队列长度等关键指标，防止系统卡死
 */

class EmergencyMonitor {
  constructor(options = {}) {
    this.options = {
      checkInterval: options.checkInterval || 30000, // 30秒检查一次
      memoryThreshold: options.memoryThreshold || 100 * 1024 * 1024, // 100MB
      queueThreshold: options.queueThreshold || 800, // 队列长度阈值
      logInterval: options.logInterval || 5 * 60 * 1000, // 5分钟记录一次
      enableAutoCleanup: options.enableAutoCleanup !== false, // 自动清理
    };
    
    this.lastLogTime = Date.now();
    this.alerts = new Map();
    this.stats = {
      checksPerformed: 0,
      alertsTriggered: 0,
      cleanupOperations: 0,
      startTime: Date.now()
    };
    
    // 启动监控
    this.startMonitoring();
    
    // 监听紧急清理事件
    process.on('emergency-cleanup', () => {
      this.performEmergencyCleanup();
    });
    
    console.log('[紧急监控] 系统监控已启动');
  }
  
  startMonitoring() {
    this.monitorTimer = setInterval(() => {
      this.checkSystemHealth();
    }, this.options.checkInterval);
    
    // 优雅关闭处理
    process.on('SIGINT', () => this.shutdown());
    process.on('SIGTERM', () => this.shutdown());
  }
  
  checkSystemHealth() {
    this.stats.checksPerformed++;
    const now = Date.now();
    const memoryUsage = process.memoryUsage();
    const memoryMB = memoryUsage.rss / 1024 / 1024;
    
    // 检查内存使用
    if (memoryMB > this.options.memoryThreshold / 1024 / 1024) {
      this.triggerAlert('memory', `内存使用过高: ${memoryMB.toFixed(2)}MB`);
    } else {
      this.resolveAlert('memory');
    }
    
    // 检查队列长度（如果有全局队列引用）
    this.checkQueueLengths();
    
    // 定期记录系统状态
    if (now - this.lastLogTime > this.options.logInterval) {
      this.logSystemStatus(memoryUsage);
      this.lastLogTime = now;
    }
  }
  
  checkQueueLengths() {
    try {
      // 检查消息处理器队列（如果存在全局引用）
      if (global.messageProcessor && global.messageProcessor.messageQueue) {
        const queueLength = global.messageProcessor.messageQueue.length;
        if (queueLength > this.options.queueThreshold) {
          this.triggerAlert('queue', `消息队列积压: ${queueLength} 条消息`);
        } else {
          this.resolveAlert('queue');
        }
      }
      
      // 检查消息路由器缓存（如果存在全局引用）
      if (global.messageRouter && global.messageRouter.processedMessages) {
        const cacheSize = global.messageRouter.processedMessages.size;
        const maxSize = global.messageRouter.maxCacheSize || 500;
        if (cacheSize > maxSize * 0.9) { // 90%阈值
          this.triggerAlert('cache', `消息路由器缓存接近满载: ${cacheSize}/${maxSize}`);
        } else {
          this.resolveAlert('cache');
        }
      }
    } catch (error) {
      // 忽略检查错误，避免影响主流程
    }
  }
  
  triggerAlert(type, message) {
    if (!this.alerts.has(type)) {
      console.error(`[紧急监控] 🚨 告警触发: ${message}`);
      this.alerts.set(type, { 
        message, 
        triggeredAt: Date.now(),
        count: 1
      });
      this.stats.alertsTriggered++;
      
      // 根据告警类型执行相应操作
      if (this.options.enableAutoCleanup) {
        switch (type) {
          case 'memory':
            this.performMemoryCleanup();
            break;
          case 'queue':
            this.performQueueCleanup();
            break;
          case 'cache':
            this.performCacheCleanup();
            break;
        }
      }
    } else {
      // 增加告警计数
      this.alerts.get(type).count++;
    }
  }
  
  resolveAlert(type) {
    if (this.alerts.has(type)) {
      const alert = this.alerts.get(type);
      const duration = Date.now() - alert.triggeredAt;
      console.log(`[紧急监控] ✅ 告警解除: ${type} (持续时间: ${Math.round(duration/1000)}秒, 触发次数: ${alert.count})`);
      this.alerts.delete(type);
    }
  }
  
  performMemoryCleanup() {
    console.log('[紧急监控] 执行内存清理...');
    this.stats.cleanupOperations++;
    
    // 强制垃圾回收
    if (global.gc) {
      const beforeGC = process.memoryUsage().rss;
      global.gc();
      const afterGC = process.memoryUsage().rss;
      const freed = (beforeGC - afterGC) / 1024 / 1024;
      console.log(`[紧急监控] 强制GC完成，释放内存: ${freed.toFixed(2)}MB`);
    }
    
    // 通知其他模块执行清理
    process.emit('emergency-cleanup');
  }
  
  performQueueCleanup() {
    console.log('[紧急监控] 执行队列清理...');
    
    // 通知消息处理器清理队列
    if (global.messageProcessor && typeof global.messageProcessor.emergencyCleanup === 'function') {
      global.messageProcessor.emergencyCleanup();
    }
  }
  
  performCacheCleanup() {
    console.log('[紧急监控] 执行缓存清理...');
    
    // 通知消息路由器清理缓存
    if (global.messageRouter && typeof global.messageRouter.cleanupExpiredMessages === 'function') {
      global.messageRouter.cleanupExpiredMessages();
    }
  }
  
  performEmergencyCleanup() {
    console.log('[紧急监控] 执行全面紧急清理...');
    
    this.performMemoryCleanup();
    this.performQueueCleanup();
    this.performCacheCleanup();
    
    // 清理监控器自身的数据
    if (this.alerts.size > 10) {
      console.log('[紧急监控] 清理过多的告警记录');
      this.alerts.clear();
    }
  }
  
  logSystemStatus(memoryUsage) {
    const uptime = Math.round((Date.now() - this.stats.startTime) / 1000);
    const memoryMB = memoryUsage.rss / 1024 / 1024;
    const heapMB = memoryUsage.heapUsed / 1024 / 1024;
    
    console.log(`[紧急监控] 系统状态报告:`);
    console.log(`  运行时间: ${Math.floor(uptime/3600)}h ${Math.floor((uptime%3600)/60)}m ${uptime%60}s`);
    console.log(`  内存使用: ${memoryMB.toFixed(2)}MB (堆: ${heapMB.toFixed(2)}MB)`);
    console.log(`  检查次数: ${this.stats.checksPerformed}, 告警次数: ${this.stats.alertsTriggered}, 清理次数: ${this.stats.cleanupOperations}`);
    console.log(`  当前告警: ${this.alerts.size} 个`);
    
    if (this.alerts.size > 0) {
      console.log(`  活跃告警: ${Array.from(this.alerts.keys()).join(', ')}`);
    }
  }
  
  getStatus() {
    const memoryUsage = process.memoryUsage();
    const uptime = Math.round((Date.now() - this.stats.startTime) / 1000);
    
    return {
      uptime: uptime,
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024),
        heap: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024)
      },
      alerts: Array.from(this.alerts.entries()).map(([type, alert]) => ({
        type,
        message: alert.message,
        duration: Math.round((Date.now() - alert.triggeredAt) / 1000),
        count: alert.count
      })),
      stats: this.stats,
      thresholds: {
        memory: Math.round(this.options.memoryThreshold / 1024 / 1024),
        queue: this.options.queueThreshold
      }
    };
  }
  
  // 手动触发检查
  forceCheck() {
    console.log('[紧急监控] 执行手动健康检查...');
    this.checkSystemHealth();
  }
  
  // 手动触发清理
  forceCleanup() {
    console.log('[紧急监控] 执行手动清理...');
    this.performEmergencyCleanup();
  }
  
  // 更新配置
  updateConfig(newOptions) {
    Object.assign(this.options, newOptions);
    console.log('[紧急监控] 配置已更新:', newOptions);
  }
  
  shutdown() {
    console.log('[紧急监控] 正在关闭监控器...');
    
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
      this.monitorTimer = null;
    }
    
    // 最后一次状态报告
    this.logSystemStatus(process.memoryUsage());
    
    console.log('[紧急监控] 监控器已关闭');
  }
}

module.exports = EmergencyMonitor;
