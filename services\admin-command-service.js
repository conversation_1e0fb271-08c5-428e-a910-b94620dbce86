/**
 * 管理员命令服务
 * 整合各个子命令模块，处理管理员命令
 */

const logger = require('../utils/logger');
const {
  BasicCommands,
  UserCommands,
  GroupCommands,
  MonitorCommands,
  SpecialMonitorCommands,
  DownloadCommands,
  RedPacketCommands,
  MentionCommands,
  XiaohongshuCommands
} = require('./admin-commands');

class AdminCommandService {
  constructor(options = {}) {
    this.serviceLoader = options.serviceLoader;
    this.config = options.config || {};
    this.permissionsService = options.permissionsService;
    this.messageSender = options.messageSender;
    this.saveConfig = options.saveConfig || (() => Promise.resolve(true));

    // 统计信息
    this.stats = {
      startTime: Date.now(),
      handledCommands: 0
    };

    // 初始化命令处理器映射
    this.commandHandlers = {};

    // 初始化中文命令映射
    this.chineseCommandMap = {};

    // 初始化命令别名映射
    this.commandAliases = {};

    // 加载所有命令模块
    this.loadCommandModules();

    // 增加广播到指定用户的方法
    this.broadcastToAdmins = this.broadcastToAdmins.bind(this);

    logger.info('管理员命令服务初始化完成');
  }

  /**
   * 加载所有命令模块
   */
  loadCommandModules() {
    try {
      // 初始化基础命令模块
      this.basicCommands = new BasicCommands(this);
      this.registerCommandModule(this.basicCommands);

      // 初始化用户管理命令模块
      this.userCommands = new UserCommands(this);
      this.registerCommandModule(this.userCommands);

      // 初始化群组管理命令模块
      this.groupCommands = new GroupCommands(this);
      this.registerCommandModule(this.groupCommands);

      // 初始化监控命令模块
      this.monitorCommands = new MonitorCommands(this);
      this.registerCommandModule(this.monitorCommands);

      // 初始化特殊监控命令模块
      this.specialMonitorCommands = new SpecialMonitorCommands(this);
      this.registerCommandModule(this.specialMonitorCommands);

      // 初始化下载命令模块
      this.downloadCommands = new DownloadCommands(this);
      this.registerCommandModule(this.downloadCommands);

      // 初始化红包命令模块
      this.redPacketCommands = new RedPacketCommands(this);
      this.registerCommandModule(this.redPacketCommands);

      // 初始化艾特命令模块
      this.mentionCommands = new MentionCommands(this);
      this.registerCommandModule(this.mentionCommands);

      // 初始化小红书转发命令模块
      this.xiaohongshuCommands = new XiaohongshuCommands(this);
      this.registerCommandModule(this.xiaohongshuCommands);

      // {{ AURA-X: Add - 初始化浏览器服务健康监控命令模块. Approval: 寸止(ID:1751501600). }}
      // 初始化浏览器服务健康监控命令模块
      const BrowserServiceCommands = require('./admin-commands/browser-service-commands');
      this.browserServiceCommands = new BrowserServiceCommands(this);
      this.registerCommandModule(this.browserServiceCommands);

      // 初始化内存管理命令模块
      const MemoryCommands = require('../commands/memory-commands');
      this.memoryCommands = new MemoryCommands(this);
      this.registerCommandModule(this.memoryCommands);

      // 初始化内存诊断命令模块
      const MemoryDiagnostic = require('../commands/memory-diagnostic');
      this.memoryDiagnostic = new MemoryDiagnostic(this);
      this.registerCommandModule(this.memoryDiagnostic);

      // 初始化日志管理命令模块
      logger.info('开始加载日志管理命令模块...');
      const LogManagementCommands = require('../commands/log-management-commands');
      this.logManagementCommands = new LogManagementCommands(this);
      logger.info('日志管理命令模块实例创建成功');
      this.registerCommandModule(this.logManagementCommands);
      logger.info('日志管理命令模块注册完成');

      // 手动注册特殊命令（直接转发到其他服务的命令）
      this.registerSpecialCommands();

      // 注册命令别名
      this.registerCommandAliases();

      // 注册中文命令映射
      this.registerChineseCommands();

      logger.info(`已注册 ${Object.keys(this.commandHandlers).length} 个命令处理器`);
    } catch (error) {
      logger.error(`加载命令模块失败: ${error.message}`);
    }
  }

  /**
   * 注册命令模块的所有命令
   * @param {Object} module - 命令模块实例
   */
  registerCommandModule(module) {
    try {
      const commands = module.getCommands();
      logger.info(`注册命令模块，包含 ${Object.keys(commands).length} 个命令: ${Object.keys(commands).join(', ')}`);
      for (const [command, handler] of Object.entries(commands)) {
        this.commandHandlers[command] = handler;
        logger.info(`已注册命令: ${command}`);
      }
    } catch (error) {
      logger.error(`注册命令模块失败: ${error.message}`);
      logger.error(`错误堆栈: ${error.stack}`);
    }
  }

  /**
   * 注册特殊命令（直接转发到其他服务的命令）
   */
  registerSpecialCommands() {
    try {
      // 注册抖音监控命令
      this.commandHandlers['/douyin'] = this.handleDouyinCommand.bind(this);
      logger.info('已注册特殊命令: /douyin');

      // 可以在这里添加其他需要特殊处理的命令
      // this.commandHandlers['/other'] = this.handleOtherCommand.bind(this);

    } catch (error) {
      logger.error(`注册特殊命令失败: ${error.message}`);
    }
  }

  /**
   * 注册命令别名
   */
  registerCommandAliases() {
    // 基础命令别名
    this.registerAlias('/h', '/help');
    this.registerAlias('/s', '/status');
    this.registerAlias('/r', '/restart');
    this.registerAlias('/c', '/clear');
    this.registerAlias('/st', '/stats');

    // 用户命令别名
    this.registerAlias('/bl', '/blacklist');
    this.registerAlias('/bc', '/broadcast');
    this.registerAlias('/t', '/toggle');

    // 群组命令别名
    this.registerAlias('/g', '/group');
    this.registerAlias('/img', '/image');
    this.registerAlias('/rec', '/record');

    // 监控命令别名
    this.registerAlias('/m', '/monitor');
    this.registerAlias('/gh', '/github');
    this.registerAlias('/dp', '/dianping');
    this.registerAlias('/dy', '/douyin');

    // 红包命令别名
    this.registerAlias('/rp', '/redpacket');
    this.registerAlias('/红包', '/redpacket');

    // 小红书转发命令别名
    this.registerAlias('/xhs', '/xiaohongshu');
    this.registerAlias('/小红书', '/xiaohongshu');

    logger.info(`已注册 ${Object.keys(this.commandAliases).length} 个命令别名`);
  }

  /**
   * 注册中文命令映射
   */
  registerChineseCommands() {
    // 基础命令中文映射
    this.registerChineseCommand('/帮助', '/help');
    this.registerChineseCommand('/状态', '/status');
    this.registerChineseCommand('/重启', '/restart');
    this.registerChineseCommand('/重载', '/reload');
    this.registerChineseCommand('/清理', '/clear');
    this.registerChineseCommand('/测试', '/test');
    this.registerChineseCommand('/统计', '/stats');
    this.registerChineseCommand('/查看统计', '/stats');
    this.registerChineseCommand('/转换', '/convert');
    this.registerChineseCommand('/聊天记录转换', '/convert');
    this.registerChineseCommand('/转换执行', '/convert run');
    this.registerChineseCommand('/转换状态', '/convert status');
    this.registerChineseCommand('/转换文件', '/convert files');

    // 用户命令中文映射
    this.registerChineseCommand('/黑名单', '/blacklist');
    this.registerChineseCommand('/查看黑名单', '/blacklist list');
    this.registerChineseCommand('/添加黑名单', '/blacklist add');
    this.registerChineseCommand('/移除黑名单', '/blacklist remove');
    this.registerChineseCommand('/广播', '/broadcast');
    this.registerChineseCommand('/开关', '/toggle');
    this.registerChineseCommand('/管理员', '/admin');
    this.registerChineseCommand('/查看管理员', '/admin list');
    this.registerChineseCommand('/添加管理员', '/admin add');
    this.registerChineseCommand('/移除管理员', '/admin remove');

    // 群组命令中文映射
    this.registerChineseCommand('/群组', '/group');
    this.registerChineseCommand('/查看群组', '/group list');
    this.registerChineseCommand('/启用群组', '/group enable');
    this.registerChineseCommand('/禁用群组', '/group disable');
    this.registerChineseCommand('/图片', '/image');
    this.registerChineseCommand('/查看图片白名单', '/image list');
    this.registerChineseCommand('/添加下载白名单', '/image allow group');
    this.registerChineseCommand('/添加用户下载白名单', '/image allow user');
    this.registerChineseCommand('/移除下载白名单', '/image deny group');
    this.registerChineseCommand('/移除用户下载白名单', '/image deny user');
    this.registerChineseCommand('/记录', '/record');
    this.registerChineseCommand('/查看记录目标', '/record list');
    this.registerChineseCommand('/添加记录目标', '/record add');
    this.registerChineseCommand('/移除记录目标', '/record remove');

    // 监控命令中文映射
    this.registerChineseCommand('/监控', '/monitor');
    this.registerChineseCommand('/大众点评', '/dianping');
    this.registerChineseCommand('/抖音', '/douyin');

    // 抖音子命令中文映射
    this.registerChineseCommand('/抖音诊断', '/douyin diagnose');
    this.registerChineseCommand('/抖音重置', '/douyin reset');
    this.registerChineseCommand('/抖音强制检查', '/douyin forcecheck');
    this.registerChineseCommand('/抖音状态', '/douyin status');
    this.registerChineseCommand('/抖音启动', '/douyin start');
    this.registerChineseCommand('/抖音停止', '/douyin stop');
    this.registerChineseCommand('/抖音帮助', '/douyin help');

    // 红包命令中文映射
    this.registerChineseCommand('/红包', '/redpacket');
    this.registerChineseCommand('/红包状态', '/redpacket status');
    this.registerChineseCommand('/启用红包', '/redpacket enable');
    this.registerChineseCommand('/禁用红包', '/redpacket disable');
    this.registerChineseCommand('/红包统计', '/redpacket stats');
    this.registerChineseCommand('/重置红包统计', '/redpacket reset');
    this.registerChineseCommand('/测试红包', '/redpacket test');

    // 艾特命令中文映射
    this.registerChineseCommand('/艾特', '/mention');
    this.registerChineseCommand('/艾特状态', '/mention status');
    this.registerChineseCommand('/启用艾特', '/mention enable');
    this.registerChineseCommand('/禁用艾特', '/mention disable');
    this.registerChineseCommand('/艾特群组', '/mention group');
    this.registerChineseCommand('/艾特用户', '/mention user');
    this.registerChineseCommand('/艾特缓存', '/mention cache');
    this.registerChineseCommand('/测试艾特', '/mention test');

    // 小红书转发命令中文映射
    this.registerChineseCommand('/小红书', '/xiaohongshu');
    this.registerChineseCommand('/小红书状态', '/xiaohongshu status');
    this.registerChineseCommand('/启用小红书', '/xiaohongshu enable');
    this.registerChineseCommand('/禁用小红书', '/xiaohongshu disable');
    this.registerChineseCommand('/小红书配置', '/xiaohongshu config');
    this.registerChineseCommand('/小红书统计', '/xiaohongshu stats');
    this.registerChineseCommand('/测试小红书', '/xiaohongshu test');
    this.registerChineseCommand('/小红书日志', '/xiaohongshu logs');
    this.registerChineseCommand('/小红书源群组', '/xiaohongshu source');
    this.registerChineseCommand('/小红书目标群组', '/xiaohongshu target');
    this.registerChineseCommand('/小红书权限', '/xiaohongshu permission');

    // {{ AURA-X: Add - 浏览器服务健康监控命令中文映射. Approval: 寸止(ID:1751501600). }}
    // 浏览器服务健康监控命令中文映射
    this.registerChineseCommand('/浏览器服务', '/browser');
    this.registerChineseCommand('/浏览器服务状态', '/browser status');
    this.registerChineseCommand('/启用浏览器监控', '/browser enable');
    this.registerChineseCommand('/禁用浏览器监控', '/browser disable');
    this.registerChineseCommand('/浏览器服务统计', '/browser stats');
    this.registerChineseCommand('/浏览器服务信息', '/browser info');
    this.registerChineseCommand('/浏览器服务重启', '/browser restart');
    this.registerChineseCommand('/浏览器服务恢复', '/browser recover');
    this.registerChineseCommand('/浏览器监控', '/browser');
    this.registerChineseCommand('/浏览器监控状态', '/browser status');
    this.registerChineseCommand('/浏览器监控统计', '/browser stats');

    logger.info(`已注册 ${Object.keys(this.chineseCommandMap).length} 个中文命令映射`);
  }

  /**
   * 注册命令别名
   * @param {string} alias - 命令别名
   * @param {string} command - 原始命令
   */
  registerAlias(alias, command) {
    this.commandAliases[alias] = command;
  }

  /**
   * 注册中文命令映射
   * @param {string} chineseCommand - 中文命令
   * @param {string} englishCommand - 对应的英文命令
   */
  registerChineseCommand(chineseCommand, englishCommand) {
    this.chineseCommandMap[chineseCommand] = englishCommand;
  }

  /**
   * 处理管理员命令
   * @param {Object} msg - 消息对象
   * @returns {Promise<boolean>} 是否处理了此消息
   */
  async handleCommand(msg) {
    try {
      // 提取消息内容和发送者ID
      const content = this.extractContent(msg);
      const fromUser = this.extractFromUser(msg);

      // 检查是否是命令
      if (!content || typeof content !== 'string' || !content.startsWith('/')) {
        return false;
      }

      // 检查当前用户是否具有管理员权限
      let isAdmin = false;
      if (this.permissionsService) {
        isAdmin = await this.permissionsService.hasRole(fromUser, 'admin');
      } else if (this.config.admin && Array.isArray(this.config.admin.admins)) {
        isAdmin = this.config.admin.admins.includes(fromUser);
      }

      // 如果用户不是管理员，则不处理命令
      if (!isAdmin) {
        logger.info(`用户 ${fromUser} 尝试执行管理员命令 ${content.substr(0, 20)}，但没有管理员权限`);
        return false;
      }

      // 解析命令和参数
      const [commandStr, ...args] = content.split(/\s+/);
      let command = commandStr.toLowerCase();

      // 检查是否是中文命令，如果是，转换为英文命令
      if (this.chineseCommandMap[command]) {
        const mappedCommand = this.chineseCommandMap[command];

        // 检查映射命令是否包含参数
        if (mappedCommand.includes(' ')) {
          const [baseCmd, ...mappedArgs] = mappedCommand.split(/\s+/);
          command = baseCmd;
          args.unshift(...mappedArgs);
        } else {
          command = mappedCommand;
        }

        logger.info(`中文命令 "${commandStr}" 映射到 "${command}"`);
      }

      // 检查是否是命令别名，如果是，转换为原始命令
      if (this.commandAliases[command]) {
        command = this.commandAliases[command];
        logger.info(`命令别名 "${commandStr}" 映射到 "${command}"`);
      }

      // 查找对应的命令处理器
      const handler = this.commandHandlers[command];

      if (!handler) {
        logger.info(`未找到命令处理器: ${command}`);
        return false;
      }

      // 增加命令处理计数
      this.stats.handledCommands++;

      // 调用命令处理器
      logger.info(`处理命令: ${command} ${args.join(' ')}`);
      await handler(msg, args);

      return true;
    } catch (error) {
      logger.error(`处理管理员命令出错: ${error.message}`);

      // 尝试回复错误消息
      try {
        await this.sendReply(msg, `处理命令出错: ${error.message}`);
      } catch (replyError) {
        logger.error(`无法回复错误消息: ${replyError.message}`);
      }

      return false;
    }
  }

  /**
   * 从消息对象中提取内容
   * @param {Object} msg - 消息对象
   * @returns {string} 消息内容
   */
  extractContent(msg) {
    if (typeof msg === 'string') {
      return msg;
    }

    // 尝试从各种可能的字段提取消息内容
    const possibleFields = ['content', 'Content', 'text', '_text'];

    for (const field of possibleFields) {
      if (msg[field] !== undefined) {
        if (typeof msg[field] === 'string') {
          return msg[field];
        } else if (msg[field] && typeof msg[field].text === 'string') {
          return msg[field].text;
        } else if (msg[field] && typeof msg[field].content === 'string') {
          return msg[field].content;
        }
      }
    }

    return '';
  }

  /**
   * 从消息对象中提取发送者ID
   * @param {Object} msg - 消息对象
   * @returns {string} 发送者ID
   */
  extractFromUser(msg) {
    const possibleFields = ['fromUserName', 'FromUserName', 'from_user_name', 'from'];

    for (const field of possibleFields) {
      if (msg[field] !== undefined && msg[field]) {
        return msg[field];
      }
    }

    return '';
  }

  /**
   * 发送回复消息
   * @param {Object} msg - 原始消息对象
   * @param {string} content - 回复内容
   * @returns {Promise<boolean>} 是否成功发送
   */
  async sendReply(msg, content) {
    try {
      // {{ EMERGENCY-FIX: 添加详细的调试日志 }}
      console.log('[sendReply] 开始发送回复消息');
      console.log('[sendReply] 消息内容长度:', content.length);
      console.log('[sendReply] 消息对象信息:', {
        hasReply: typeof msg.reply === 'function',
        FromUserName: msg.FromUserName,
        from_user_name: msg.from_user_name,
        hasMessageSender: !!this.messageSender
      });

      // 尝试使用消息对象的reply方法
      if (msg.reply && typeof msg.reply === 'function') {
        console.log('[sendReply] 使用msg.reply方法发送');
        await msg.reply(content);
        console.log('[sendReply] msg.reply发送成功');
        return true;
      }

      // 尝试从消息对象提取发送者
      const fromUser = this.extractFromUser(msg);
      console.log('[sendReply] 提取的发送者:', fromUser);

      // 如果有消息发送服务和发送者，则使用消息发送服务
      if (this.messageSender && fromUser) {
        console.log('[sendReply] 使用messageSender发送');
        await this.messageSender.sendText(fromUser, content);
        console.log('[sendReply] messageSender发送成功');
        return true;
      }

      console.log('[sendReply] 无法发送回复消息：未找到有效的回复方法或消息发送服务');
      logger.warn('无法发送回复消息：未找到有效的回复方法或消息发送服务');
      return false;
    } catch (error) {
      console.log('[sendReply] 发送回复消息失败:', error.message);
      logger.error(`发送回复消息失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取运行时间
   * @returns {string} 格式化的运行时间
   */
  getUptime() {
    const now = Date.now();
    const uptime = now - this.stats.startTime;

    const seconds = Math.floor((uptime / 1000) % 60);
    const minutes = Math.floor((uptime / (1000 * 60)) % 60);
    const hours = Math.floor((uptime / (1000 * 60 * 60)) % 24);
    const days = Math.floor(uptime / (1000 * 60 * 60 * 24));

    let uptimeStr = '';
    if (days > 0) {
      uptimeStr += `${days}天 `;
    }
    if (hours > 0 || days > 0) {
      uptimeStr += `${hours}小时 `;
    }
    if (minutes > 0 || hours > 0 || days > 0) {
      uptimeStr += `${minutes}分钟 `;
    }
    uptimeStr += `${seconds}秒`;

    return uptimeStr;
  }

  /**
   * 获取内存使用
   * @returns {string} 格式化的内存使用信息
   */
  getMemoryUsage() {
    const memoryUsage = process.memoryUsage();
    const rss = Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100;
    const heapTotal = Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100;
    const heapUsed = Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100;

    return `RSS: ${rss}MB, 堆总量: ${heapTotal}MB, 堆使用: ${heapUsed}MB`;
  }

  /**
   * 获取服务
   * @param {string} serviceName - 服务名称
   * @returns {Object|null} 服务实例或null
   */
  getService(serviceName) {
    if (this.serviceLoader) {
      return this.serviceLoader.getService(serviceName);
    }
    return null;
  }

  /**
   * 转发大众点评监控命令到monitor-service
   * @param {Object} msg - 消息对象
   * @param {Array} args - 命令参数
   */
  async handleDianpingCommand(msg, args) {
      // 获取监控服务
      const monitorService = this.getService('monitor');
      if (!monitorService) {
      await this.sendReply(msg, '❌ 监控服务未启用或未初始化');
        return;
      }

    try {
      // 构建标准化的消息对象
      const standardMsg = {
        content: `/dianping ${args.join(' ')}`,
        fromUserName: this.extractFromUser(msg),
        reply: (text) => this.sendReply(msg, text)
      };

      // 转发到监控服务处理
      const result = await monitorService.handleMessage(standardMsg);

      // 如果监控服务没有处理此命令，则返回错误
      if (!result || !result.handled) {
        await this.sendReply(msg, '❌ 大众点评监控命令处理失败');
      }
            } catch (error) {
      logger.error(`处理大众点评监控命令出错: ${error.message}`);
      await this.sendReply(msg, `处理大众点评监控命令出错: ${error.message}`);
    }
  }

  /**
   * 转发抖音监控命令到monitor-service
   * @param {Object} msg - 消息对象
   * @param {Array} args - 命令参数
   */
  async handleDouyinCommand(msg, args) {
      // 获取监控服务
      const monitorService = this.getService('monitor');
      if (!monitorService) {
      await this.sendReply(msg, '❌ 监控服务未启用或未初始化');
        return;
      }

    try {
      // 构建标准化的消息对象
      const standardMsg = {
        content: `/douyin ${args.join(' ')}`,
        fromUserName: this.extractFromUser(msg),
        reply: (text) => this.sendReply(msg, text)
      };

      // 转发到监控服务处理
      const result = await monitorService.handleMessage(standardMsg);

      // 如果监控服务没有处理此命令，则返回错误
      if (!result || !result.handled) {
        await this.sendReply(msg, '❌ 抖音监控命令处理失败');
      }
    } catch (error) {
      logger.error(`处理抖音监控命令出错: ${error.message}`);
      await this.sendReply(msg, `处理抖音监控命令出错: ${error.message}`);
    }
  }

  /**
   * 向所有管理员广播消息
   * @param {string} message - 要广播的消息
   * @param {string} excludeUser - 要排除的用户ID
   * @returns {Promise<{success: number, failed: number}>} 结果统计
   */
  async broadcastToAdmins(message, excludeUser = '') {
    const adminIds = this.config.admin?.admins || [];

    if (adminIds.length === 0 || !this.messageSender) {
      return { success: 0, failed: 0 };
    }

    let successCount = 0;
    let failedCount = 0;

    for (const adminId of adminIds) {
      if (adminId === excludeUser) {
        continue;
      }

      try {
        await this.messageSender.sendText(adminId, message);
        successCount++;
          } catch (error) {
        logger.error(`向管理员 ${adminId} 发送广播消息失败: ${error.message}`);
        failedCount++;
      }
    }

    return { success: successCount, failed: failedCount };
  }

  /**
   * 设置消息发送服务
   * @param {Object} messageSender - 消息发送服务实例
   */
  setMessageSender(messageSender) {
    this.messageSender = messageSender;
    logger.info('已设置消息发送服务到管理员命令服务');
  }

  /**
   * 设置服务加载器
   * @param {Object} serviceLoader - 服务加载器实例
   */
  setServiceLoader(serviceLoader) {
    this.serviceLoader = serviceLoader;
    logger.info('已设置服务加载器到管理员命令服务');
  }
}

module.exports = AdminCommandService;