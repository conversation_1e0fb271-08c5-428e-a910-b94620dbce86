#!/bin/bash

# 完整的第三个微信机器人API测试脚本
# 验证所有API接口的功能和兼容性

echo "🧪 完整测试第三个微信机器人的所有API接口..."

BASE_URL="http://localhost:8057"
ADMIN_KEY="3rd_bot_admin_key_2025_secure"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_api() {
    local test_name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local headers="$5"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}📋 测试 $TOTAL_TESTS: $test_name${NC}"
    echo "请求: $method $url"
    
    if [ "$method" = "POST" ]; then
        if [ -n "$headers" ]; then
            response=$(curl -s -w "\n%{http_code}" -X POST "$url" -H "Content-Type: application/json" -H "$headers" -d "$data")
        else
            response=$(curl -s -w "\n%{http_code}" -X POST "$url" -H "Content-Type: application/json" -d "$data")
        fi
    else
        if [ -n "$headers" ]; then
            response=$(curl -s -w "\n%{http_code}" "$url" -H "$headers")
        else
            response=$(curl -s -w "\n%{http_code}" "$url")
        fi
    fi
    
    # 分离响应体和状态码
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "状态码: $http_code"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 测试通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # 尝试格式化JSON输出
        if command -v jq &> /dev/null; then
            echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
        else
            echo "$response_body"
        fi
    else
        echo -e "${RED}❌ 测试失败${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "$response_body"
    fi
    
    echo ""
}

# 检查服务是否运行
echo -e "${YELLOW}🔍 检查服务状态...${NC}"
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo -e "${RED}❌ 服务未运行，请先启动程序: ./run-complete-api.sh${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 服务正在运行${NC}"
echo ""

# 开始测试
echo -e "${YELLOW}🚀 开始完整API测试...${NC}"
echo "======================================================"

# 测试1: 健康检查
test_api "健康检查接口" "GET" "$BASE_URL/health"

# 测试2: 登录状态检查
test_api "登录状态检查" "GET" "$BASE_URL/login/GetLoginStatus"

# 测试3: 唤醒登录
test_api "唤醒登录接口" "POST" "$BASE_URL/login/WakeUpLogin" '{"Check":false,"Proxy":""}'

# 测试4: 生成授权码 (GET方式)
test_api "生成授权码 (GET方式)" "GET" "$BASE_URL/login/GenAuthKey2?key=$ADMIN_KEY&count=1&days=30"

# 测试5: 生成授权码 (POST方式)
test_api "生成授权码 (POST方式)" "POST" "$BASE_URL/admin/GenAuthKey?key=$ADMIN_KEY" '{"Count":2,"Days":30}'

# 测试6: 管理员生成授权码1
test_api "管理员生成授权码1" "POST" "$BASE_URL/admin/GenAuthKey1?key=$ADMIN_KEY" '{"Count":1,"Days":30}'

# 测试7: 管理员生成微信授权码
test_api "管理员生成微信授权码" "POST" "$BASE_URL/admin/GenWxAuthKey?key=$ADMIN_KEY" '{"Count":1,"Days":30}'

# 测试8: 获取登录二维码
test_api "获取登录二维码" "POST" "$BASE_URL/login/GetLoginQrCodeNew?key=$ADMIN_KEY" '{"Check":false,"Proxy":""}'

# 测试9: API状态检查
test_api "API状态检查" "GET" "$BASE_URL/api/status" "" "Admin-Key: $ADMIN_KEY"

# 测试10: WebSocket端点
test_api "WebSocket端点" "GET" "$BASE_URL/ws"

# 测试11: 错误的管理员密钥
test_api "错误的管理员密钥" "GET" "$BASE_URL/login/GenAuthKey?key=wrong_key"

# 测试12: 未知API路径
test_api "未知API路径" "GET" "$BASE_URL/unknown/api"

# 测试13: 无效的JSON数据
test_api "无效的JSON数据" "POST" "$BASE_URL/login/WakeUpLogin" '{"invalid_json"}'

# 测试14: 缺少必需参数
test_api "缺少必需参数" "POST" "$BASE_URL/admin/GenAuthKey" '{}'

# 测试15: 大量授权码生成
test_api "大量授权码生成" "POST" "$BASE_URL/admin/GenAuthKey?key=$ADMIN_KEY" '{"Count":10,"Days":30}'

echo "======================================================"
echo -e "${YELLOW}📊 测试结果统计${NC}"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！第三个微信机器人API完全正常！${NC}"
    exit 0
else
    echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查相关接口${NC}"
    exit 1
fi
