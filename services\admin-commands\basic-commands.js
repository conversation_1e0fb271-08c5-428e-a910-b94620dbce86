/**
 * 管理员基础命令模块
 * 处理帮助、状态、重启等基础命令
 */

const logger = require('../../utils/logger');

class BasicCommands {
  constructor(service) {
    this.service = service; // 保存对主服务的引用
    this.config = service.config;
    this.permissionsService = service.permissionsService;
    this.messageSender = service.messageSender;
    this.serviceLoader = service.serviceLoader;
    this.stats = service.stats;

    // 绑定方法到当前实例
    this.handleHelpCommand = this.handleHelpCommand.bind(this);
    this.handleStatusCommand = this.handleStatusCommand.bind(this);
    this.handleRestartCommand = this.handleRestartCommand.bind(this);
    this.handleReloadCommand = this.handleReloadCommand.bind(this);
    this.handleTestCommand = this.handleTestCommand.bind(this);
    this.handleClearCommand = this.handleClearCommand.bind(this);
    this.handleStatsCommand = this.handleStatsCommand.bind(this);
    this.handleConvertCommand = this.handleConvertCommand.bind(this);
  }

  /**
   * 获取此模块的命令映射
   * @returns {Object} 命令映射
   */
  getCommands() {
    return {
      '/help': this.handleHelpCommand,
      '/status': this.handleStatusCommand,
      '/restart': this.handleRestartCommand,
      '/reload': this.handleReloadCommand,
      '/test': this.handleTestCommand,
      '/clear': this.handleClearCommand,
      '/stats': this.handleStatsCommand,
      '/convert': this.handleConvertCommand
    };
  }

  /**
   * 处理帮助命令
   * @param {Object} msg - 消息对象
   * @param {Array} args - 命令参数
   */
  async handleHelpCommand(msg, args) {
    if (args.length > 0 && args[0] === '帮助') {
      args.shift(); // 移除"帮助"参数
    }

    // 处理特定命令的帮助信息
    if (args.length > 0) {
      const specificCommand = args[0].toLowerCase();
      let helpText = '';

      switch (specificCommand) {
        case 'blacklist':
        case '黑名单':
          helpText = `
黑名单命令帮助:

英文命令:
/blacklist list - 查看AI黑名单
/blacklist add [wxid] - 添加用户到AI黑名单
/blacklist remove [wxid] - 从AI黑名单移除用户

中文命令:
/黑名单 查看 - 查看AI黑名单
/黑名单 添加 [wxid] - 添加用户到AI黑名单
/黑名单 移除 [wxid] - 从AI黑名单移除用户

别名:
/bl - /blacklist 的别名
/查看黑名单 - 查看AI黑名单
/添加黑名单 [wxid] - 添加用户到AI黑名单
/移除黑名单 [wxid] - 从AI黑名单移除用户
`;
          break;

        case 'group':
        case '群组':
          helpText = `
群组命令帮助:

英文命令:
/group list - 查看群组AI功能状态
/group enable [groupId] - 为群组启用AI功能
/group disable [groupId] - 为群组禁用AI功能

中文命令:
/群组 查看 - 查看群组AI功能状态
/群组 启用 [群组ID] - 为群组启用AI功能
/群组 禁用 [群组ID] - 为群组禁用AI功能

别名:
/g - /group 的别名
/查看群组 - 查看群组AI功能状态
/启用群组 [群组ID] - 为群组启用AI功能
/禁用群组 [群组ID] - 为群组禁用AI功能
`;
          break;

        case 'image':
        case '图片':
          helpText = `
图片下载权限命令帮助:

英文命令:
/image list - 查看图片下载白名单
/image allow [user|group] [wxid] - 添加用户或群组到图片下载白名单
/image deny [user|group] [wxid] - 从图片下载白名单移除用户或群组

中文命令:
/图片 查看 - 查看图片下载白名单
/图片 添加 [用户|群组] [wxid] - 添加用户或群组到图片下载白名单
/图片 移除 [用户|群组] [wxid] - 从图片下载白名单移除用户或群组

别名:
/img - /image 的别名
/查看图片白名单 - 查看图片下载白名单
/添加下载白名单 [群组ID] - 添加群组到图片下载白名单
/添加用户下载白名单 [用户ID] - 添加用户到图片下载白名单
/移除下载白名单 [群组ID] - 从图片下载白名单移除群组
/移除用户下载白名单 [用户ID] - 从图片下载白名单移除用户
`;
          break;

        case 'record':
        case '记录':
          helpText = `
聊天记录命令帮助:

英文命令:
/record list - 查看当前聊天记录目标列表
/record add [wxid] - 添加wxid到聊天记录目标列表
/record remove [wxid] - 从聊天记录目标列表中移除wxid

中文命令:
/记录 查看 - 查看当前聊天记录目标列表
/记录 添加 [wxid] - 添加wxid到聊天记录目标列表
/记录 移除 [wxid] - 从聊天记录目标列表中移除wxid

别名:
/rec - /record 的别名
/查看记录目标 - 查看当前聊天记录目标列表
/添加记录目标 [wxid] - 添加wxid到聊天记录目标列表
/移除记录目标 [wxid] - 从聊天记录目标列表中移除wxid
`;
          break;

        case 'toggle':
        case '开关':
          helpText = `
功能切换命令帮助:

英文命令:
/toggle - 显示所有功能状态
/toggle [feature] [on|off] - 切换功能开关

中文命令:
/开关 查看 - 显示所有功能状态
/开关 [功能名] [开|关] - 切换功能开关

别名:
/t - /toggle 的别名
`;
          break;

        case 'stats':
        case '统计':
          helpText = `
统计命令帮助:

英文命令:
/stats - 显示机器人统计信息

中文命令:
/统计 - 显示机器人统计信息
/查看统计 - 显示机器人统计信息

别名:
/st - /stats 的别名
`;
          break;

        case 'monitor':
        case '监控':
        case 'm':
          helpText = `
监控命令帮助:

英文命令:
/monitor status - 查看所有监控模块状态
/monitor start - 启动所有监控模块
/monitor stop - 停止所有监控模块
/monitor list - 列出所有监控模块和配置

中文命令:
/监控 状态 - 查看所有监控模块状态
/监控 启动 - 启动所有监控模块
/监控 停止 - 停止所有监控模块
/监控 查看 - 列出所有监控模块和配置

别名:
/m - /monitor 的别名
`;
          break;

        case 'github':
        case 'gh':
          helpText = `
GitHub监控命令帮助:

英文命令:
/github status - 查看GitHub监控状态
/github start - 启动GitHub监控
/github stop - 停止GitHub监控
/github list - 列出监控的GitHub仓库
/github add [owner/repo] - 添加GitHub仓库到监控列表
/github delete [owner/repo] - 从监控列表删除GitHub仓库

中文命令:
/github 状态 - 查看GitHub监控状态
/github 启动 - 启动GitHub监控
/github 停止 - 停止GitHub监控
/github 列表 - 列出监控的GitHub仓库
/github 添加 [owner/repo] - 添加GitHub仓库到监控列表
/github 删除 [owner/repo] - 从监控列表删除GitHub仓库

别名:
/gh - /github 的别名
`;
          break;

        case 'dianping':
        case 'dp':
        case '大众点评':
          helpText = `
大众点评监控命令帮助:

英文命令:
/dianping status - 查看大众点评监控状态
/dianping start - 启动大众点评监控
/dianping stop - 停止大众点评监控
/dianping list - 列出监控的商家
/dianping add [id] [name] - 添加商家到监控列表
/dianping delete [id] - 从监控列表删除商家

中文命令:
/dianping 状态 - 查看大众点评监控状态
/dianping 启动 - 启动大众点评监控
/dianping 停止 - 停止大众点评监控
/dianping 列表 - 列出监控的商家
/dianping 添加 [id] [name] - 添加商家到监控列表
/dianping 删除 [id] - 从监控列表删除商家

别名:
/dp - /dianping 的别名
/大众点评 - /dianping 的别名
`;
          break;

        case 'douyin':
        case 'dy':
        case '抖音':
          helpText = `
抖音线索监控命令帮助:

英文命令:
/douyin status - 查看抖音线索监控状态
/douyin start - 启动抖音线索监控
/douyin stop - 停止抖音线索监控
/douyin check - 立即检查新线索
/douyin account list - 列出监控的抖音账号
/douyin account add [id] [name] - 添加抖音账号到监控列表
/douyin account delete [id] - 从监控列表删除抖音账号
/douyin test - 测试抖音线索监控
/douyin decrypt [phone] - 解密抖音手机号

中文命令:
/douyin 状态 - 查看抖音线索监控状态
/douyin 启动 - 启动抖音线索监控
/douyin 停止 - 停止抖音线索监控
/douyin 账号 列表 - 列出监控的抖音账号
/douyin 账号 添加 [id] [name] - 添加抖音账号到监控列表
/douyin 账号 删除 [id] - 从监控列表删除抖音账号

别名:
/dy - /douyin 的别名
/抖音 - /douyin 的别名
`;
          break;

        case 'redpacket':
        case 'rp':
        case '红包':
          helpText = `
🧧 红包管理命令帮助:

📋 基本命令：
/redpacket status - 查看红包服务状态
/redpacket enable - 启用红包服务
/redpacket disable - 禁用红包服务
/redpacket stats - 查看红包统计
/redpacket reset - 重置统计数据
/redpacket test - 测试红包功能

⚙️ 配置命令：
/redpacket config show - 显示当前配置
/redpacket config mode [passive|active|hybrid] - 设置工作模式
/redpacket config delay [min] [max] - 设置随机延迟(毫秒)
/redpacket config limit [次数] - 设置每分钟最大抢红包次数

🔐 权限命令：
/redpacket permission show - 显示权限配置
/redpacket permission user add [wxid] - 添加用户权限
/redpacket permission user remove [wxid] - 移除用户权限
/redpacket permission group add [群ID] - 添加群组权限
/redpacket permission group remove [群ID] - 移除群组权限

🇨🇳 中文命令：
/红包 状态 - 查看红包服务状态
/启用红包 - 启用红包服务
/禁用红包 - 禁用红包服务
/红包统计 - 查看红包统计

💡 使用示例：
/redpacket enable
/redpacket config mode hybrid
/redpacket permission user add wxid_123456

别名:
/rp - /redpacket 的别名
/红包 - /redpacket 的别名

⚠️ 注意事项：
- 首次使用需要先启用服务：/redpacket enable
- 需要配置用户和群组权限才能正常工作
- 建议设置合理的延迟和频率限制以避免风险
`;
          break;

        case 'mention':
        case '艾特':
          helpText = `
🎯 艾特功能管理命令帮助:

📋 基本命令：
/mention status - 查看艾特功能状态
/mention enable - 启用艾特功能
/mention disable - 禁用艾特功能
/mention test [群组ID] - 测试艾特功能

👥 群组管理：
/mention group list - 查看允许艾特的群组列表
/mention group add [群组ID] - 添加群组到白名单
/mention group remove [群组ID] - 从白名单移除群组

🔐 用户权限：
/mention user list - 查看可以艾特所有人的用户列表
/mention user add [用户ID] - 添加艾特所有人权限
/mention user remove [用户ID] - 移除艾特所有人权限

💾 缓存管理：
/mention cache clear - 清理群成员缓存
/mention cache refresh [群组ID] - 刷新指定群组成员信息

🇨🇳 中文命令：
/艾特 状态 - 查看艾特功能状态
/启用艾特 - 启用艾特功能
/禁用艾特 - 禁用艾特功能
/艾特 群组 列表 - 查看群组白名单
/艾特 用户 列表 - 查看用户权限列表

💡 使用示例：
/mention enable
/mention group add 12345@chatroom
/mention user add wxid_admin123

📝 艾特语法：
- @用户名 - 艾特特定用户
- @所有人 - 艾特所有群成员（需权限）
- @管理员 - 艾特管理员角色

⚠️ 注意事项：
- 首次使用需要先启用功能：/mention enable
- 艾特所有人需要特殊权限
- 群成员信息会自动缓存30分钟
`;
          break;

        case 'xiaohongshu':
        case 'xhs':
        case '小红书':
          helpText = `
📱 小红书转发管理命令帮助:

🔧 基础命令：
/xiaohongshu status (或 /xhs 状态) - 查看转发服务状态
/xiaohongshu enable (或 /xhs 启用) - 启用转发功能
/xiaohongshu disable (或 /xhs 禁用) - 禁用转发功能
/xiaohongshu config (或 /xhs 配置) - 查看当前配置

📊 监控命令：
/xiaohongshu stats (或 /xhs 统计) - 查看转发统计信息
/xiaohongshu test (或 /xhs 测试) - 测试转发功能
/xiaohongshu logs (或 /xhs 日志) - 查看最近的转发日志

🎯 群组管理：
/xiaohongshu source list (或 /xhs 源群组 列表) - 查看源群组列表
/xiaohongshu source add <群组ID> (或 /xhs 源群组 添加) - 添加源群组
/xiaohongshu source remove <群组ID> (或 /xhs 源群组 移除) - 移除源群组

/xiaohongshu target list (或 /xhs 目标群组 列表) - 查看目标群组列表
/xiaohongshu target add <群组ID> (或 /xhs 目标群组 添加) - 添加目标群组
/xiaohongshu target remove <群组ID> (或 /xhs 目标群组 移除) - 移除目标群组

🔐 权限管理：
/xiaohongshu permission add <群组ID> (或 /xhs 权限 添加) - 添加群组权限
/xiaohongshu permission remove <群组ID> (或 /xhs 权限 移除) - 移除群组权限
/xiaohongshu permission list (或 /xhs 权限 列表) - 查看权限群组列表

🇨🇳 中文命令：
/小红书 状态 - 查看转发服务状态
/启用小红书 - 启用转发功能
/禁用小红书 - 禁用转发功能
/小红书配置 - 查看当前配置
/小红书统计 - 查看转发统计信息
/测试小红书 - 测试转发功能

💡 使用示例：
/xiaohongshu status
/xhs 源群组 添加 48326155652@chatroom
/xhs 统计

⚠️ 注意事项：
- 小红书转发功能会自动检测指定源群组中的小红书链接消息
- 检测到小红书消息后会自动转发到配置的目标群组
- 支持频率限制，防止消息轰炸
- 只有在权限白名单中的群组才能使用此功能
- 配置更改需要重启服务才能生效
`;
          break;

        case 'browser':
        case 'browserservice':
        case '浏览器服务':
        case '浏览器监控':
          helpText = `
🌐 浏览器服务健康监控命令帮助:

📋 基本命令：
/browser status (或 /浏览器服务 状态) - 查看浏览器服务健康监控状态
/browser enable (或 /启用浏览器监控) - 启用浏览器服务健康监控
/browser disable (或 /禁用浏览器监控) - 禁用浏览器服务健康监控
/browser stats (或 /浏览器服务 统计) - 查看健康监控统计信息
/browser info (或 /浏览器服务 信息) - 查看详细配置信息

🔧 管理命令：
/browser restart (或 /浏览器服务 重启) - 重启健康监控器
/browser recover (或 /浏览器服务 恢复) - 手动触发服务恢复

🇨🇳 中文命令：
/浏览器服务 状态 - 查看监控状态
/启用浏览器监控 - 启用健康监控
/禁用浏览器监控 - 禁用健康监控
/浏览器服务 统计 - 查看统计信息
/浏览器服务 信息 - 查看配置信息
/浏览器服务 重启 - 重启监控器
/浏览器服务 恢复 - 手动恢复服务

💡 使用示例：
/browser status
/browser enable
/浏览器服务 统计

⚠️ 注意事项：
- 浏览器服务健康监控用于监控外部浏览器服务的可用性
- 启用后会定期检查服务状态并在异常时尝试自动恢复
- 不需要浏览器服务的机器人建议保持禁用状态以节省资源
- 配置更改会立即生效，无需重启机器人
`;
          break;

        case 'logs':
        case 'log':
        case '日志':
          helpText = `
📋 日志管理命令帮助:

📊 统计命令：
/logs stats (或 /logstats) - 查看日志文件统计信息
/logs list - 列出所有日志文件

🧹 清理命令：
/logs clean [类型] (或 /logclean) - 清理日志文件
  • all - 清理所有日志文件
  • old - 清理旧的error_log.txt文件
  • chunk - 清理分片下载器日志

📦 备份命令：
/logs backup - 备份当前日志文件

📝 使用说明：
- 系统会自动管理日志文件大小，防止占用过多磁盘空间
- 清理操作会创建备份，确保数据安全
- 建议定期使用 /logs stats 查看日志状态
- 如果磁盘空间不足，可以使用 /logs clean old 清理大文件

💡 使用示例：
/logs stats - 查看当前日志状态
/logs clean old - 清理旧的大型日志文件
/logstats - 快速查看日志统计
/logclean - 快速清理旧日志
`;
          break;

        default:
          helpText = `未找到命令 "${specificCommand}" 的帮助信息，请使用 /help 查看所有可用命令。`;
          break;
      }

      await this.service.sendReply(msg, helpText);
      return;
    }

    const helpText = `
管理员命令列表:

基础命令:
/help (或 /帮助, /h) - 显示此帮助信息
/status (或 /状态, /s) - 显示机器人状态
/restart (或 /重启, /r) - 重启机器人
/reload (或 /重载) - 重新加载配置
/clear (或 /清理, /c) - 清理临时文件和缓存
/test (或 /测试) [消息] - 测试AI回复
/convert (或 /转换) [run|status|files] - 聊天记录转换管理

用户和群组管理:
/blacklist (或 /黑名单, /bl) [list|add|remove] [wxid] - 管理AI黑名单
/group (或 /群组, /g) [list|enable|disable] [groupId] - 管理群组AI功能
/toggle (或 /开关, /t) [feature] [on|off] - 切换功能开关
/broadcast (或 /广播, /bc) [消息] - 向所有管理员广播消息

功能管理:
/image (或 /图片, /img) [list|allow|deny] [user|group] [wxid] - 管理图片下载权限
/record (或 /记录, /rec) [list|add|remove] [wxid] - 管理聊天记录目标列表
/stats (或 /统计, /st) - 显示统计信息

监控命令:
/monitor (或 /监控, /m) [status|start|stop|list] - 管理监控服务
/github (或 /gh) [status|start|stop|add|delete|list] [repo] - 管理GitHub仓库监控
/dianping (或 /dp) [status|start|stop|add|delete|list] [shop] - 管理大众点评监控
/douyin (或 /dy) [status|start|stop|account] - 管理抖音线索监控

红包功能:
/redpacket (或 /红包, /rp) [status|enable|disable|stats|config|permission] - 管理红包抢取功能

艾特功能:
/mention (或 /艾特) [status|enable|disable|group|user|cache|test] - 管理群聊艾特功能

小红书转发:
/xiaohongshu (或 /小红书, /xhs) [status|enable|disable|config|stats|test] - 管理小红书消息转发功能

浏览器服务监控:
/browser (或 /浏览器服务) [status|enable|disable|stats|info|restart|recover] - 管理浏览器服务健康监控功能

日志管理:
/logs (或 /日志) [stats|clean|list|backup] - 管理系统日志文件
/logstats - 快速查看日志统计信息
/logclean - 快速清理旧日志文件

可以使用 "/命令 帮助" 或 "/help 命令" 获取特定命令的详细帮助。
例如: "/logs 帮助" 或 "/help logs"

其他功能:
/下载 [链接] - 下载并发送视频/图片内容
/toggle video_download [on|off] - 开启/关闭视频下载功能
`;

    await this.service.sendReply(msg, helpText);
  }

  /**
   * 处理状态命令
   * @param {Object} msg - 消息对象
   * @param {Array} args - 命令参数
   */
  async handleStatusCommand(msg, args) {
    try {
      // 获取基本状态信息
      const uptime = this.service.getUptime();
      const memoryUsage = this.service.getMemoryUsage();

      // 获取功能状态
      const featureStates = this.permissionsService.getFeatureStates();
      const featureStatusText = Object.entries(featureStates)
        .map(([key, value]) => `${key}: ${value ? '✅' : '❌'}`)
        .join('\n');

      // 构造状态消息
      const statusText = `
机器人状态:

运行时间: ${uptime}
处理命令: ${this.stats.handledCommands}
内存使用: ${memoryUsage}

功能状态:
${featureStatusText}

配置状态:
- 管理员数: ${this.config.admin?.admins?.length || 0}
- AI黑名单: ${this.config.whitelist?.users?.aiBlacklist?.length || 0}人
- AI群聊群组: ${this.config.whitelist?.groups?.aiEnabled?.length || 0}个
- AI禁用群组: ${this.config.whitelist?.groups?.aiDisabled?.length || 0}个
`;

      await this.service.sendReply(msg, statusText);
    } catch (error) {
      logger.error(`获取状态信息失败: ${error.message}`);
      await this.service.sendReply(msg, `获取状态信息失败: ${error.message}`);
    }
  }

  /**
   * 处理重启命令
   * @param {Object} msg - 消息对象
   * @param {Array} args - 命令参数
   */
  async handleRestartCommand(msg, args) {
    // 获取触发重启的管理员ID
    const fromUser = msg.FromUserName || msg.from_user_name || 'unknown';

    // 创建重启标记文件，包含重启相关信息
    try {
      const restartInfo = {
        timestamp: Date.now(),
        triggeredBy: fromUser,
        shouldNotify: true,
        restartMethod: 'command'
      };

      // 将重启信息写入文件
      const fs = require('fs');
      const path = require('path');
      const restartFlagPath = path.join(__dirname, '../../.restart_flag');

      fs.writeFileSync(restartFlagPath, JSON.stringify(restartInfo, null, 2));
      console.log(`已创建重启标记文件: ${restartFlagPath}`);

      await this.service.sendReply(msg, '🔄 正在重启机器人...\n✅ 将使用内存优化模式重新启动（--expose-gc）');

      // 记录重启信息
      console.log(`[重启命令] 管理员 ${fromUser} 触发重启`);
      console.log(`[重启命令] 当前启动参数: ${process.argv.join(' ')}`);
      console.log(`[重启命令] 垃圾回收可用: ${global.gc ? '是' : '否'}`);

    } catch (error) {
      console.error(`创建重启标记文件失败: ${error.message}`);
      await this.service.sendReply(msg, `重启准备出错: ${error.message}`);
      return;
    }

    // {{ AURA-X: Modify - 智能检测启动脚本，支持Supervisor环境. Approval: 寸止(ID:1751501600). }}
    // 尝试使用spawn重新启动进程，确保使用--expose-gc参数
    try {
      const { spawn } = require('child_process');
      const path = require('path');

      // 智能检测当前启动的脚本
      const currentScript = process.argv[1];
      const scriptName = path.basename(currentScript);

      let targetScript;
      let nodeArgs = ['--expose-gc'];

      // 根据当前运行的脚本决定重启目标
      if (scriptName === 'start.js') {
        targetScript = path.join(__dirname, '../../start.js');
        console.log(`[重启命令] 检测到通过 start.js 启动，将重启 start.js`);
      } else if (scriptName === 'start-optimized.js') {
        targetScript = path.join(__dirname, '../../start-optimized.js');
        console.log(`[重启命令] 检测到通过 start-optimized.js 启动，将重启 start-optimized.js`);
      } else {
        // 默认使用 bot.js
        targetScript = path.join(__dirname, '../../bot.js');
        console.log(`[重启命令] 默认重启 bot.js`);
      }

      console.log(`[重启命令] 准备重新启动: node ${nodeArgs.join(' ')} ${targetScript}`);

      // 延迟启动新进程，让当前消息有时间发送
      setTimeout(() => {
        // 启动新进程
        const child = spawn('node', [...nodeArgs, targetScript], {
          detached: true,
          stdio: 'inherit',
          cwd: path.join(__dirname, '../..')
        });

        // 分离子进程，让它独立运行
        child.unref();

        console.log(`[重启命令] 新进程已启动，PID: ${child.pid}`);
        console.log(`[重启命令] 当前进程即将退出`);

        // 退出当前进程
        process.exit(0);
      }, 1500);

    } catch (error) {
      console.error(`重启进程失败: ${error.message}`);
      await this.service.sendReply(msg, `❌ 重启失败: ${error.message}\n请手动重启机器人`);

      // 如果spawn失败，回退到简单的退出方式
      setTimeout(() => {
        process.exit(0);
      }, 1000);
    }
  }

  /**
   * 处理重新加载配置命令
   * @param {Object} msg - 消息对象
   * @param {Array} args - 命令参数
   */
  async handleReloadCommand(msg, args) {
    try {
      logger.info('[配置重载] 开始执行配置重载命令');
      await this.service.sendReply(msg, '⏳ 正在重新加载配置...\n请稍候，这可能需要几秒钟时间');

      // 添加延迟，避免消息发送过快
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 记录初始状态以便报告变化
      logger.info('[配置重载] 记录初始配置状态');
      const initialConfig = {
        adminCount: this.config.admin?.admins?.length || 0,
        blacklistCount: this.config.whitelist?.users?.aiBlacklist?.length || 0,
        enabledGroupsCount: this.config.whitelist?.groups?.aiEnabled?.length || 0,
        disabledGroupsCount: this.config.whitelist?.groups?.aiDisabled?.length || 0,
        imageDownloadGroupsCount: this.config.whitelist?.groups?.imageDownload?.length || 0,
        qrScanGroupsCount: this.config.whitelist?.groups?.qrScan?.length || 0
      };
      logger.info('[配置重载] 初始配置状态:', initialConfig);

      // 清除所有require缓存
      logger.info('[配置重载] 开始清除require缓存');
      let clearedModules = 0;
      Object.keys(require.cache).forEach(key => {
        if (key.includes('config/')) {
          delete require.cache[key];
          clearedModules++;
        }
      });
      logger.info(`[配置重载] 清除了 ${clearedModules} 个配置模块缓存`);

      // 重新加载配置
      logger.info('[配置重载] 重新加载配置文件');
      const newConfig = require('../../config/bot-config');
      const aiConfig = require('../../config/ai-config');

      // 更新配置
      logger.info('[配置重载] 更新当前配置对象');
      Object.assign(this.config, newConfig);

      // 清理权限缓存并让权限服务重新加载配置
      logger.info('[配置重载] 开始重新加载权限服务配置');
      let permissionsReloaded = false;
      if (this.permissionsService) {
        try {
          // 使用reloadConfig方法重新加载所有配置和缓存
          const reloaded = this.permissionsService.reloadConfig(this.config);
          permissionsReloaded = reloaded;
          if (reloaded) {
            logger.info('[配置重载] 权限服务配置已重新加载，包括运行时配置文件和权限缓存');
          } else {
            logger.warn('[配置重载] 权限服务配置重新加载可能不完整');
          }
        } catch (error) {
          logger.error(`[配置重载] 权限服务重载失败: ${error.message}`);
          permissionsReloaded = false;
        }
      } else {
        logger.warn('[配置重载] 未找到权限服务，无法重新加载权限配置');
      }

      // 计算配置变化
      logger.info('[配置重载] 计算新配置状态');
      let newConfigState;
      try {
        newConfigState = {
          adminCount: this.config.admin?.admins?.length || 0,
          blacklistCount: this.config.whitelist?.users?.aiBlacklist?.length || 0,
          enabledGroupsCount: this.config.whitelist?.groups?.aiEnabled?.length || 0,
          disabledGroupsCount: this.config.whitelist?.groups?.aiDisabled?.length || 0,
          imageDownloadGroupsCount: this.config.whitelist?.groups?.imageDownload?.length || 0,
          qrScanGroupsCount: this.config.whitelist?.groups?.qrScan?.length || 0
        };
        logger.info('[配置重载] 新配置状态:', newConfigState);
      } catch (error) {
        logger.error(`[配置重载] 计算新配置状态失败: ${error.message}`);
        throw new Error(`计算配置状态失败: ${error.message}`);
      }

      // 构造分段回复消息
      logger.info('[配置重载] 构造分段回复消息');

      // 第一段：基本状态
      const basicMsg = `✅ 配置重载完成！\n\n📊 重载统计:\n- 清除缓存: ${clearedModules}个模块\n- 权限服务: ${permissionsReloaded ? '✅成功' : '❌未完成'}`;

      // 第二段：配置状态
      const configMsg = `📋 当前配置状态:\n- 👥管理员: ${newConfigState.adminCount}人\n- 🚫黑名单: ${newConfigState.blacklistCount}人\n- 🤖AI启用群: ${newConfigState.enabledGroupsCount}个\n- ⛔AI禁用群: ${newConfigState.disabledGroupsCount}个\n- 📷图片下载群: ${newConfigState.imageDownloadGroupsCount}个\n- 📱扫码群: ${newConfigState.qrScanGroupsCount}个`;

      // 第三段：变化检测
      const changesDetected = Object.keys(initialConfig).some(key => initialConfig[key] !== newConfigState[key]);
      let changesMsg = '';
      if (changesDetected) {
        changesMsg = '🔄 检测到配置变化:\n';
        for (const [key, value] of Object.entries(initialConfig)) {
          if (value !== newConfigState[key]) {
            const friendlyNames = {
              adminCount: '👥管理员',
              blacklistCount: '🚫黑名单',
              enabledGroupsCount: '🤖AI启用群',
              disabledGroupsCount: '⛔AI禁用群',
              imageDownloadGroupsCount: '📷图片下载群',
              qrScanGroupsCount: '📱扫码群'
            };
            changesMsg += `- ${friendlyNames[key]}: ${value}→${newConfigState[key]}\n`;
          }
        }
      } else {
        changesMsg = '📝 未检测到配置变化\n如修改了配置文件但未生效，请检查格式是否正确';
      }

      // 分段发送消息，每段之间添加延迟
      logger.info('[配置重载] 开始分段发送回复消息');

      // 发送第一段
      await this.service.sendReply(msg, basicMsg);
      logger.info('[配置重载] 第一段消息已发送');
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2秒延迟

      // 发送第二段
      await this.service.sendReply(msg, configMsg);
      logger.info('[配置重载] 第二段消息已发送');
      await new Promise(resolve => setTimeout(resolve, 1500)); // 1.5秒延迟

      // 发送第三段（如果有变化）
      if (changesMsg.trim()) {
        await this.service.sendReply(msg, changesMsg);
        logger.info('[配置重载] 第三段消息已发送');
      }

      logger.info('[配置重载] 所有回复消息发送完成');
      logger.info('[配置重载] 配置重载命令执行完成');
    } catch (error) {
      logger.error(`[配置重载] 重新加载配置失败: ${error.message}`);
      logger.error(`[配置重载] 错误堆栈: ${error.stack}`);
      try {
        await this.service.sendReply(msg, `❌ 重新加载配置失败: ${error.message}\n请检查日志以获取更多信息。`);
      } catch (replyError) {
        logger.error(`[配置重载] 发送错误回复失败: ${replyError.message}`);
      }
    }
  }

  /**
   * 处理测试命令
   * @param {Object} msg - 消息对象
   * @param {Array} args - 命令参数
   */
  async handleTestCommand(msg, args) {
    if (args.length === 0) {
      await this.service.sendReply(msg, '用法: /test [测试消息]');
      return;
    }

    const testMessage = args.join(' ');
    await this.service.sendReply(msg, `测试消息: ${testMessage}\n\n正在处理测试请求...`);

    try {
      // 获取AI服务
      const aiService = this.serviceLoader?.getService('ai');
      if (!aiService) {
        await this.service.sendReply(msg, '无法获取AI服务，测试失败');
        return;
      }

      // 构造测试提示
      const prompt = `回复以下测试消息：\n"${testMessage}"`;

      // 获取AI回复
      const aiReply = await aiService.analyze(prompt);

      // 发送回复
      await this.service.sendReply(msg, `AI回复:\n\n${aiReply}`);
    } catch (error) {
      logger.error(`测试AI回复失败: ${error.message}`);
      await this.service.sendReply(msg, `测试AI回复失败: ${error.message}`);
    }
  }

  /**
   * 处理清理缓存命令
   * @param {Object} msg - 消息对象
   * @param {Array} args - 命令参数
   */
  async handleClearCommand(msg, args) {
    try {
      // 清理权限服务缓存
      if (this.permissionsService) {
        this.permissionsService.permissionCache.clear();
      }

      // 清理消息服务缓存
      if (this.serviceLoader && this.serviceLoader.getService('message')) {
        this.serviceLoader.getService('message').clearMessageCache();
      }

      // 记录全局垃圾回收
      if (global.gc) {
        global.gc();
      }

      await this.service.sendReply(msg, '缓存清理完成');
    } catch (error) {
      logger.error(`清理缓存失败: ${error.message}`);
      await this.service.sendReply(msg, `清理缓存失败: ${error.message}`);
    }
  }

  /**
   * 处理统计命令
   * @param {Object} msg - 消息对象
   * @param {Array} args - 命令参数
   */
  async handleStatsCommand(msg, args) {
    try {
      let statsText = '机器人统计信息:\n\n';

      // 基本统计
      statsText += `启动时间: ${new Date(this.stats.startTime).toLocaleString()}\n`;
      statsText += `运行时间: ${this.service.getUptime()}\n`;
      statsText += `处理命令: ${this.stats.handledCommands}次\n\n`;

      // 获取其他服务的统计信息
      if (this.serviceLoader) {
        const services = this.serviceLoader.getAllServices();

        // 消息服务统计
        if (services.message) {
          const messageStats = services.message.getStats?.() || {};
          statsText += '消息处理统计:\n';
          statsText += `- 处理消息: ${messageStats.processedMessages || 0}条\n`;
          statsText += `- AI回复: ${messageStats.aiResponses || 0}条\n`;
          statsText += `- 错误数: ${messageStats.errors || 0}次\n\n`;
        }

        // AI服务统计
        if (services.ai) {
          const aiStats = services.ai.getStats?.() || {};
          statsText += 'AI服务统计:\n';
          statsText += `- 请求总数: ${aiStats.requestCount || 0}次\n`;
          statsText += `- 成功请求: ${aiStats.successCount || 0}次\n`;
          statsText += `- 失败请求: ${aiStats.failureCount || 0}次\n\n`;
        }
      }

      await this.service.sendReply(msg, statsText);
    } catch (error) {
      logger.error(`获取统计信息失败: ${error.message}`);
      await this.service.sendReply(msg, `获取统计信息失败: ${error.message}`);
    }
  }

  /**
   * 处理聊天记录转换命令
   * @param {Object} msg - 消息对象
   * @param {Array} args - 命令参数
   */
  async handleConvertCommand(msg, args) {
    try {
      // 检查子命令
      const subCommand = args[0]?.toLowerCase() || 'help';

      switch (subCommand) {
        case 'help':
        case '帮助':
          const helpText = `
📄 聊天记录转换命令帮助:

🔄 转换命令:
/convert run (或 /convert 执行) - 手动执行聊天记录转换
/convert status (或 /convert 状态) - 查看转换服务状态
/convert files (或 /convert 文件) - 查看今日CSV文件

🇨🇳 中文命令:
/转换 执行 - 手动执行聊天记录转换
/转换 状态 - 查看转换服务状态
/转换 文件 - 查看今日CSV文件

💡 说明:
- 转换功能将CSV格式的聊天记录转换为TXT格式
- 默认每天18:20自动执行转换
- 可以使用此命令手动触发转换
- 只转换当天的CSV文件`;

          await this.service.sendReply(msg, helpText);
          break;

        case 'run':
        case '执行':
          await this.service.sendReply(msg, '🔄 开始执行聊天记录转换...');

          // 获取聊天记录转换服务
          const converterService = this.serviceLoader?.getService('chatRecordConverter');
          if (!converterService) {
            await this.service.sendReply(msg, '❌ 聊天记录转换服务未启用或未初始化');
            return;
          }

          try {
            const result = await converterService.executeDailyConversion();

            // {{ EMERGENCY-FIX: 添加调试日志 }}
            console.log('[转换命令] 转换任务完成，结果:', JSON.stringify(result, null, 2));

            // {{ EMERGENCY-FIX: 简化消息内容，避免被过滤 }}
            let resultMessage = `转换完成！\n`;
            resultMessage += `结果: ${result.message}\n`;
            resultMessage += `处理文件数: ${result.processedFiles || 0}\n`;
            resultMessage += `成功: ${result.successCount || 0}, 失败: ${result.errorCount || 0}\n`;

            if (result.convertedFiles && result.convertedFiles.length > 0) {
              resultMessage += `\n转换后的文件:\n`;
              result.convertedFiles.forEach((file, index) => {
                const fileName = require('path').basename(file);
                resultMessage += `${index + 1}. ${fileName}\n`;
              });
            }

            if (result.error) {
              resultMessage += `\n错误: ${result.error}`;
            }

            // {{ EMERGENCY-FIX: 添加发送前的调试日志 }}
            console.log('[转换命令] 准备发送结果消息:', resultMessage);
            console.log('[转换命令] 消息对象:', {
              FromUserName: msg.FromUserName,
              from_user_name: msg.from_user_name,
              hasReply: typeof msg.reply === 'function',
              hasService: !!this.service,
              hasSendReply: typeof this.service?.sendReply === 'function'
            });

            // {{ EMERGENCY-FIX: 添加延迟，避免发送过快 }}
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒

            const sendResult = await this.service.sendReply(msg, resultMessage);
            console.log('[转换命令] 发送结果:', sendResult);

            // {{ EMERGENCY-FIX: 再次确认发送 }}
            if (sendResult) {
              console.log('[转换命令] ✅ 结果消息发送成功');
            } else {
              console.log('[转换命令] ❌ 结果消息发送失败，尝试备用方法');
              // 备用发送方法
              try {
                await this.service.messageSender.sendText(msg.FromUserName, `转换完成: ${result.successCount}个文件`);
                console.log('[转换命令] ✅ 备用方法发送成功');
              } catch (backupError) {
                console.log('[转换命令] ❌ 备用方法也失败:', backupError.message);
              }
            }
          } catch (error) {
            logger.error(`执行聊天记录转换失败: ${error.message}`);
            console.log('[转换命令] 转换失败，发送错误消息');
            await this.service.sendReply(msg, `❌ 转换执行失败: ${error.message}`);
          }
          break;

        case 'status':
        case '状态':
          // 获取聊天记录转换服务
          const statusService = this.serviceLoader?.getService('chatRecordConverter');
          if (!statusService) {
            await this.service.sendReply(msg, '❌ 聊天记录转换服务未启用或未初始化');
            return;
          }

          const status = statusService.getStatus();
          let statusMessage = `📊 **聊天记录转换服务状态**\n\n`;
          statusMessage += `🏷️ 服务名称: ${status.name}\n`;
          statusMessage += `📂 输入目录: ${status.chatRecordsDir}\n`;
          statusMessage += `📁 输出目录: ${status.outputDir}\n`;
          statusMessage += `✅ 服务就绪: ${status.isReady ? '是' : '否'}\n`;

          await this.service.sendReply(msg, statusMessage);
          break;

        case 'files':
        case '文件':
          // 获取聊天记录转换服务
          const filesService = this.serviceLoader?.getService('chatRecordConverter');
          if (!filesService) {
            await this.service.sendReply(msg, '❌ 聊天记录转换服务未启用或未初始化');
            return;
          }

          try {
            const csvFiles = await filesService.findTodaysCsvFiles();

            let filesMessage = `📁 **今日CSV文件列表**\n\n`;
            filesMessage += `📊 找到 ${csvFiles.length} 个文件\n\n`;

            if (csvFiles.length === 0) {
              filesMessage += `ℹ️ 没有找到今日的CSV文件`;
            } else {
              csvFiles.forEach((file, index) => {
                const fileName = require('path').basename(file);
                filesMessage += `${index + 1}. ${fileName}\n`;
              });
            }

            await this.service.sendReply(msg, filesMessage);
          } catch (error) {
            logger.error(`查找CSV文件失败: ${error.message}`);
            await this.service.sendReply(msg, `❌ 查找文件失败: ${error.message}`);
          }
          break;

        default:
          await this.service.sendReply(msg, `❌ 未知子命令: ${subCommand}\n可用选项: run, status, files, help\n使用 "/convert help" 查看详细帮助`);
      }
    } catch (error) {
      logger.error(`处理转换命令失败: ${error.message}`);
      await this.service.sendReply(msg, `❌ 命令执行失败: ${error.message}`);
    }
  }
}

module.exports = BasicCommands;